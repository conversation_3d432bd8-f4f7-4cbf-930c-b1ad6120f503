import { Component, useEffect, useState, useRef } from 'react'
import Taro from '@tarojs/taro'
import { View, Text, Input, Image, Picker } from '@tarojs/components'
import MyModal from '@/components/back/modal';
import Bg from '@/components/tool/bg'
import { AtIcon } from "taro-ui"
import tools from '@/utils/tools'
import "./index.scss"

function Index (props) { 
  const params =  tools.getQuery()
  const who = Boolean(params.data.rel) ? parseInt(params.data.rel, 10) : 0
  const good_key = Boolean(params.data.key) ? params.data.key : null
  console.warn("good_key -------------", good_key, who)
  const prve = (who === 0) ? '' : tools.relation[who].label
  const [ myphoto, setMyphoto ] = useState(null)
  const [ isCheck, setIsCheck ]= useState(false)
  const [ isClose, setIsClose ]= useState(false)
  const [ isOk, setIsOk ]= useState(false)
  const [ cardType, setCardType ]= useState(0)
  const [ cardVal, setCardVal] = useState('')
  const [ cut, setCut] = useState(false)
  const [ info, setInfo ] = useState({
    user_img: "",
    user_img_check: "请上传持卡人照片",
    real_name : null,
    real_name_check: "请填写姓名",
    id_card_no: "fake",
    id_card_no_check: "请填写证件号码",
    good_key: good_key,
    good_key_check: "请填写卡片激活码",
  })
  const imageCropper = useRef(null);
  const id_card_list_str  = ['身份证','护照', '港澳居民来往内地通行证', '台湾省居民来往大陆通行证', '军官证' ]
  const id_card_list = [
    {
      len: 10,
      title: '身份证',
      val: 0,
      type: 'card'
    },
    {
      len: 5,
      title: '护照',
      val: 1,
      type: 'hz'
    },
    {
      len: 5,
      title: '港澳居民来往内地通行证',
      val: 2,
      type: 'gh'
    },
    {
      len: 5,
      val: 3,
      title: '台湾省居民来往大陆通行证',
      type: 'tai'
    },
    {
      len: 5,
      val: 4,
      title: '军官证',
      type: 'jun'
    },
  ]
  const uploadOk = data => {
    setMyphoto(data[0])
    info.user_img = data[0]
    setInfo(info)
  }
  const checkAll = (val, len, title, name, type = "null") =>{
    if(val.detail.value.length > 0 ) {
      const check = tools.checkData(val.detail.value, len, title, type)
      if (check.check !== true) {
        Taro.showToast({
          title: check.check,
          icon: 'none',
          mask: true,
          duration: 2000
        })
        info[`${name}_check`] = check.check
        info[name] = null
      } else {
        info[name] = check.data
      }
      setInfo(info)
    }
  }
  const reData = () => {
    const tips =[]
    Object.keys(info).forEach(val => {
          if (!val.includes("_check")) {
            if (info[val] === null) {
              tips.push(info[`${val}_check`])
            }
          }
        })
    if (tips.length > 0) {
      setIsCheck(true)
    } else {
      setIsOk(true)
    }
  }
  const bindCard = async () => {
    setIsOk(false)
    if (isClose) {
      return false
    }
    setIsClose(true)
    Taro.showLoading({
      mask: true,
      title: '提交中'
    });
    const userYear = (cardType === 0) ? tools.checkData(info.id_card_no, 17, "身份证", "card") : {
      year: 1984,
      sex: 1
    }
    const data = {
      good_key: info.good_key.toUpperCase(),
      info : {
        id_card_no : info.id_card_no,
        id_card_type : cardType,
        real_name : info.real_name,
        user_tel : info.user_tel,
        user_img : info.user_img,
        user_year: userYear.year,
        user_gender: userYear.sex,
        user_rel: who
      }
    }
    const oper = await tools.Api.bindCard(data)
    if (oper.code === 200) {
      Taro.redirectTo({
        url: "/pages/tips/index?type=100"
      })
    } else {
      setIsClose(false)
    }
  }
  const isAuthorize = async (authorize) => {
		let authSetting = await Taro.getSetting();
		if (!authSetting[authorize]) {
			return new Promise((resolve, reject) => {
				Taro.authorize({
					scope: "scope.writePhotosAlbum",
					success() {
						resolve("yes");
					},
					fail() {
						reject();
					},
				});
			});
		}
		return Promise.resolve("yes");
	}
  const handleOk = async() => {
		let isAuthSetting = await isAuthorize("scope.writePhotosAlbum");
		if (isAuthSetting === "yes") {
			let result = await this.imageCropper.current._getImg();
			console.log(result, "result");
			// this.setState({
			// 	previewUrl: result.tempFilePath,
			// });
			// Taro.saveImageToPhotosAlbum({
			// 	filePath: result.tempFilePath,
			// 	success: function(res) {
			// 		Taro.showToast({
			// 			title: "保存图片成功",
			// 		});
			// 	},
			// 	fail() {
			// 		Taro.showToast({
			// 			title: "保存图片失败",
			// 			icon: "fail",
			// 		});
			// 	},
			// });
		} else {
			Taro.showToast({
				title: "请在右上角授权",
			});
		}
	}
  return (
    <View>
      <Bg />
      <View className='addPage'>
         {/* <View className="">
            <View className="image" onClick={() => {
              wx.chooseImage({
                count: 1,
                sizeType: ['compressed'],
                success : async res  => {
                  const tempFilePaths = res.tempFilePaths
                  setCut(tempFilePaths[0])
                  await tools.uploadImages(tempFilePaths, uploadOk)
                }
              })
            }}> 
                {(myphoto === null) ? <View>
                  <View style={{marginTop: '30px'}}><AtIcon value='share-2' size='24' color='#2196F3'></AtIcon></View>
                  <View style={{marginTop: '5px'}}>上传持卡人照片</View>
                </View> : <View>
                  <Image src={myphoto} mode="aspectFit" className="photo"/>
                </View>
                }
            </View>
          </View>*/}
          <View class="item" style={{marginTop: "18px"}}>
            <Text style={{width: "80px"}}>{prve}姓名</Text>
            <Input type='text' placeholder='请输入姓名' className="input" maxlength={10} onBlur={(val) => {
               checkAll(val, 2, "姓名", "real_name")
            }}/>
          </View>
          {/*<View style={{marginTop: "18px"}}>
            <Text>{prve}证件类型</Text>
            <Picker mode='selector' range={id_card_list_str} onChange={val => {
              setCardVal('')
              setCardType(parseInt(val.detail.value, 10))
            }}>
              <View className="at-row input">
                <View className='at-col at-col-11'>{id_card_list[cardType].title}</View>
                <View className='at-col at-col-1' style={{textAlign: 'right'}}><AtIcon value='chevron-right' size='20' color='#2196f3'></AtIcon></View>
              </View>
            </Picker>
          </View>
          <View style={{marginTop: "18px"}}>
            <Text>{prve}证件号码</Text>
            <Input type='text' value={cardVal} placeholder={`请输入${id_card_list[cardType].title}号码`} className="input" maxlength={18} onBlur={(val) => {
              setCardVal(val.detail.value)
              checkAll(val, id_card_list[cardType].len, id_card_list[cardType].title, "id_card_no", id_card_list[cardType].type)
            }}/>
          </View>*/}
          <View class="item" style={{marginTop: "18px"}}>
            <Text style={{width: "80px"}}>卡激活码</Text>
            <Input type='text' placeholder='请输入卡激活码' className="input" maxlength={15} value={good_key} onBlur={(val) => {
                checkAll(val, 8, "卡激活码", "good_key")
            }}/>
          </View>
          <View className="card">
            <Image src={`${tools.ip}/card.jpg`} mode="widthFix" style={{width: "70%", marginBottom: "12px"}}/>
            <View style={{color: "#3b883e", fontSize: '25rpx'}}>实体卡右下角为激活码，电子票请在小程序我的订单中查看</View>
          </View>
      </View>
      <View className="jbtn" onClick={() => {
          reData()
      }}>提 交</View>
      <MyModal open={isCheck} title="提示" ok={{name: "重新填写", fun : () => {
        setIsCheck(false)
      }}}>
        <View style={{fontSize: "16px"}}>
          {Object.keys(info).map(val => {
            if (info[val] === null) {
              return <View style={{padding :'4px'}}><Text style={{color: "red"}}>X</Text> {info[`${val}_check`]}</View>
            }
          })}
        </View>
      </MyModal>
      <MyModal open={isOk} title="确认信息" ok={{name: "立即提交", fun : () => {
        bindCard()
      }}} close={{name: "重新填写", fun : () => {
        setIsOk(false)
      }}}>
        <View style={{fontSize: "14px", padding: "0 5px"}}>
          <View style={{paddingBottom: "5px"}}>真实姓名: {info.real_name}</View>
{/*          <View style={{paddingBottom: "5px"}}>证件类型: {id_card_list[cardType].title}</View>
          <View style={{paddingBottom: "5px"}}>证件号码: {info.id_card_no}</View>*/}
          <View style={{paddingBottom: "5px"}}>卡激活码: {info.good_key}</View>
        </View>
      </MyModal>     
    </View>
  )
}
export default Index;

/*
 * @Author: 高超
 * @Date: 2021-12-17 22:35:13
 * @LastEditTime: 2022-08-11 20:02:56
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/my/book.jsx
 * love jiajia
 */
import { Component,useState, useEffect, Fragment } from 'react'
import Taro from '@tarojs/taro';
import { View, Text, Button, Image } from '@tarojs/components'
import tools from '@/utils/tools'
import moment from "moment"
import './index.scss'

const Item = props => {
    const { data } = props
    const [show, setShow] = useState(true)
    const csscolor = data.enable ? {} : {color: '#ccc'}
    const weekDay = ["周日","周一","周二","周三","周四","周五","周六"];
    return (
        <Fragment>
        {show ? <View className='bookItem'>
        <View className='at-row cardNum'>
            <View className='at-col at-col-6' style={csscolor}>
            {data.card_name}
            </View>
            <View className='at-col at-col-5' style={{textAlign: 'right'}}>
                <Text style={csscolor}>No.{data.user_card_code}</Text>
            </View>
        </View>
        <View>
        <View className='at-row' style={{ padding: '0 14px', width: '90%', marginTop: '14px'}}>
            <View className='at-col at-col-3' style={{textAlign: 'center'}}>
            <View className='week'  style={csscolor}>{weekDay[moment(data.book_time_unix * 1000).weekday()]}</View>
            <View className='day'  style={csscolor}>{data.book_time_show}</View>
            </View>
            <View className='at-col at-col-2' style={{margin: '0 15px'}}>
                <View className='linetext'  style={csscolor}>游玩</View>
                <Image src={`${tools.picUrl}/bind/${data.enable ? 'right' : 'right_cc'}.png`} className='line' mode='widthFix' />
            </View>
            <View className='at-col at-col-8 brand'>
            <View  style={csscolor}>{data.brand_name}</View>
            <View className='address'>
            <Text style={csscolor}>{tools.getSamll(`${data.province}${data.city}${data.address}`, 10)}</Text></View>
            </View>
        </View>
        <View className='at-row' style={{ padding: '0 14px',width: '90%', marginBottom: '14px', marginTop: '6px', color:'#8e8c8c'}}>
            <View className='at-col at-col-3' style={{textAlign: 'center'}}>
            <Text style={csscolor}>预约人:  {data.real_name}</Text>
            </View>
            <View className='at-col at-col-3' style={{textAlign: 'left'}}></View>
            <View className='at-col at-col-6' style={{textAlign: 'left', paddingLeft: '5px'}}>
                <Text style={csscolor}>{data.add_time}</Text>
            </View>
        </View>
        {data.enable ? <View className='at-row oper'>
            <View className='at-col at-col-4' style={{textAlign: 'center', borderRight: '1px solid #e2e2e2'}} onClick={ () => {
                Taro.navigateTo({
                    url : `/pages/qr/index?code=${data.user_card_code}`
                })
            }}>
                扫码入园
            </View>
            <View className='at-col at-col-4' style={{textAlign: 'center', borderRight: '1px solid #e2e2e2'}} onClick={() => {
                Taro.openLocation({
                    latitude: parseFloat(data.latitude),
                    longitude: parseFloat(data.longitude),
                    scale: 15
                  })
            }}>
                位置导航
            </View>
            <View className='at-col at-col-4' style={{textAlign: 'center'}} onClick={() => {
                Taro.showModal({
                    title: `请确认`,
                    content: `取消此预约？`,
                    success:  async (res) => {
                      if (res.confirm) {
                        const oper = await tools.Api.removeBook({id : data.id})
                        if (oper.data === true) {
                            Taro.showToast({
                                title: '预约已取消',
                                icon: 'none',
                                duration: 2000
                              })
                            setShow(false)
                        }   
                        
                      }
                    }
                  })
            }}>
                取消预约
            </View>
        </View> : <View><View style={{height: '10px'}}></View></View>}
        
        </View>
        </View> : null}
        </Fragment>
        
    )
}
export default Item;
{"version": 3, "file": "pages/buy/ok.js", "sources": ["webpack:///./src/pages/buy/ok.jsx", "webpack:///./src/pages/buy/ok.jsx?0c0c"], "sourcesContent": ["import { useState, useEffect } from 'react'\r\nimport { View, Image } from '@tarojs/components'\r\nimport Taro from '@tarojs/taro'\r\nimport Bg from '@/components/tool/bg'\r\nimport tools from \"@/utils/tools\"\r\nimport \"./index.scss\"\r\n\r\nfunction Index (props) { \r\n  const [init, setInit] = useState([])\r\n  let good_key = \"\"\r\n  useEffect( async () => {\r\n    Taro.showLoading({\r\n      mask: true,\r\n      title: '读取中'\r\n    })\r\n    const myOrders = await tools.Api.myOrders()\r\n    Taro.hideLoading()\r\n    if (myOrders.code === 200) {\r\n      setInit(myOrders.data[myOrders.data.length - 1])\r\n    }\r\n  }, [])\r\n\r\n  return (\r\n    <View className='index'>\r\n        <Bg/>\r\n        <View style={{textAlign: 'center', marginTop: \"60px\", color: \"#333\", fontSize: \"18px\"}}>\r\n          <View><Image src={`${tools.picUrl}/bind/ok.png`} mode= \"widthFix\" style={{width : '20%'}}/></View>\r\n          <View style={{marginTop: \"10px\" , color: \"#848181\"}}>购买成功</View>\r\n          <View className=\"okbtn\" onClick={() => {\r\n            Taro.redirectTo({\r\n              url: '/pages/bind/index?key=' + init.good_key,\r\n            });\r\n          }}>立即绑定激活</View>\r\n          <View className=\"checkbtn\" onClick={() => {\r\n            Taro.redirectTo({\r\n              url: '/pages/my/order',\r\n            });\r\n          }}>查看激活码，暂不绑定</View>\r\n        </View>\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./ok.jsx\"\nvar config = {\"navigationBarTitleText\":\"年票购买成功\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/buy/ok', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAGA;AACA;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
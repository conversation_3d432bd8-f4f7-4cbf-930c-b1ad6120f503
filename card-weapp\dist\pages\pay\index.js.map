{"version": 3, "file": "pages/pay/index.js", "sources": ["webpack:///./src/pages/pay/index.js", "webpack:///./src/pages/pay/index.js?3d5c", "webpack:///./src/pages/pay/index.scss"], "sourcesContent": ["/*\r\n * @Author: 高超\r\n * @Date: 2022-07-21 10:41:16\r\n * @LastEditTime: 2022-07-22 14:14:30\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/pay/index.js\r\n * love jiajia\r\n */\r\nimport { useState, useEffect } from 'react'\r\nimport Taro from '@tarojs/taro';\r\nimport { View, Image, Text } from '@tarojs/components';\r\nimport tools from '@/utils/tools';\r\nimport './index.scss';\r\n\r\nfunction Pay(props) {\r\n    const { params: {id} } = Taro.useRouter();\r\n    useEffect( async () => {\r\n      Taro.showLoading({\r\n        title: '发起中',\r\n      })\r\n      const payData = await tools.Api.pay({code: id, openid: tools.getData('userInfo').code, appid: \"wx99dcb5f0415eb002\"});\r\n        Taro.hideLoading();\r\n        console.log(payData.code)\r\n        if (payData.code === 0) {\r\n          Taro.requestPayment({\r\n            timeStamp: payData.data.timeStamp,\r\n            nonceStr: payData.data.nonceStr,\r\n            package: payData.data.package,\r\n            signType: payData.data.signType,\r\n            paySign: payData.data.sign,\r\n            success: function () {\r\n              Taro.redirectTo({\r\n                url: '/pages/tips/index?type=pay100',\r\n              });\r\n            },\r\n            fail: function () {\r\n              Taro.redirectTo({\r\n                url: '/pages/tips/index?type=pay105',\r\n              });\r\n            }\r\n          })\r\n        } else {\r\n          Taro.redirectTo({\r\n            url: '/pages/tips/index?type=pay106',\r\n          });\r\n        }\r\n    }, [])\r\n\r\n    return (\r\n        <View style=\"text-align: center;padding-top: 100px;\">\r\n          <Image src={`https://test.qqyhmmwg.com/res/wbg/w.png`} className='pay_img' />\r\n          <View className='pay_memo'>支付中请稍等</View>\r\n          <View className='pay_txt'>支付跳转中, 支付完成, 页面自动跳转</View>\r\n        </View>\r\n    )\r\n}\r\n\r\nexport default Pay", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.js\"\nvar config = {\"navigationBarTitleText\":\"请支付\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/pay/index', {root:{cn:[]}}, config || {}))\n\n", "// extracted by mini-css-extract-plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAGA;AAEA;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA;;;;A", "sourceRoot": ""}
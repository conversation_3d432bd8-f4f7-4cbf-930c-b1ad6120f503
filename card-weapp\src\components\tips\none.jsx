import { View, Text, Image } from '@tarojs/components'
import "./index.scss"

const None = props => {
  return (
      <View>
      {(props.loaded) ? <View> 
        <View className='clear_v'>
            <Image src={`https://test.qqyhmmwg.com/res/wbg/user/clear.png`} className='clear' mode='widthFix'/>
        </View>
        <View style={{textAlign: 'center', color: '#c1c1c1'}}>
            <Text>哇～这里空空如也～</Text>
        </View>
    </View> : null}
    </View>
      
  )
}
export default None 
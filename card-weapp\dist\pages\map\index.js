(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/map/index"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/map/index.jsx":
/*!*****************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/map/index.jsx ***!
  \*****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _components_memu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/memu */ "./src/components/memu/index.jsx");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./index.scss */ "./src/pages/map/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__);



/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-12 19:19:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/map/index.jsx
 */









var MapView = function MapView() {
  var login = Object(react_redux__WEBPACK_IMPORTED_MODULE_5__[/* useSelector */ "c"])(function (state) {
    return state.login;
  });
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])([]),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    mapData = _useState2[0],
    setMapData = _useState2[1];
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var data;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (!login) {
            _context.next = 5;
            break;
          }
          _context.next = 3;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].Api.brandMap();
        case 3:
          data = _context.sent;
          setMapData(data.data || []);
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [login]);
  // const normalCallout = {
  //     id: 1,
  //     latitude: 23.098994,
  //     longitude: 113.32252,
  //     iconPath:`${tools.picUrl}/memu/m2.png`,
  //     height: 40,
  //     width: 40,
  //     callout: {
  //         content: '北京故宫',
  //         color: '#ff0000',
  //         fontSize: 14,
  //         borderWidth: 2,
  //         borderRadius: 10,
  //         borderColor: 'blue',
  //         bgColor: '#fff',
  //         padding: 5,
  //         display: 'ALWAYS',
  //         textAlign: 'center',
  //       }
  //   }

  //   const customCallout1 = {
  //     id: 2,
  //     latitude: 23.097994,
  //     longitude: 113.32352,
  //     iconPath:`${tools.picUrl}/memu/m2.png`,
  //     height: 40,
  //     width: 40,
  //     customCallout: {
  //       anchorY: 0,
  //       anchorX: 0,
  //       display: 'ALWAYS',
  //     },
  //   }

  //   const customCallout2 = {
  //     id: 3,
  //     latitude: 23.096994,
  //     longitude: 113.32452,
  //     iconPath:`${tools.picUrl}/memu/m2.png`,
  //     height: 40,
  //     width: 40,
  //     customCallout: {
  //       anchorY: 0,
  //       anchorX: 0,
  //       display: 'ALWAYS',
  //     },
  //   }

  //   const customCallout3 = {
  //     id: 4,
  //     latitude: 23.095994,
  //     longitude: 113.32552,
  //     iconPath:`${tools.picUrl}/memu/m2.png`,
  //     height: 40,
  //     width: 40,
  //     customCallout: {
  //       anchorY: 0,
  //       anchorX: 0,
  //       display: 'ALWAYS',
  //     },
  //   }

  //   const customMarkers = [
  //     customCallout1,
  //     customCallout2,
  //     customCallout3,
  //   ]

  //   const mapMarkers = [
  //     normalCallout,
  //     ...customMarkers
  //   ]

  var goBrand = function goBrand(id) {
    var code = null;
    mapData.forEach(function (val) {
      if (val.id === id) {
        code = val.code;
      }
    });
    if (code !== null) {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
        url: "/pages/brand/index?v=".concat(code)
      });
    }
  };
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Map"], {
    setting: {},
    scale: 8,
    "show-compass": true,
    "enable-overlooking": true,
    markers: mapData,
    latitude: 39.925218,
    longitude: 116.404425,
    style: {
      height: '100vh',
      width: '100vw'
    },
    onCalloutTap: function onCalloutTap(v) {
      console.warn("v -------- v", v);
      goBrand(v.markerId);
    },
    onMarkerTap: function onMarkerTap(v) {
      console.warn("v -------- v", v.markerId);
      goBrand(v.markerId);
    },
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["CoverView"], {
      slot: "callout",
      children: mapData.map(function (item) {
        return /*#__PURE__*/(
          /** 自定义样式的 callout */
          Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["CoverView"], {
            "marker-id": item.code,
            height: 10,
            iconPath: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].picUrl, "/memu/love.png")
          }, item.code)
        );
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_components_memu__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"], {
      now: 1
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (MapView);

/***/ }),

/***/ "./src/pages/map/index.jsx":
/*!*********************************!*\
  !*** ./src/pages/map/index.jsx ***!
  \*********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./index.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/map/index.jsx");


var config = {"navigationBarTitleText":"景区地图"};


var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/map/index', {root:{cn:[]}}, config || {}))



/***/ }),

/***/ "./src/pages/map/index.scss":
/*!**********************************!*\
  !*** ./src/pages/map/index.scss ***!
  \**********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ })

},[["./src/pages/map/index.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=index.js.map
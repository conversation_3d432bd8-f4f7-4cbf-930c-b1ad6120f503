(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/pay/index"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/pay/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/pay/index.js ***!
  \****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./index.scss */ "./src/pages/pay/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__);


/*
 * @Author: 高超
 * @Date: 2022-07-21 10:41:16
 * @LastEditTime: 2022-07-22 14:14:30
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/pay/index.js
 * love jiajia
 */







function Pay(props) {
  var _Taro$useRouter = _tarojs_taro__WEBPACK_IMPORTED_MODULE_3___default.a.useRouter(),
    id = _Taro$useRouter.params.id;
  Object(react__WEBPACK_IMPORTED_MODULE_2__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var payData;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_3___default.a.showLoading({
            title: '发起中'
          });
          _context.next = 3;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].Api.pay({
            code: id,
            openid: _utils_tools__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].getData('userInfo').code,
            appid: "wx99dcb5f0415eb002"
          });
        case 3:
          payData = _context.sent;
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_3___default.a.hideLoading();
          console.log(payData.code);
          if (payData.code === 0) {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_3___default.a.requestPayment({
              timeStamp: payData.data.timeStamp,
              nonceStr: payData.data.nonceStr,
              package: payData.data.package,
              signType: payData.data.signType,
              paySign: payData.data.sign,
              success: function success() {
                _tarojs_taro__WEBPACK_IMPORTED_MODULE_3___default.a.redirectTo({
                  url: '/pages/tips/index?type=pay100'
                });
              },
              fail: function fail() {
                _tarojs_taro__WEBPACK_IMPORTED_MODULE_3___default.a.redirectTo({
                  url: '/pages/tips/index?type=pay105'
                });
              }
            });
          } else {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_3___default.a.redirectTo({
              url: '/pages/tips/index?type=pay106'
            });
          }
        case 7:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), []);
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
    style: "text-align: center;padding-top: 100px;",
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["Image"], {
      src: "https://test.qqyhmmwg.com/res/wbg/w.png",
      className: "pay_img"
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
      className: "pay_memo",
      children: "\u652F\u4ED8\u4E2D\u8BF7\u7A0D\u7B49"
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
      className: "pay_txt",
      children: "\u652F\u4ED8\u8DF3\u8F6C\u4E2D, \u652F\u4ED8\u5B8C\u6210, \u9875\u9762\u81EA\u52A8\u8DF3\u8F6C"
    })]
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Pay);

/***/ }),

/***/ "./src/pages/pay/index.js":
/*!********************************!*\
  !*** ./src/pages/pay/index.js ***!
  \********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./index.js */ "./node_modules/babel-loader/lib/index.js!./src/pages/pay/index.js");


var config = {"navigationBarTitleText":"请支付"};


var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_index_js__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/pay/index', {root:{cn:[]}}, config || {}))



/***/ }),

/***/ "./src/pages/pay/index.scss":
/*!**********************************!*\
  !*** ./src/pages/pay/index.scss ***!
  \**********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ })

},[["./src/pages/pay/index.js","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=index.js.map
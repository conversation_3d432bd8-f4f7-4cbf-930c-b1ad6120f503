(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/buy/index"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/buy/index.jsx":
/*!*****************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/buy/index.jsx ***!
  \*****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _components_tool_bg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/tool/bg */ "./src/components/tool/bg.jsx");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./index.scss */ "./src/pages/buy/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _static_card_online_2024_jpg__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/static/card-online-2024.jpg */ "./src/static/card-online-2024.jpg");
/* harmony import */ var _static_card_online_2024_jpg__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_static_card_online_2024_jpg__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__);



/*
 * @Author: 高超
 * @Date: 2021-11-04 17:05:46
 * @LastEditTime: 2022-02-16 17:37:22
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/bind/index.jsx
 * love jiajia
 */










function Index(props) {
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(true),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    check = _useState2[0],
    setCheck = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState3, 2),
    tel = _useState4[0],
    setTel = _useState4[1];
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var userInfo;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          userInfo = _utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].getDataSafe("userInfo");
          if (userInfo !== null) {
            setTel(Boolean(userInfo.user_tel));
          } else {
            setTel(false);
          }
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [_utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].getDataSafe("userInfo")]);
  Object(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__["useShareAppMessage"])(function () {
    return {
      title: "购买北京风景名胜区年票",
      path: "/pages/buy/index",
      imageUrl: "?x-oss-process=image/resize,m_fill,h_400,w_400"
    };
  });
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
    className: "pageAdd",
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_components_tool_bg__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], {}), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      style: {
        textAlign: "center"
      },
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Image"], {
        src: 'https://cardall.oss-cn-beijing.aliyuncs.com/card_online.jpg',
        mode: "widthFix",
        style: {
          marginTop: "30px",
          borderRadius: '12px'
        }
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        className: "title",
        children: "\u5317\u4EAC\u98CE\u666F\u540D\u80DC\u5E74\u7968"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        className: "info",
        children: "\u8D2D\u4E70\u5E76\u5F00\u901A\u5E74\u7968\uFF0C\u5168\u5E74\u4FBF\u6377\u5165\u56ED"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Image"], {
        src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].picUrl, "/bind/link.jpg"),
        mode: "widthFix",
        style: {
          marginTop: "30px"
        }
      })]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      style: {
        marginLeft: "15px",
        marginTop: "15px"
      },
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        className: "at-row at-row--wrap",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-col at-col-1"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-col at-col-10 at-col--wrap",
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
            className: "txt",
            children: ["\u8D2D\u4E70\u5373\u8868\u793A\u540C\u610F", /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
              style: {
                color: "#03A9F4"
              },
              onClick: function onClick() {
                _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
                  url: '/pages/buy/txt'
                });
              },
              children: "\u300A\u7528\u6237\u6388\u6743\u534F\u8BAE\u300B"
            }), "\uFF0C\u5E76\u6388\u6743", /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
              style: {
                fontWeight: "bold"
              },
              children: "\u5317\u4EAC\u98CE\u666F\u540D\u80DC\u534F\u4F1A"
            }), "\u4F7F\u7528\u60A8\u7684\u59D3\u540D\u3001\u8BC1\u4EF6\u53F7\u3001\u624B\u673A\u53F7\u8FDB\u884C\u5B9E\u540D\u5F00\u5361\uFF0C\u4EE5\u4FBF\u4E3A\u60A8\u63D0\u4F9B\u66F4\u597D\u7684\u7535\u5B50\u5361\u670D\u52A1"]
          })
        })]
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      style: {
        height: '80px'
      }
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      className: "at-row brand_memu",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        className: "at-col at-col-6 card_status",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-row",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
            className: "nocard",
            style: {
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#FF5722',
              padding: '0 0 0 15px'
            },
            children: "\xA5100"
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
            className: "nocard",
            style: {
              padding: '0',
              margin: '6px 0 0 7px'
            }
          })]
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        className: "at-col at-col-6",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "book buybtn",
          onClick: /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee2() {
            var data;
            return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  if (!(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.getEnv() === _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.ENV_TYPE.ALIPAY)) {
                    _context2.next = 4;
                    break;
                  }
                  my.navigateTo({
                    url: "plugin://myPlugin/annual-card-detail?itemId=2024020822000912070045"
                  });
                  _context2.next = 10;
                  break;
                case 4:
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showLoading({
                    title: '请求中'
                  });
                  _context2.next = 7;
                  return _utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].Api.buy();
                case 7:
                  data = _context2.sent;
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.hideLoading();
                  if (data.code === 200) {
                    wx.requestPayment({
                      'timeStamp': '' + data.data.timestamp_unix,
                      'nonceStr': data.data.nonceStr,
                      'package': data.data.package,
                      'signType': data.data.signType,
                      'paySign': data.data.paySign,
                      // 支付签名
                      success: function success(res) {
                        _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
                          url: '/pages/buy/ok'
                        });
                      }
                    });
                  } else {
                    _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showToast({
                      title: data.message,
                      icon: 'none',
                      duration: 2000
                    });
                  }
                case 10:
                case "end":
                  return _context2.stop();
              }
            }, _callee2);
          })),
          children: "\u7ACB\u5373\u8D2D\u4E70"
        })
      })]
    })]
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Index);

/***/ }),

/***/ "./src/pages/buy/index.jsx":
/*!*********************************!*\
  !*** ./src/pages/buy/index.jsx ***!
  \*********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./index.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/buy/index.jsx");


var config = {"navigationBarTitleText":"","navigationBarBackgroundColor":"#fff","enableShareAppMessage":true};

_node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].enableShareAppMessage = true
var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/buy/index', {root:{cn:[]}}, config || {}))



/***/ }),

/***/ "./src/static/card-online-2024.jpg":
/*!*****************************************!*\
  !*** ./src/static/card-online-2024.jpg ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/card-online-2024.jpg";

/***/ })

},[["./src/pages/buy/index.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=index.js.map
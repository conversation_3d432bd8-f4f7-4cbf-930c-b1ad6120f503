{"version": 3, "file": "pages/my/index.js", "sources": ["webpack:///./src/pages/my/index.jsx", "webpack:///./src/pages/my/index.jsx?9bf4"], "sourcesContent": ["/*\r\n * @Author: 高超\r\n * @Date: 2021-11-27 11:01:05\r\n * @LastEditTime: 2022-08-03 18:05:51\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/my/index.jsx\r\n * love jiajia\r\n */\r\nimport { useState, useEffect } from 'react'\r\nimport Taro from '@tarojs/taro';\r\nimport { useSelector } from 'react-redux'\r\nimport { View, Text, Image } from '@tarojs/components'\r\nimport { AtList, AtListItem, AtCurtain } from \"taro-ui\"\r\nimport MyCard from '@/components/card/list';\r\nimport Memu from '@/components/memu'\r\nimport tools from '@/utils/tools'\r\nimport './index.scss'\r\n\r\n\r\nfunction Index (props) { \r\n  const [isOpened, setIsOpened] = useState(false)\r\n  const login = useSelector(state => state.login)\r\n  const [loginOk, setLoginOk] = useState(false)\r\n  useEffect( async () => {\r\n    const userInfo = tools.getDataSafe('userInfo')\r\n    if (userInfo !== null) {\r\n      if (userInfo.token !== null) setLoginOk(true)\r\n    }\r\n  }, [])\r\n\r\n  return (\r\n    <View>\r\n    <View style={{height:  `${tools.reTopH(6)}rpx` ,backgroundColor: '#fff'}}></View>\r\n    {loginOk ? <View className='at-row my_avatr'>\r\n      <View className='at-col at-col-3'>\r\n        <Image src={tools.getDataSafe('userInfo').avatar ? tools.getDataSafe('userInfo').avatar : `${tools.picUrl}/index/head.png`} mode=\"widthFix\" className=\"myhead\"/>\r\n      </View>\r\n      <View className='at-col at-col-9'>\r\n         <View className=\"nickname\">{tools.getDataSafe('userInfo').nickname ? tools.getDataSafe('userInfo').nickname : \"已登录\"}</View>\r\n         <View className=\"nickinfo\">欢迎使用北京风景名胜年票</View>\r\n      </View>\r\n    </View> : <View className='at-row my_avatr' onClick={() => {\r\n      tools.getUserInfo((res) => setLoginOk(res))\r\n    }}>\r\n      <View className='at-col at-col-3'>\r\n        <Image src={`${tools.picUrl}/index/head.png`} mode=\"widthFix\" className=\"myhead\"/>\r\n      </View>\r\n      <View className='at-col at-col-9'>\r\n         <View className=\"nickname\">请登录</View>\r\n         <View className=\"nickinfo\">登录查看更多内容</View>\r\n      </View>\r\n    </View>}\r\n    \r\n    <AtList>\r\n      <AtListItem title='我的卡包' arrow='right' thumb={`${tools.picUrl}/index/card.png`} onClick={() => {\r\n        Taro.navigateTo({\r\n          url: '/pages/my/card',\r\n        });\r\n      }}/>\r\n      <AtListItem title='预约记录' arrow='right' thumb={`${tools.picUrl}/index/book.png`} onClick={() => {\r\n         Taro.navigateTo({\r\n          url: '/pages/my/book',\r\n        });\r\n      }}/>\r\n      <AtListItem title='入园记录' arrow='right' thumb={`${tools.picUrl}/index/plan.png`} onClick={() => {\r\n        Taro.navigateTo({\r\n          url: '/pages/my/use',\r\n        });\r\n      }}/>\r\n      <AtListItem title='我的订单' arrow='right' thumb={`${tools.picUrl}/index/card.png`} onClick={() => {\r\n        Taro.navigateTo({\r\n          url: '/pages/my/order',\r\n        });\r\n      }}/>\r\n      {/* <AtListItem title='我的订单' arrow='right' thumb={`https://test.qqyhmmwg.com/res/card/memu/gift.png`} onClick={ async () => {\r\n        await tools.appConfig('order')\r\n      }}/> */}\r\n    </AtList>\r\n    <View style={{height: \"8px\"}}></View>\r\n    <AtList>\r\n      <AtListItem title='我的收藏' arrow='right' thumb={`${tools.picUrl}/index/love.png`} onClick={() => {\r\n         Taro.navigateTo({\r\n          url: '/pages/my/love',\r\n        });\r\n      }}/>\r\n      <AtListItem title='联系客服' arrow='right' thumb={`${tools.picUrl}/index/help.png`} onClick={() => {\r\n        Taro.makePhoneCall({\r\n          phoneNumber: \"4006091798\"\r\n        })\r\n      }} />\r\n    </AtList>\r\n    <View className=\"cardList\">\r\n      <Text className=\"lastuse\">最近使用</Text>\r\n      {login ? <MyCard callback={() => setLoginOk(true)} max={3} />  : null}\r\n    </View>\r\n    <AtCurtain\r\n        isOpened={isOpened}\r\n        onClose={()=>{\r\n          setIsOpened(false)\r\n        }}\r\n      >\r\n        <Image\r\n          style={{width: \"100%\", borderRadius: \"15rpx\"}}\r\n          mode='widthFix'\r\n          src={`${tools.ip}/go.jpg`}\r\n        />\r\n      </AtCurtain>\r\n      <Memu now={3} />\r\n  </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.jsx\"\nvar config = {\"navigationBarTitleText\":\"\",\"navigationStyle\":\"custom\",\"transparentTitle\":\"always\",\"titlePenetrate\":\"YES\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/my/index', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;AC9GA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
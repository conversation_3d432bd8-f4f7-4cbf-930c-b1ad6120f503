/*
 * @Author: 高超
 * @Date: 2021-11-27 23:20:42
 * @LastEditTime: 2021-11-28 02:23:16
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/tags/index.jsx
 * love jiajia
 */
import { Component, useEffect, useState } from 'react'
import { View, Text } from '@tarojs/components'
import tools from '@/utils/tools'
import "./index.scss"

const Tags = (props) => {
    const [active, setActive] = useState()
    useEffect( () => {
      setActive(props.data.data[props.val.name].includes(props.val.val))
    }, [props.data])
    return (
       <Text key={tools.createId(5)} className={`tags ${active ? 'active' : ''}`} onClick={() => {
         props.onClick((active) ? false : true)
         setActive(!active)
        //  if (props.val.multiple){
        //   props.callBack()
        // }
       }}>{props.children}</Text>
    )
}
export default Tags 
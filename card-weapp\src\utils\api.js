/*
 * @Author: 高超
 * @Date: 2021-10-30 11:39:41
 * @LastEditTime: 2022-07-22 10:02:24
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/utils/api.js
 * love jiajia
 */
import Request from '@/utils/request';
import config from './api.config.json';
export default {
    baseUrl: config.baseUrl,
    zhbaseUrl: config.zhbaseUrl,
    zhnodeUrl: config.zhnodeUrl,
    getUserCode : async (data) => {
        return await Request({
            url: 'codeToInfo',
            method: 'GET',
            data
        })
    },
    getAlipayUserCode : async (data) => {
        return await Request({
            url: 'alipayCodeToInfo',
            method: 'GET',
            data
        })
    },
    regZhMem : async (data) => {
        return await Request({
            url: 'regFromOther',
            method: 'POST',
            data
        },  config.zhnodeUrl)
    }, 
    myCardList : async (data) => {
        return await Request({
            url: 'userCardList',
            method: 'POST',
            data
        })
    }, 
    myBookList : async (data) => {
        return await Request({
            url: 'myBookList',
            method: 'POST',
            data
        })
    }, 
    index:  async (data) => {
        return await Request({
            url: 'brandListIndex',
            method: 'POST',
            data
        })
    },
    userReg:  async (data) => {
        return await Request({
            url: 'userUpdate',
            method: 'POST',
            data
        })
    },
    alipayUserReg:  async (data) => {
        return await Request({
            url: 'alipayUserUpdate',
            method: 'POST',
            data
        })
    },
    bindCard:  async (data) => {
        return await Request({
            url: 'bindCard',
            method: 'POST',
            data
        })
    },
    getPhone:  async (data) => {
        return await Request({
            url: 'getPhone',
            method: 'POST',
            data
        })
    },
    brandCity:  async (data) => {
        return await Request({
            url: 'brandCity',
            method: 'POST',
            data
        })
    },
    brandBuy:  async (data) => {
        return await Request({
            url: 'brandBuy',
            method: 'POST',
            data
        })
    },
    brandList:  async (data) => {
        return await Request({
            url: 'brandList',
            method: 'POST',
            data
        })
    },
    brandBuyList:  async (data) => {
        return await Request({
            url: 'agent/allFindBrand',
            method: 'POST',
            data
        }, config.zhbaseUrl)
    },
    myCollect:  async (data) => {
        return await Request({
            url: 'myCollect',
            method: 'POST',
            data
        })
    },
    needBookList:  async (data) => {
        return await Request({
            url: 'needBookList',
            method: 'POST',
            data
        })
    },
    topicInfo:  async (data) => {
        return await Request({
            url: 'topicInfo',
            method: 'POST',
            data
        })
    },
    brandInfo:  async (data) => {
        return await Request({
            url: 'brandInfo',
            method: 'POST',
            data
        })
    },
    brandInfoZh:  async (data) => {
        return await Request({
            url: `getBrand/${data}`,
            method: 'GET'
        }, config.zhnodeUrl)
    },
    getBrandDatePrice: async (data) => {
        return await Request({
            url: `fe/brandDatePrice`,
            method: 'GET',
            data
        }, config.zhbaseUrl)
    },
    brandGoodZh:  async (data) => {
        return await Request({
            url: `fe/brandVlog?id=${data}&shopid=1`,
            method: 'GET'
        }, config.zhbaseUrl)
    },
    brandCollect:  async (data) => {
        return await Request({
            url: 'brandCollect',
            method: 'POST',
            data
        })
    },
    brandBookList:  async (data) => {
        return await Request({
            url: 'brandBookList',
            method: 'POST',
            data
        })
    },
    addBrandBook:  async (data) => {
        return await Request({
            url: 'addBrandBook',
            method: 'POST',
            data
        })
    },
    myCard : async () => {
        return await Request({
            url: 'myCard',
            method: 'GET',
        })
    },
    appConfig : async () => {
        return await Request({
            url: 'appConfig',
            method: 'GET',
        })
    },
    cardInfo : async (data) => {
        return await Request({
            url: 'getCradUser',
            method: 'GET',
            data
        })
    }, 
    brandMap : async (data) => {
        return await Request({
            url: 'brandMap',
            method: 'GET'
        })
    }, 
    removeBook: async (data) => {
        return await Request({
            url: 'removeBook',
            method: 'POST',
            data
        })
    },
    noteList : async (data) => {
        return await Request({
            url: 'noteList',
            method: 'GET'
        })
    },
    noteInfo : async (data) => {
        return await Request({
            url: 'noteInfo',
            method: 'POST',
            data
        })
    },
    myUseList : async (data) => {
        return await Request({
            url: 'myUseList',
            method: 'POST',
            data
        })
    },
    getGoodWxshopLive: async (data) => {
        return await Request({
            url: `fe/liveBuy?id=${data}`,
            method: 'GET'
        }, config.zhbaseUrl)
    },
    getShopGood:  async (data) => {
        return await Request({
            url: 'goods/detail',
            method: 'GET',
            data
        }, config.zhbaseUrl)
    },
    getShopGoodRule:  async (data) => {
        return await Request({
            url: `fe/brandDatePrice?id=${data}`,
            method: 'GET',
        }, config.zhbaseUrl)
    },
    addShopOrder:  async (data) => {
        return await Request({
            url: 'buy/wxShopAdd',
            method: 'POST',
            data
        }, config.zhbaseUrl)
    },
    pay:  async (data) => {
        return await Request({
            url: 'addOrderAll',
            method: 'POST',
            data
        }, config.zhnodeUrl)
    },
    buy:  async () => {
        return await Request({
            url: 'placeOrder',
            method: 'GET',
        }, config.baseUrl)
    },
    myOrders : async (data) => {
        return await Request({
            url: 'myOrders',
            method: 'GET'
        })
    }
}

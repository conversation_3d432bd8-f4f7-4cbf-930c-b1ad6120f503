{"version": 3, "file": "pages/sreach/type.js", "sources": ["webpack:///./src/pages/sreach/type.jsx", "webpack:///./src/pages/sreach/type.jsx?7b37"], "sourcesContent": ["/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2021-12-18 00:11:31\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/type.jsx\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport { View } from '@tarojs/components'\r\nimport { useSelector } from 'react-redux'\r\nimport Taro, { useReachBottom } from '@tarojs/taro'\r\nimport Memu from '@/components/memu'\r\nimport None from '@/components/tips/none'\r\nimport SreachTools from './bar'\r\nimport Item from '@/components/brand/item'\r\nimport tools from '@/utils/tools'\r\nimport \"./index.scss\"\r\n\r\nconst SreachIndex = (props) => {\r\n    const query = tools.getQuery()\r\n    const login = useSelector(state => state.login)\r\n    const [init, setInit] = useState(null)\r\n    const [page , setPage] = useState(1)\r\n    const [brandList , setBrandList] = useState([])  \r\n    const [loaded, setLoaded] = useState(false)\r\n    const [title, setTitle] = useState('')\r\n    useEffect( async () => {\r\n        setLoaded(false)\r\n        Taro.showLoading({\r\n          mask: true,\r\n          title: '请稍等'\r\n        })\r\n        if (login) {\r\n          const apiParams = {\r\n            line_id: tools.line_code,\r\n            order : \"km\",\r\n            point : true,\r\n            action : \"type\",\r\n            type_list : [query.data.v],\r\n            page : {\r\n                current: page,\r\n                pageSize : 20\r\n            }\r\n          }\r\n          const data = await tools.Api.brandList(apiParams)\r\n          if (data.code === 200) {\r\n            setInit(data.data.page)\r\n            setBrandList([\r\n                ...brandList,\r\n                ...data.data.list\r\n            ])\r\n            setTitle(data.data.typeShow.title)\r\n            Taro.setNavigationBarTitle({\r\n              title: `${data.data.typeShow.title}相关`\r\n            })\r\n          }\r\n          setLoaded(true)\r\n          Taro.hideLoading()\r\n        }\r\n    }, [login, page])\r\n\r\n    useReachBottom(() => {\r\n        const nextPage = init.current + 1\r\n        if (nextPage <= init.totalPage) {\r\n          setPage(nextPage)\r\n        } else {\r\n          Taro.showToast({\r\n            title: '暂无更多内容',\r\n            icon: 'none',\r\n            duration: 2000\r\n          })\r\n        }\r\n    })\r\n\r\n    return (\r\n        <View>\r\n          <View style={{fontSize: '36px', padding: '20px', fontWeight: 'bold'}}>{title}</View>\r\n          <View>\r\n            {\r\n                (brandList.length > 0) ?  brandList.map(v => <Item className=\"list\" data={v} cname=\"images_l\"/>) : <None loaded={loaded}/>\r\n            }\r\n          </View>\r\n          <Memu now={3} />\r\n        </View>\r\n    )\r\n}\r\nexport default SreachIndex ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./type.jsx\"\nvar config = {\"navigationBarTitleText\":\"精选景区\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/sreach/type', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
/*
 * @Author: 高超
 * @Date: 2021-11-04 17:05:46
 * @LastEditTime: 2022-02-16 17:37:22
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/bind/index.jsx
 * love jiajia
 */
import { Component, useEffect, useState } from 'react'
import Taro, { useShareAppMessage } from '@tarojs/taro'
import { View, Text, Button, Image } from '@tarojs/components'
import { AtIcon, AtButton } from 'taro-ui'
import Bg from '@/components/tool/bg'
import tools from "@/utils/tools"
import "./index.scss"
import cardJpg from '@/static/card-online-2024.jpg'

function Index (props) {
  const [check, setCheck] = useState(true)
  const [tel, setTel] = useState(false)

  useEffect( async () => {
    const userInfo = tools.getDataSafe("userInfo")
    if (userInfo !== null) {
      setTel(Boolean(userInfo.user_tel))
    } else {
      setTel(false)
    }
  }, [tools.getDataSafe("userInfo")])

  useShareAppMessage(() => {
    return {
      title: "购买北京风景名胜区年票",
      path: `/pages/buy/index`,
      imageUrl: `?x-oss-process=image/resize,m_fill,h_400,w_400`
    }
  })

  return (
    <View className='pageAdd'>
      <Bg/>
      
       <View style={{textAlign:"center"}}>
         <Image src={'https://cardall.oss-cn-beijing.aliyuncs.com/card_online.jpg'} mode="widthFix" style={{marginTop : "30px", borderRadius: '12px'}}/>
         <View className="title">北京风景名胜年票</View>
         <View className="info">购买并开通年票，全年便捷入园</View>
         <Image src={`${tools.picUrl}/bind/link.jpg`} mode="widthFix" style={{marginTop : "30px"}}/>
       </View>

       <View style={{marginLeft: "15px", marginTop: "15px"}}>
        <View className='at-row at-row--wrap'>
        <View className='at-col at-col-1'>
        </View>
        <View className='at-col at-col-10 at-col--wrap'>
          <View className="txt">
            购买即表示同意<Text style={{color: "#03A9F4"}} onClick={() => {
              Taro.navigateTo({
                url: '/pages/buy/txt',
              });
            }}>《用户授权协议》</Text>，并授权<Text style={{fontWeight:"bold"}}>北京风景名胜协会</Text>使用您的姓名、证件号、手机号进行实名开卡，以便为您提供更好的电子卡服务
          </View>
        </View>
        </View>
      </View>
     
      <View style={{height: '80px'}}></View>
      <View className="at-row brand_memu">
        <View className='at-col at-col-6 card_status'>
          <View className='at-row'>
            <Text className='nocard' style={{fontSize: '18px', fontWeight: 'bold', color: '#FF5722', padding: '0 0 0 15px'}}>¥100</Text>
            <Text className='nocard' style={{padding: '0', margin: '6px 0 0 7px'}}></Text>
          </View>
        </View> 
        <View className='at-col at-col-6'>
          <View className="book buybtn" onClick={async () => {
            if (Taro.getEnv() === Taro.ENV_TYPE.ALIPAY) { 
              my.navigateTo({
                url: `plugin://myPlugin/annual-card-detail?itemId=2024020822000912070045`,
              });
            } else {
              Taro.showLoading({title: '请求中'});
              const data = await tools.Api.buy()
              Taro.hideLoading()
              if (data.code === 200) {
                wx.requestPayment({
                  'timeStamp': '' + data.data.timestamp_unix,
                  'nonceStr': data.data.nonceStr,
                  'package': data.data.package,
                  'signType': data.data.signType,
                  'paySign': data.data.paySign, // 支付签名
                  success: function(res) {
                    Taro.navigateTo({
                      url: '/pages/buy/ok'
                    });
                  }
                })
              } else {
                Taro.showToast({
                  title: data.message,
                  icon: 'none',
                  duration: 2000
                })
              }
            }
          }}>立即购买</View>
        </View> 
      </View>
    </View>
  )
}
export default Index;

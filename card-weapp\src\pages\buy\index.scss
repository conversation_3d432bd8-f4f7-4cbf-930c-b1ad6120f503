page {
    background-color: #fff;
}
.pageAdd {
    .title {
        font-weight:bold;
        font-size: 35px;
        text-align: center;
        margin-top: 40px;
    }
    .info {
        font-size: 32px;
        text-align: center;
        margin-top: 20px;
    }
    .txt {
        font-size: 25px;
        color: rgb(160, 157, 157);
    }
    .btn {
        width: 90%;
        height: 80px;
        background-color: #02b921;
        color: #fff;
        margin: 0 auto;
        text-align: center;
        border-radius: 10px;
        line-height: 80px;
        margin-top: 25px;
    }
    .disable {
        opacity: 0.4;
    }
    .buybtn {
        background-color: #FF5722;
    }
}

.addPage {
    color: #9D9D9D;
    padding: 50px 40px;
    .input{
        margin-top: 10px;
        border-bottom: 1px solid #E3E3E3;
        padding: 10px 7px;
        color: #000;
    }
    .card {
        text-align: center;
        margin-top: 60px;
    }
    .image {
        width: 180px;
        height: 200px;
        border: 1px solid #ccc;
        margin: 0 auto;
        border-radius: 10px;
        text-align: center;
        font-size: 25px;
        color: #2196F3
    }
    .photo {
        width: 180px;
        height: 200px;
    }
    .photoSamll {
        width: 100px;
        height: 120px;
        margin: 0 auto;
    }
}

.jbtn {
    position: fixed;
    bottom: 0;
    font-size: 35px;
    width: 100%;
    height: 90px;
    background-color: #02b921;
    color: #fff;
    margin: 0 auto;
    text-align: center;
    line-height: 90px;
}

.okbtn {
    width: 60%;
    height: 70px;
    background-color: #02b921;
    color: #fff;
    margin: 0 auto;
    text-align: center;
    border-radius: 10px;
    line-height: 70px;
    margin-top: 80px;
}

.checkbtn {
    width: 60%;
    height: 70px;
    background-color: #fff;
    border: solid 1px #ddd;
    color: #333;
    margin: 0 auto;
    text-align: center;
    border-radius: 10px;
    line-height: 70px;
    margin-top: 20px;
}
.at-icon {
    margin-top: 0px;
}
import { Component, useEffect, useState } from 'react'
import { View, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { AtRadio, AtNoticebar } from 'taro-ui'
import tools from '@/utils/tools'
import Bg from '@/components/tool/bg'
import "./index.scss"

function Index (props) { 
  const params =  tools.getQuery()
  const good_key = Boolean(params.data.key) ? params.data.key : ""
  const [who, setWho] = useState(0)
  useEffect( async () => {}, [])
  const handleChange = value => {
    setWho(value)
  }
  return (
    <View>
      <Bg/>
      <AtNoticebar icon='volume-plus'>为方便使用您可为您的家人开通卡片，尤其是老人与孩子呦～祝您游玩愉快！</AtNoticebar>
      <View style={{padding: '30px 20px'}}>
        <AtRadio
          options={tools.relation}
          value={who}
          onClick={handleChange.bind(this)}
        />
      </View>
      <View className="jbtn" onClick={() => {
        Taro.navigateTo({
          url: `/pages/bind/add?rel=${who}&key=${good_key}`,
        });
      }}>下一步</View>
    </View>
  )
}
export default Index;

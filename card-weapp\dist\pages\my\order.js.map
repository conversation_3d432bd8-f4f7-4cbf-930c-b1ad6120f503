{"version": 3, "file": "pages/my/order.js", "sources": ["webpack:///./src/pages/my/order.jsx", "webpack:///./src/components/my/order.jsx", "webpack:///./src/pages/my/order.jsx?1658"], "sourcesContent": ["import { Component, useState, useEffect } from 'react'\r\nimport Taro from '@tarojs/taro';\r\nimport { View } from '@tarojs/components'\r\nimport tools from '@/utils/tools'\r\nimport OrderItem from '@/components/my/order';\r\nimport { AtList, AtIcon } from \"taro-ui\"\r\nimport './index.scss'\r\n\r\nfunction Index (props) { \r\n  const [init, setInit] = useState([])\r\n  useEffect( async () => {\r\n    Taro.showLoading({\r\n      mask: true,\r\n      title: '读取中'\r\n    })\r\n    const myOrders = await tools.Api.myOrders()\r\n    Taro.hideLoading()\r\n    if (myOrders.code === 200) {\r\n      setInit(myOrders.data)\r\n    }\r\n  }, [])\r\n\r\n  return (\r\n    <View className='index'>\r\n      {(init.length === 0) ? <View style={{textAlign: 'center', marginTop: \"60px\", color: \"#333\", fontSize: \"16px\"}}>\r\n          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>\r\n          <View style={{marginTop: \"10px\" , color: \"#848181\"}}>暂无订单记录</View>\r\n        </View> :\r\n        <View>\r\n          {init.map(v => {\r\n            return <OrderItem data={v}/>\r\n          })}   \r\n        </View>\r\n        }\r\n      <View style={{height: '30px'}}></View>  \r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { Component,useState, useEffect, Fragment } from 'react'\r\nimport Taro from '@tarojs/taro';\r\nimport { View, Text, Button, Image } from '@tarojs/components'\r\nimport tools from '@/utils/tools'\r\nimport moment from \"moment\"\r\nimport './index.scss'\r\n\r\nconst Item = props => {\r\n    const { data } = props\r\n    console.warn('--------------------', data)\r\n    const [show, setShow] = useState(true)\r\n    const csscolor = data.bound || data.refund_status ? {color: '#ccc'} : {}\r\n    return (\r\n        <Fragment>\r\n        {show ? <View className='bookItem'>\r\n        <View className='at-row cardNum'>\r\n            <View className='at-col at-col-4' style={csscolor}>\r\n            状态：{data.bound ? '已使用' : '未使用'} {data.refund_status ? '(已退)' : ''}\r\n            </View>\r\n            <View className='at-col at-col-7' style={{textAlign: 'right'}}>\r\n                <Text style={csscolor}>购买日期：{data.create_time}</Text>\r\n            </View>\r\n        </View>\r\n        <View>\r\n        <View className='at-row' style={{ padding: '0 14px', width: '90%', marginTop: '14px'}}>\r\n            <View className='at-col at-col-3' style={{textAlign: 'center'}}>\r\n                <View className='day'  style={csscolor}>北京风景名胜区电子年票一张</View>\r\n            </View>\r\n        </View>\r\n        {!data.refund_status ? <View className='at-row' style={{ padding: '0 14px',width: '90%', marginBottom: '0px', marginTop: '14px', color:'#8e8c8c'}}\r\n            onClick={() => {\r\n            Taro.setClipboardData({\r\n              data: data.good_key,\r\n              success: function (res) {\r\n                Taro.showToast({\r\n                   title: '激活码已复制',\r\n                   icon: 'none'\r\n                })\r\n              }\r\n            })\r\n        }}>\r\n            <View>\r\n                <Text style={csscolor}>激活码：{data.good_key}</Text>\r\n            </View>\r\n            <View className='copy_btn'>点击复制</View>\r\n        </View> : <View></View>}\r\n        <View className='at-row' style={{ padding: '0 14px',width: '90%', marginBottom: '14px', marginTop: '6px', color:'#8e8c8c'}}>\r\n            <View className='at-col at-col-12' style={{textAlign: 'left'}}>\r\n                <Text style={csscolor}>凭此激活码可激活年票一张，重绑无效，请妥善保管</Text>\r\n            </View>\r\n        </View>\r\n        {!data.bound && !data.refund_status ? <View className='at-row oper'>\r\n            <View className='at-col at-col-6' style={{textAlign: 'center', borderRight: '1px solid #e2e2e2'}} onClick={ () => {\r\n                Taro.navigateTo({\r\n                    url : `/pages/bind/index?key=${data.good_key}`\r\n                })\r\n            }}>\r\n                立即激活绑定\r\n            </View>\r\n            <View className='at-col at-col-6' style={{textAlign: 'center'}} onClick={() => {\r\n                Taro.showModal({\r\n                    title: `转赠激活码`,\r\n                    content: `请将该激活码发送给您的家人或朋友，凭此码可激活北京风景名胜区协会年票一张`,\r\n                    success:  async (res) => {\r\n                    }\r\n                  })\r\n            }}>\r\n                转赠家人或朋友\r\n            </View>\r\n        </View> : <View><View style={{height: '10px'}}></View></View>}\r\n        \r\n        </View>\r\n        </View> : null}\r\n        </Fragment>\r\n        \r\n    )\r\n}\r\nexport default Item;", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./order.jsx\"\nvar config = {\"navigationBarTitleText\":\"我的订单\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/my/order', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAEA;AAAA;AAAA;AACA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtCA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAIA;AACA;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
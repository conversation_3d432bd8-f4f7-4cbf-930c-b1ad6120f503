{"version": 3, "file": "taro.js", "sources": ["webpack:///./node_modules/@tarojs/api/dist/env.js", "webpack:///./node_modules/@tarojs/api/dist/index.js", "webpack:///./node_modules/@tarojs/api/dist/interceptor/chain.js", "webpack:///./node_modules/@tarojs/api/dist/interceptor/index.js", "webpack:///./node_modules/@tarojs/api/dist/interceptor/interceptors.js", "webpack:///./node_modules/@tarojs/api/dist/tools.js", "webpack:///../src/is.ts", "webpack:///../../src/template/custom-wrapper.ts", "webpack:///../../taro-components/mini/index.js", "webpack:///../src/components-react.ts", "webpack:///../src/apis-list.ts", "webpack:///../src/apis.ts", "webpack:///../src/components.ts", "webpack:///../src/runtime-utils.ts", "webpack:///../src/runtime.ts", "webpack:///../src/props.ts", "webpack:///../src/reconciler.ts", "webpack:///../src/render.ts", "webpack:///../src/index.ts", "webpack:///../src/polyfill/reflect-metadata.js", "webpack:///../src/constants/identifiers.ts", "webpack:///../src/constants/index.ts", "webpack:///../src/utils/index.ts", "webpack:///../src/dom/event-target.ts", "webpack:///../src/hydrate.ts", "webpack:///../src/dom/event-source.ts", "webpack:///../src/interface/container.ts", "webpack:///../src/dom/node.ts", "webpack:///../src/dom/text.ts", "webpack:///../src/dom/style_properties.ts", "webpack:///../src/dom/style.ts", "webpack:///../src/dom/tree.ts", "webpack:///../src/dom/class-list.ts", "webpack:///../src/dom/element.ts", "webpack:///../node_modules/lodash-es/isArray.js", "webpack:///../node_modules/lodash-es/_freeGlobal.js", "webpack:///../node_modules/lodash-es/_root.js", "webpack:///../node_modules/lodash-es/_Symbol.js", "webpack:///../node_modules/lodash-es/_getRawTag.js", "webpack:///../node_modules/lodash-es/_objectToString.js", "webpack:///../node_modules/lodash-es/_baseGetTag.js", "webpack:///../node_modules/lodash-es/isObjectLike.js", "webpack:///../node_modules/lodash-es/isSymbol.js", "webpack:///../node_modules/lodash-es/_isKey.js", "webpack:///../node_modules/lodash-es/isObject.js", "webpack:///../node_modules/lodash-es/isFunction.js", "webpack:///../node_modules/lodash-es/_coreJsData.js", "webpack:///../node_modules/lodash-es/_isMasked.js", "webpack:///../node_modules/lodash-es/_toSource.js", "webpack:///../node_modules/lodash-es/_baseIsNative.js", "webpack:///../node_modules/lodash-es/_getValue.js", "webpack:///../node_modules/lodash-es/_getNative.js", "webpack:///../node_modules/lodash-es/_nativeCreate.js", "webpack:///../node_modules/lodash-es/_hashClear.js", "webpack:///../node_modules/lodash-es/_hashDelete.js", "webpack:///../node_modules/lodash-es/_hashGet.js", "webpack:///../node_modules/lodash-es/_hashHas.js", "webpack:///../node_modules/lodash-es/_hashSet.js", "webpack:///../node_modules/lodash-es/_Hash.js", "webpack:///../node_modules/lodash-es/_listCacheClear.js", "webpack:///../node_modules/lodash-es/eq.js", "webpack:///../node_modules/lodash-es/_assocIndexOf.js", "webpack:///../node_modules/lodash-es/_listCacheDelete.js", "webpack:///../node_modules/lodash-es/_listCacheGet.js", "webpack:///../node_modules/lodash-es/_listCacheHas.js", "webpack:///../node_modules/lodash-es/_listCacheSet.js", "webpack:///../node_modules/lodash-es/_ListCache.js", "webpack:///../node_modules/lodash-es/_Map.js", "webpack:///../node_modules/lodash-es/_mapCacheClear.js", "webpack:///../node_modules/lodash-es/_isKeyable.js", "webpack:///../node_modules/lodash-es/_getMapData.js", "webpack:///../node_modules/lodash-es/_mapCacheDelete.js", "webpack:///../node_modules/lodash-es/_mapCacheGet.js", "webpack:///../node_modules/lodash-es/_mapCacheHas.js", "webpack:///../node_modules/lodash-es/_mapCacheSet.js", "webpack:///../node_modules/lodash-es/_MapCache.js", "webpack:///../node_modules/lodash-es/memoize.js", "webpack:///../node_modules/lodash-es/_memoizeCapped.js", "webpack:///../node_modules/lodash-es/_stringToPath.js", "webpack:///../node_modules/lodash-es/_arrayMap.js", "webpack:///../node_modules/lodash-es/_baseToString.js", "webpack:///../node_modules/lodash-es/toString.js", "webpack:///../node_modules/lodash-es/_castPath.js", "webpack:///../node_modules/lodash-es/_toKey.js", "webpack:///../node_modules/lodash-es/_baseGet.js", "webpack:///../node_modules/lodash-es/get.js", "webpack:///../src/options.ts", "webpack:///../src/perf.ts", "webpack:///../src/dom/root.ts", "webpack:///../src/dom/form.ts", "webpack:///../src/dom/svg.ts", "webpack:///../src/dom-external/inner-html/scaner.ts", "webpack:///../src/dom-external/inner-html/tags.ts", "webpack:///../src/dom-external/inner-html/utils.ts", "webpack:///../src/dom-external/inner-html/style.ts", "webpack:///../src/dom-external/inner-html/parser.ts", "webpack:///../src/dom-external/inner-html/html.ts", "webpack:///../src/dom-external/node.ts", "webpack:///../src/dom-external/node-impl.ts", "webpack:///../src/dom-external/element.ts", "webpack:///../src/dom-external/element-impl.ts", "webpack:///../src/dom/document.ts", "webpack:///../src/hooks.ts", "webpack:///../src/constants/events.ts", "webpack:///../src/container/default-hooks.ts", "webpack:///../src/container/plugin-hooks.ts", "webpack:///../src/container/index.ts", "webpack:///../src/dom/event.ts", "webpack:///../src/env.ts", "webpack:///../src/bom/document.ts", "webpack:///../src/bom/navigator.ts", "webpack:///../src/bom/raf.ts", "webpack:///../src/bom/getComputedStyle.ts", "webpack:///../src/bom/window.ts", "webpack:///../src/current.ts", "webpack:///../src/emitter/emitter.ts", "webpack:///../src/dsl/common.ts", "webpack:///../src/dsl/react.ts", "webpack:///../src/dsl/vue.ts", "webpack:///../src/dsl/vue3.ts", "webpack:///../src/dsl/hooks.ts", "webpack:///../src/next-tick.ts", "webpack:///../src/utils.ts", "webpack:///../src/native-apis.ts", "webpack:///./node_modules/@tarojs/taro/index.js", "webpack:///../src/polyfill.js", "webpack:///../src/env.js", "webpack:///../src/interceptor/chain.js", "webpack:///../src/interceptor/index.js", "webpack:///../src/interceptor/interceptors.js", "webpack:///../src/tools.js", "webpack:///../src/index.js", "webpack:///./node_modules/taro-skeleton/node_modules/@tarojs/taro/index.js", "webpack:///./node_modules/taro-skeleton/node_modules/@tarojs/taro/lib/alipay.js", "webpack:///./node_modules/taro-skeleton/node_modules/@tarojs/taro/lib/data-cache.js", "webpack:///./node_modules/taro-skeleton/node_modules/@tarojs/taro/lib/utils.js"], "sourcesContent": ["const ENV_TYPE = {\n    WEAPP: 'WEAPP',\n    SWAN: 'SWAN',\n    ALIPAY: 'ALIPAY',\n    TT: 'TT',\n    QQ: 'QQ',\n    JD: 'JD',\n    WEB: 'WEB',\n    RN: 'RN',\n    HARMONY: 'HARMONY',\n    QUICKAPP: 'QUICKAPP',\n    HARMONYHYBRID: 'HARMONYHYBRID',\n};\nfunction getEnv() {\n    if (process.env.TARO_ENV === 'weapp') {\n        return ENV_TYPE.WEAPP;\n    }\n    else if (process.env.TARO_ENV === 'alipay') {\n        return ENV_TYPE.ALIPAY;\n    }\n    else if (process.env.TARO_ENV === 'swan') {\n        return ENV_TYPE.SWAN;\n    }\n    else if (process.env.TARO_ENV === 'tt') {\n        return ENV_TYPE.TT;\n    }\n    else if (process.env.TARO_ENV === 'jd') {\n        return ENV_TYPE.JD;\n    }\n    else if (process.env.TARO_ENV === 'qq') {\n        return ENV_TYPE.QQ;\n    }\n    else if (process.env.TARO_ENV === 'harmony-hybrid') {\n        return ENV_TYPE.HARMONYHYBRID;\n    }\n    else if (process.env.TARO_ENV === 'h5' || process.env.TARO_PLATFORM === 'web') {\n        return ENV_TYPE.WEB;\n    }\n    else if (process.env.TARO_ENV === 'rn') {\n        return ENV_TYPE.RN;\n    }\n    else if (process.env.TARO_ENV === 'harmony' || process.env.TARO_PLATFORM === 'harmony') {\n        return ENV_TYPE.HARMONY;\n    }\n    else if (process.env.TARO_ENV === 'quickapp') {\n        return ENV_TYPE.QUICKAPP;\n    }\n    else {\n        return process.env.TARO_ENV || 'Unknown';\n    }\n}\n\nexport { ENV_TYPE, getEnv };\n", "import { Current, getCurrentInstance, options, nextTick, eventCenter, Events } from '@tarojs/runtime';\nimport { getEnv, ENV_TYPE } from './env.js';\nimport Link, { interceptorify } from './interceptor/index.js';\nimport * as interceptors from './interceptor/interceptors.js';\nimport { Behavior, getInitPxTransform, getPreload, getPxTransform } from './tools.js';\n\n/* eslint-disable camelcase */\nconst Taro = {\n    Behavior,\n    getEnv,\n    ENV_TYPE,\n    Link,\n    interceptors,\n    Current,\n    getCurrentInstance,\n    options,\n    nextTick,\n    eventCenter,\n    Events,\n    getInitPxTransform,\n    interceptorify\n};\nTaro.initPxTransform = getInitPxTransform(Taro);\nTaro.preload = getPreload(Current);\nTaro.pxTransform = getPxTransform(Taro);\n\nexport { Taro as default };\n", "import { isFunction } from '@tarojs/shared';\n\nclass Chain {\n    constructor(requestParams, interceptors, index) {\n        this.index = index || 0;\n        this.requestParams = requestParams || {};\n        this.interceptors = interceptors || [];\n    }\n    proceed(requestParams = {}) {\n        this.requestParams = requestParams;\n        if (this.index >= this.interceptors.length) {\n            throw new Error('chain 参数错误, 请勿直接修改 request.chain');\n        }\n        const nextInterceptor = this._getNextInterceptor();\n        const nextChain = this._getNextChain();\n        const p = nextInterceptor(nextChain);\n        const res = p.catch(err => Promise.reject(err));\n        Object.keys(p).forEach(k => isFunction(p[k]) && (res[k] = p[k]));\n        return res;\n    }\n    _getNextInterceptor() {\n        return this.interceptors[this.index];\n    }\n    _getNextChain() {\n        return new Chain(this.requestParams, this.interceptors, this.index + 1);\n    }\n}\n\nexport { Chain as default };\n", "import Chain from './chain.js';\n\nclass Link {\n    constructor(interceptor) {\n        this.taroInterceptor = interceptor;\n        this.chain = new Chain();\n    }\n    request(requestParams) {\n        const chain = this.chain;\n        const taroInterceptor = this.taroInterceptor;\n        chain.interceptors = chain.interceptors\n            .filter(interceptor => interceptor !== taroInterceptor)\n            .concat(taroInterceptor);\n        return chain.proceed(Object.assign({}, requestParams));\n    }\n    addInterceptor(interceptor) {\n        this.chain.interceptors.push(interceptor);\n    }\n    cleanInterceptors() {\n        this.chain = new Chain();\n    }\n}\nfunction interceptorify(promiseifyApi) {\n    return new Link(function (chain) {\n        return promiseifyApi(chain.requestParams);\n    });\n}\n\nexport { Link as default, interceptorify };\n", "import { isUndefined, isFunction } from '@tarojs/shared';\n\nfunction timeoutInterceptor(chain) {\n    const requestParams = chain.requestParams;\n    let p;\n    const res = new Promise((resolve, reject) => {\n        const timeout = setTimeout(() => {\n            clearTimeout(timeout);\n            reject(new Error('网络链接超时,请稍后再试！'));\n        }, (requestParams && requestParams.timeout) || 60000);\n        p = chain.proceed(requestParams);\n        p\n            .then(res => {\n            if (!timeout)\n                return;\n            clearTimeout(timeout);\n            resolve(res);\n        })\n            .catch(err => {\n            timeout && clearTimeout(timeout);\n            reject(err);\n        });\n    });\n    // @ts-ignore\n    if (!isUndefined(p) && isFunction(p.abort))\n        res.abort = p.abort;\n    return res;\n}\nfunction logInterceptor(chain) {\n    const requestParams = chain.requestParams;\n    const { method, data, url } = requestParams;\n    // eslint-disable-next-line no-console\n    console.log(`http ${method || 'GET'} --> ${url} data: `, data);\n    const p = chain.proceed(requestParams);\n    const res = p\n        .then(res => {\n        // eslint-disable-next-line no-console\n        console.log(`http <-- ${url} result:`, res);\n        return res;\n    });\n    // @ts-ignore\n    if (isFunction(p.abort))\n        res.abort = p.abort;\n    return res;\n}\n\nexport { logInterceptor, timeoutInterceptor };\n", "import { isObject, isFunction } from '@tarojs/shared';\n\nfunction Behavior(options) {\n    return options;\n}\nfunction getPreload(current) {\n    return function (key, val) {\n        current.preloadData = isObject(key)\n            ? key\n            : {\n                [key]: val\n            };\n    };\n}\nconst defaultDesignWidth = 750;\nconst defaultDesignRatio = {\n    640: 2.34 / 2,\n    750: 1,\n    828: 1.81 / 2\n};\nconst defaultBaseFontSize = 20;\nconst defaultUnitPrecision = 5;\nconst defaultTargetUnit = 'rpx';\nfunction getInitPxTransform(taro) {\n    return function (config) {\n        const { designWidth = defaultDesignWidth, deviceRatio = defaultDesignRatio, baseFontSize = defaultBaseFontSize, targetUnit = defaultTargetUnit, unitPrecision = defaultUnitPrecision, } = config;\n        taro.config = taro.config || {};\n        taro.config.designWidth = designWidth;\n        taro.config.deviceRatio = deviceRatio;\n        taro.config.baseFontSize = baseFontSize;\n        taro.config.targetUnit = targetUnit;\n        taro.config.unitPrecision = unitPrecision;\n    };\n}\nfunction getPxTransform(taro) {\n    return function (size) {\n        const config = taro.config || {};\n        const baseFontSize = config.baseFontSize;\n        const deviceRatio = config.deviceRatio || defaultDesignRatio;\n        const designWidth = (((input = 0) => isFunction(config.designWidth)\n            ? config.designWidth(input)\n            : config.designWidth || defaultDesignWidth))(size);\n        if (!(designWidth in deviceRatio)) {\n            throw new Error(`deviceRatio 配置中不存在 ${designWidth} 的设置！`);\n        }\n        const targetUnit = config.targetUnit || defaultTargetUnit;\n        const unitPrecision = config.unitPrecision || defaultUnitPrecision;\n        const formatSize = ~~size;\n        let rootValue = 1 / deviceRatio[designWidth];\n        switch (targetUnit) {\n            case 'rem':\n                rootValue *= baseFontSize * 2;\n                break;\n            case 'px':\n                rootValue *= 2;\n                break;\n        }\n        let val = formatSize / rootValue;\n        if (unitPrecision >= 0 && unitPrecision <= 100) {\n            val = Number(val.toFixed(unitPrecision));\n        }\n        return val + targetUnit;\n    };\n}\n\nexport { Behavior, getInitPxTransform, getPreload, getPxTransform };\n", "export function isString (o: unknown): o is string {\n  return typeof o === 'string'\n}\n\nexport function isUndefined (o: unknown): o is undefined {\n  return typeof o === 'undefined'\n}\n\nexport function isNull (o: unknown): o is null {\n  return o === null\n}\n\nexport function isObject<T> (o: unknown): o is T {\n  return o !== null && typeof o === 'object'\n}\n\nexport function isBoolean (o: unknown): o is boolean {\n  return o === true || o === false\n}\n\nexport function isFunction (o: unknown): o is (...args: any[]) => any {\n  return typeof o === 'function'\n}\n\nexport function isNumber (o: unknown): o is number {\n  return typeof o === 'number'\n}\n\nexport function isBooleanStringLiteral (o: unknown): o is string {\n  return o === 'true' || o === 'false'\n}\n\nexport const isArray = Array.isArray\n\nexport const isWebPlatform = () => process.env.TARO_ENV === 'h5' || process.env.TARO_PLATFORM === 'web'\n", null, "export const View = 'view'\nexport const Icon = 'icon'\nexport const Progress = 'progress'\nexport const RichText = 'rich-text'\nexport const Text = 'text'\nexport const Button = 'button'\nexport const Checkbox = 'checkbox'\nexport const CheckboxGroup = 'checkbox-group'\nexport const Form = 'form'\nexport const Input = 'input'\nexport const Label = 'label'\nexport const Picker = 'picker'\nexport const PickerView = 'picker-view'\nexport const PickerViewColumn = 'picker-view-column'\nexport const Radio = 'radio'\nexport const RadioGroup = 'radio-group'\nexport const Slider = 'slider'\nexport const Switch = 'switch'\nexport const CoverImage = 'cover-image'\nexport const Textarea = 'textarea'\nexport const CoverView = 'cover-view'\nexport const MovableArea = 'movable-area'\nexport const MovableView = 'movable-view'\nexport const ScrollView = 'scroll-view'\nexport const Swiper = 'swiper'\nexport const SwiperItem = 'swiper-item'\nexport const Navigator = 'navigator'\nexport const Audio = 'audio'\nexport const Camera = 'camera'\nexport const Image = 'image'\nexport const LivePlayer = 'live-player'\nexport const Video = 'video'\nexport const Canvas = 'canvas'\nexport const Ad = 'ad'\nexport const WebView = 'web-view'\nexport const Block = 'block'\nexport const Map = 'map'\nexport const Slot = 'slot'\nexport const CustomWrapper = 'custom-wrapper'\n", "// For React.createElement's type\nexport * from '@tarojs/components/mini'\nexport const Lottie = 'lottie'\nexport const Lifestyle = 'lifestyle'\nexport const LifeFollow = 'life-follow'\nexport const ContactButton = 'contact-button'\n", "export const needPromiseApis = new Set([\n  'addCardAuth',\n  'getOpenUserInfo',\n  'chooseAlipayContact',\n  'chooseCity',\n  'chooseContact',\n  'choosePhoneContact',\n  'datePicker',\n  'getAddress',\n  'getAuthCode',\n  'getPhoneNumber',\n  'getRunData',\n  'getRunScene',\n  'getServerTime',\n  'getTitleColor',\n  'rsa',\n  'paySignCenter',\n  'tradePay',\n  'isCollected',\n  'multiLevelSelect',\n  'onLocatedComplete',\n  'optionsSelect',\n  'prompt',\n  'regionPicker',\n  'setLocatedCity',\n  'showAuthGuide',\n  'textRiskIdentification',\n  'vibrate',\n  'watchShake',\n  'connectBLEDevice',\n  'disconnectBLEDevice',\n  'makeBluetoothPair',\n  'writeBLECharacteristicValue',\n  'readBLECharacteristicValue',\n  'notifyBLECharacteristicValueChange',\n  'getBLEDeviceServices',\n  'getBLEDeviceCharacteristics',\n  'openBluetoothAdapter',\n  'closeBluetoothAdapter',\n  'getBluetoothAdapterState',\n  'startBluetoothDevicesDiscovery',\n  'stopBluetoothDevicesDiscovery',\n  'getBluetoothDevices',\n  'getConnectedBluetoothDevices'\n])\n", "import { processApis } from '@tarojs/shared'\nimport { needPromiseApis } from './apis-list'\n\ndeclare const my: any\n\nconst apiDiff = {\n  showActionSheet: {\n    options: {\n      change: [{\n        old: 'itemList',\n        new: 'items'\n      }]\n    }\n  },\n  showToast: {\n    options: {\n      change: [{\n        old: 'title',\n        new: 'content'\n      }, {\n        old: 'icon',\n        new: 'type'\n      }]\n    }\n  },\n  showLoading: {\n    options: {\n      change: [{\n        old: 'title',\n        new: 'content'\n      }]\n    }\n  },\n  setNavigationBarTitle: {\n    alias: 'setNavigationBar'\n  },\n  setNavigationBarColor: {\n    alias: 'setNavigationBar'\n  },\n  saveImageToPhotosAlbum: {\n    alias: 'saveImage',\n    options: {\n      change: [{\n        old: 'filePath',\n        new: 'url'\n      }]\n    }\n  },\n  previewImage: {\n    options: {\n      set: [{\n        key: 'current',\n        value (options) {\n          return options.urls.indexOf(options.current || options.urls[0])\n        }\n      }]\n    }\n  },\n  getFileInfo: {\n    options: {\n      change: [{\n        old: 'filePath',\n        new: 'apFilePath'\n      }]\n    }\n  },\n  getSavedFileInfo: {\n    options: {\n      change: [{\n        old: 'filePath',\n        new: 'apFilePath'\n      }]\n    }\n  },\n  removeSavedFile: {\n    options: {\n      change: [{\n        old: 'filePath',\n        new: 'apFilePath'\n      }]\n    }\n  },\n  saveFile: {\n    options: {\n      change: [{\n        old: 'tempFilePath',\n        new: 'apFilePath'\n      }]\n    }\n  },\n  openLocation: {\n    options: {\n      set: [{\n        key: 'latitude',\n        value (options) {\n          return String(options.latitude)\n        }\n      }, {\n        key: 'longitude',\n        value (options) {\n          return String(options.longitude)\n        }\n      }]\n    }\n  },\n  uploadFile: {\n    options: {\n      change: [{\n        old: 'name',\n        new: 'fileName'\n      }]\n    }\n  },\n  getClipboardData: {\n    alias: 'getClipboard'\n  },\n  setClipboardData: {\n    alias: 'setClipboard',\n    options: {\n      change: [{\n        old: 'data',\n        new: 'text'\n      }]\n    }\n  },\n  makePhoneCall: {\n    options: {\n      change: [{\n        old: 'phoneNumber',\n        new: 'number'\n      }]\n    }\n  },\n  scanCode: {\n    alias: 'scan',\n    options: {\n      change: [{\n        old: 'onlyFromCamera',\n        new: 'hideAlbum'\n      }],\n      set: [{\n        key: 'type',\n        value (options) {\n          return (options.scanType && options.scanType[0].slice(0, -4)) || 'qr'\n        }\n      }]\n    }\n  },\n  setScreenBrightness: {\n    options: {\n      change: [{\n        old: 'value',\n        new: 'brightness'\n      }]\n    }\n  },\n  onBLEConnectionStateChange: {\n    alias: 'onBLEConnectionStateChanged'\n  },\n  offBLEConnectionStateChange: {\n    alias: 'offBLEConnectionStateChanged'\n  },\n  createBLEConnection: {\n    alias: 'connectBLEDevice'\n  },\n  closeBLEConnection: {\n    alias: 'disconnectBLEDevice'\n  }\n}\n\nconst nativeRequest = my.canIUse('request') ? my.request : my.httpRequest\n\nexport function request (options) {\n  options = options || {}\n  if (typeof options === 'string') {\n    options = {\n      url: options\n    }\n  }\n  const defaultHeaders = {\n    'content-type': 'application/json'\n  }\n  options.headers = defaultHeaders\n  if (options.header) {\n    for (const k in options.header) {\n      const lowerK = k.toLocaleLowerCase()\n      options.headers[lowerK] = options.header[k]\n    }\n    delete options.header\n  }\n  const originSuccess = options.success\n  const originFail = options.fail\n  const originComplete = options.complete\n  let requestTask\n  const p: any = new Promise((resolve, reject) => {\n    options.success = res => {\n      res.statusCode = res.status\n      delete res.status\n      res.header = res.headers\n      delete res.headers\n      originSuccess && originSuccess(res)\n      resolve(res)\n    }\n    options.fail = res => {\n      originFail && originFail(res)\n      reject(res)\n    }\n\n    options.complete = res => {\n      originComplete && originComplete(res)\n    }\n\n    requestTask = nativeRequest(options)\n  })\n  p.abort = (cb) => {\n    cb && cb()\n    if (requestTask) {\n      requestTask.abort()\n    }\n    return p\n  }\n  return p\n}\n\nexport function handleSyncApis (key: string, global: Record<string, any>, args: any[]) {\n  if (key === 'getStorageSync') {\n    const arg1 = args[0]\n    if (arg1 != null) {\n      const res = global[key]({ key: arg1 })\n\n      // 支付宝小程序遗留bug：值可能在data或APDataStorage字段下\n      let data = null\n      if (res.hasOwnProperty('data')) {\n        data = res.data\n      } else if (res.hasOwnProperty('APDataStorage')) {\n        data = res.APDataStorage\n      }\n\n      return data === null ? '' : data\n    }\n    return console.error('getStorageSync 传入参数错误')\n  }\n  if (key === 'setStorageSync') {\n    const arg1 = args[0]\n    const arg2 = args[1]\n    if (arg1 != null) {\n      return global[key]({\n        key: arg1,\n        data: arg2\n      })\n    }\n    return console.error('setStorageSync 传入参数错误')\n  }\n  if (key === 'removeStorageSync') {\n    const arg1 = args[0]\n    if (arg1 != null) {\n      return global[key]({ key: arg1 })\n    }\n    return console.error('removeStorageSync 传入参数错误')\n  }\n  if (key === 'createSelectorQuery') {\n    const query = global[key]()\n    query.in = function () { return query }\n    return query\n  }\n  return global[key].apply(global, args)\n}\n\nexport function transformMeta (api: string, options: Record<string, any>) {\n  let apiAlias = api\n  if (api === 'showModal') {\n    options.cancelButtonText = options.cancelText || '取消'\n    options.confirmButtonText = options.confirmText || '确定'\n    apiAlias = 'confirm'\n    if (options.showCancel === false) {\n      options.buttonText = options.confirmText || '确定'\n      apiAlias = 'alert'\n    }\n  } else {\n    Object.keys(apiDiff).forEach(item => {\n      const apiItem = apiDiff[item]\n      if (api === item) {\n        if (apiItem.alias) {\n          apiAlias = apiItem.alias\n        }\n        if (apiItem.options) {\n          const change = apiItem.options.change\n          const set = apiItem.options.set\n          if (change) {\n            change.forEach(changeItem => {\n              options[changeItem.new] = options[changeItem.old]\n            })\n          }\n          if (set) {\n            set.forEach(setItem => {\n              options[setItem.key] = typeof setItem.value === 'function' ? setItem.value(options) : setItem.value\n            })\n          }\n        }\n      }\n    })\n  }\n\n  return {\n    key: apiAlias,\n    options\n  }\n}\n\nexport function modifyApis (apis: Set<string>) {\n  Object.keys(apiDiff).map(key => {\n    apis.add(key)\n    const platformKey = apiDiff[key].alias\n    platformKey && apis.delete(platformKey)\n  })\n  apis.add('showModal')\n  apis.delete('confirm')\n  apis.delete('alert')\n}\n\nexport function modifyAsyncResult (key, res) {\n  if (key === 'saveFile') {\n    res.savedFilePath = res.apFilePath\n  } else if (key === 'downloadFile') {\n    res.tempFilePath = res.apFilePath\n  } else if (key === 'chooseImage') {\n    res.tempFilePaths = res.apFilePaths\n  } else if (key === 'getClipboard') {\n    res.data = res.text\n  } else if (key === 'scan') {\n    res.result = res.code\n  } else if (key === 'getScreenBrightness') {\n    res.value = res.brightness\n    delete res.brightness\n  } else if (key === 'connectSocket') {\n    res.onClose = function (cb) {\n      my.onSocketClose(cb)\n    }\n\n    res.onError = function (cb) {\n      my.onSocketError(cb)\n    }\n\n    res.onMessage = function (cb) {\n      my.onSocketMessage(cb)\n    }\n\n    res.onOpen = function (cb) {\n      my.onSocketOpen(cb)\n    }\n\n    res.send = function (opt) {\n      my.sendSocketMessage(opt)\n    }\n\n    res.close = function () {\n      my.closeSocket()\n    }\n  }\n}\n\nexport function initNativeApi (taro) {\n  processApis(taro, my, {\n    needPromiseApis,\n    handleSyncApis,\n    transformMeta,\n    modifyApis,\n    modifyAsyncResult,\n    request\n  })\n}\n", "import { singleQuote } from '@tarojs/shared'\n\nexport const components = {\n  // ======== 调整属性 ========\n  View: {\n    'disable-scroll': 'false',\n    hidden: 'false',\n    bindAppear: '',\n    bindDisappear: '',\n    bindFirstAppear: ''\n  },\n  Text: {\n    'number-of-lines': ''\n  },\n  Map: {\n    skew: '0',\n    rotate: '0',\n    polygons: '[]',\n    'include-padding': '',\n    'ground-overlays': '',\n    'tile-overlay': '',\n    'custom-map-style': '',\n    setting: '{}',\n    optimize: '',\n    bindRegionChange: '',\n    bindPanelTap: ''\n  },\n  Button: {\n    scope: '',\n    'public-id': '',\n    bindGetAuthorize: '',\n    bindError: ''\n  },\n  Checkbox: {\n    bindChange: ''\n  },\n  Input: {\n    'random-number': 'false',\n    controlled: 'false',\n    enableNative: 'false'\n  },\n  Slider: {\n    'track-size': '4',\n    'handle-size': '22',\n    'handle-color': singleQuote('#ffffff')\n  },\n  Switch: {\n    controlled: 'false'\n  },\n  Textarea: {\n    'show-count': 'true',\n    controlled: 'false',\n    enableNative: 'false'\n  },\n  MovableView: {\n    bindChangeEnd: ''\n  },\n  ScrollView: {\n    'scroll-animation-duration': '',\n    'trap-scroll': 'false'\n  },\n  Swiper: {\n    'active-class': '',\n    'changing-class': '',\n    acceleration: 'false',\n    'disable-programmatic-animation': 'false',\n    'disable-touch': 'false',\n    bindAnimationEnd: ''\n  },\n  Image: {\n    'default-source': ''\n  },\n  Canvas: {\n    type: '',\n    width: singleQuote('300px'),\n    height: singleQuote('225px'),\n    bindReady: ''\n  },\n  Video: {\n    'poster-size': singleQuote('contain'),\n    'mobilenet-hint-type': '1',\n    enableNative: 'false',\n    bindLoading: '',\n    bindUserAction: '',\n    bindStop: '',\n    bindRenderStart: ''\n  },\n  // ======== 额外组件 ========\n  Lottie: {\n    autoplay: 'false',\n    path: '',\n    speed: '1.0',\n    repeatCount: '0',\n    autoReverse: 'false',\n    assetsPath: '',\n    placeholder: '',\n    djangoId: '',\n    md5: '',\n    optimize: 'false',\n    bindDataReady: '',\n    bindDataFailed: '',\n    bindAnimationStart: '',\n    bindAnimationEnd: '',\n    bindAnimationRepeat: '',\n    bindAnimationCancel: '',\n    bindDataLoadReady: ''\n  },\n  Lifestyle: {\n    'public-id': '',\n    memo: '',\n    bindFollow: ''\n  },\n  LifeFollow: {\n    sceneId: '',\n    checkFollow: '',\n    bindCheckFollow: '',\n    bindClose: ''\n  },\n  ContactButton: {\n    'tnt-inst-id': '',\n    scene: '',\n    size: '25',\n    color: singleQuote('#00A3FF'),\n    icon: '',\n    'alipay-card-no': '',\n    'ext-info': ''\n  }\n}\n", "\nimport {\n  initNative<PERSON><PERSON>,\n  handleSync<PERSON><PERSON>,\n  transformMeta,\n  modifyApis,\n  modifyAsyncResult,\n  request\n} from './apis'\n\ndeclare const my: any\n\nconst BUBBLE_EVENTS = new Set([\n  'touchStart',\n  'touchMove',\n  'touchEnd',\n  'touchCancel',\n  'tap',\n  'longTap'\n])\n\nexport {\n  initNativeApi,\n  handleSyncApis,\n  transformMeta,\n  modifyApis,\n  modifyAsyncResult,\n  request\n}\nexport * from './components'\nexport * from './apis-list'\nexport const hostConfig = {\n  initNativeApi,\n  getEventCenter (Events) {\n    if (!my.taroEventCenter) {\n      my.taroEventCenter = new Events()\n    }\n    return my.taroEventCenter\n  },\n  modifyTaroEvent (event, node) {\n    if (node.tagName === 'SWIPER' && event.type === 'animationend') {\n      event.type = 'animationfinish'\n    }\n  },\n  isBubbleEvents (eventName) {\n    return BUBBLE_EVENTS.has(eventName)\n  }\n}\n", "import { mergeReconciler, mergeInternalComponents } from '@tarojs/shared'\nimport { hostConfig, components } from './runtime-utils'\n\n// 支付宝真机只有 navigator.swuserAgent\nconst { userAgent } = navigator\nObject.defineProperty(navigator, 'userAgent', {\n  configurable: true,\n  enumerable: true,\n  get () {\n    return userAgent || (navigator as any).swuserAgent || ''\n  }\n})\n\nmergeReconciler(hostConfig)\nmergeInternalComponents(components)\n", "import { TaroElement, Style, FormElement } from '@tarojs/runtime'\nimport { isFunction, isString, isObject, isNumber, internalComponents, capitalize, toCamelCase } from '@tarojs/shared'\n\nexport type Props = Record<string, unknown>\n\nfunction isEventName (s: string) {\n  return s[0] === 'o' && s[1] === 'n'\n}\n\nconst IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord/i\n\nexport function updateProps (dom: TaroElement, oldProps: Props, newProps: Props) {\n  let i: string\n\n  for (i in oldProps) {\n    if (!(i in newProps)) {\n      setProperty(dom, i, null, oldProps[i])\n    }\n  }\n  const isFormElement = dom instanceof FormElement\n  for (i in newProps) {\n    if (oldProps[i] !== newProps[i] || (isFormElement && i === 'value')) {\n      setProperty(dom, i, newProps[i], oldProps[i])\n    }\n  }\n}\n\n// function eventProxy (e: CommonEvent) {\n//   const el = document.getElementById(e.currentTarget.id)\n//   const handlers = el!.__handlers[e.type]\n//   handlers[0](e)\n// }\n\nfunction setEvent (dom: TaroElement, name: string, value: unknown, oldValue?: unknown) {\n  const isCapture = name.endsWith('Capture')\n  let eventName = name.toLowerCase().slice(2)\n  if (isCapture) {\n    eventName = eventName.slice(0, -7)\n  }\n\n  const compName = capitalize(toCamelCase(dom.tagName.toLowerCase()))\n\n  if (eventName === 'click' && compName in internalComponents) {\n    eventName = 'tap'\n  }\n\n  if (isFunction(value)) {\n    if (!oldValue) {\n      dom.addEventListener(eventName, value, isCapture)\n    }\n    if (eventName === 'regionchange') {\n      dom.__handlers.begin[0] = value\n      dom.__handlers.end[0] = value\n    } else {\n      dom.__handlers[eventName][0] = value\n    }\n  } else {\n    dom.removeEventListener(eventName, oldValue as any)\n  }\n}\n\nfunction setStyle (style: Style, key: string, value: string | number) {\n  if (key[0] === '-') {\n    style.setProperty(key, value.toString())\n  }\n\n  style[key] =\n    isNumber(value) && IS_NON_DIMENSIONAL.test(key) === false\n      ? value + 'px'\n      : value == null\n        ? ''\n        : value\n}\n\ntype StyleValue = Record<string, string | number>\ninterface DangerouslySetInnerHTML {\n  __html?: string\n}\n\nfunction setProperty (dom: TaroElement, name: string, value: unknown, oldValue?: unknown) {\n  name = name === 'className' ? 'class' : name\n\n  if (\n    name === 'key' ||\n    name === 'children' ||\n    name === 'ref'\n  ) {\n    // skip\n  } else if (name === 'style') {\n    const style = dom.style\n    if (isString(value)) {\n      style.cssText = value\n    } else {\n      if (isString(oldValue)) {\n        style.cssText = ''\n        oldValue = null\n      }\n\n      if (isObject<StyleValue>(oldValue)) {\n        for (const i in oldValue) {\n          if (!(value && i in (value as StyleValue))) {\n            setStyle(style, i, '')\n          }\n        }\n      }\n\n      if (isObject<StyleValue>(value)) {\n        for (const i in value) {\n          if (!oldValue || value[i] !== (oldValue as StyleValue)[i]) {\n            setStyle(style, i, value[i])\n          }\n        }\n      }\n    }\n  } else if (isEventName(name)) {\n    setEvent(dom, name, value, oldValue)\n  } else if (name === 'dangerouslySetInnerHTML') {\n    const newHtml = (value as DangerouslySetInnerHTML)?.__html ?? ''\n    const oldHtml = (oldValue as DangerouslySetInnerHTML)?.__html ?? ''\n    if (newHtml || oldHtml) {\n      if (oldHtml !== newHtml) {\n        dom.innerHTML = newHtml\n      }\n    }\n  } else if (!isFunction(value)) {\n    if (value == null) {\n      dom.removeAttribute(name)\n    } else {\n      dom.setAttribute(name, value as string)\n    }\n  }\n}\n", "/* eslint-disable @typescript-eslint/indent */\nimport Reconciler, { HostConfig } from 'react-reconciler'\nimport * as scheduler from 'scheduler'\nimport { TaroElement, TaroText, document } from '@tarojs/runtime'\nimport { noop, EMPTY_ARR } from '@tarojs/shared'\nimport { Props, updateProps } from './props'\n\nconst {\n  unstable_now: now\n} = scheduler\n\nfunction returnFalse () {\n  return false\n}\n\nconst hostConfig: HostConfig<\n  string, // Type\n  Props, // Props\n  TaroElement, // Container\n  TaroElement, // Instance\n  TaroText, // TextInstance\n  TaroElement, // SuspenseInstance\n  TaroElement, // HydratableInstance\n  TaroElement, // PublicInstance\n  Record<string, any>, // HostContext\n  string[], // UpdatePayload\n  unknown, // ChildSet\n  unknown, // TimeoutHandle\n  unknown // NoTimeout\n> & {\n  hideInstance (instance: TaroElement): void\n  unhideInstance (instance: TaroElement, props): void\n} = {\n  createInstance (type) {\n    return document.createElement(type)\n  },\n\n  createTextInstance (text) {\n    return document.createTextNode(text)\n  },\n\n  getPublicInstance (inst: TaroElement) {\n    return inst\n  },\n\n  getRootHostContext () {\n    return {}\n  },\n\n  getChildHostContext () {\n    return {}\n  },\n\n  appendChild (parent, child) {\n    parent.appendChild(child)\n  },\n\n  appendInitialChild (parent, child) {\n    parent.appendChild(child)\n  },\n\n  appendChildToContainer (parent, child) {\n    parent.appendChild(child)\n  },\n\n  removeChild (parent, child) {\n    parent.removeChild(child)\n  },\n\n  removeChildFromContainer (parent, child) {\n    parent.removeChild(child)\n  },\n\n  insertBefore (parent, child, refChild) {\n    parent.insertBefore(child, refChild)\n  },\n\n  insertInContainerBefore (parent, child, refChild) {\n    parent.insertBefore(child, refChild)\n  },\n\n  commitTextUpdate (textInst, _, newText) {\n    textInst.nodeValue = newText\n  },\n\n  finalizeInitialChildren (dom, _, props) {\n    updateProps(dom, {}, props)\n    return false\n  },\n\n  prepareUpdate () {\n    return EMPTY_ARR\n  },\n\n  commitUpdate (dom, _payload, _type, oldProps, newProps) {\n    updateProps(dom, oldProps, newProps)\n  },\n\n  hideInstance (instance) {\n    const style = instance.style\n    style.setProperty('display', 'none')\n  },\n\n  unhideInstance (instance, props) {\n    const styleProp = props.style\n    let display = styleProp?.hasOwnProperty('display') ? styleProp.display : null\n    display = display == null || typeof display === 'boolean' || display === '' ? '' : ('' + display).trim()\n    // eslint-disable-next-line dot-notation\n    instance.style['display'] = display\n  },\n\n  clearContainer (element) {\n    if (element.childNodes.length > 0) {\n      element.textContent = ''\n    }\n  },\n\n  queueMicrotask: typeof Promise !== 'undefined'\n  ? callback =>\n      Promise.resolve(null)\n        .then(callback)\n        .catch(function (error) {\n          setTimeout(() => {\n            throw error\n          })\n        })\n  : setTimeout,\n\n  shouldSetTextContent: returnFalse,\n  prepareForCommit (..._: any[]) { return null },\n  resetAfterCommit: noop,\n  commitMount: noop,\n  now,\n  cancelTimeout: clearTimeout,\n  scheduleTimeout: setTimeout,\n  preparePortalMount: noop,\n  noTimeout: -1,\n  supportsMutation: true,\n  supportsPersistence: false,\n  isPrimaryRenderer: true,\n  supportsHydration: false\n}\n\nconst TaroReconciler = Reconciler(hostConfig)\n\nif (process.env.NODE_ENV !== 'production') {\n  const foundDevTools = TaroReconciler.injectIntoDevTools({\n    bundleType: 1,\n    version: '17.0.2',\n    rendererPackageName: 'taro-react'\n  })\n  if (!foundDevTools) {\n    // eslint-disable-next-line no-console\n    console.info('%cDownload the React DevTools ' + 'for a better development experience: ' + 'https://reactjs.org/link/react-devtools', 'font-weight:bold')\n  }\n}\n\nexport {\n  TaroReconciler\n}\n", "import { TaroElement } from '@tarojs/runtime'\nimport { ReactNode } from 'react'\nimport { TaroReconciler } from './reconciler'\nimport { OpaqueRoot } from 'react-reconciler'\n\nexport const ContainerMap: WeakMap<TaroElement, Root> = new WeakMap()\n\ntype Renderer = typeof TaroReconciler\n\nexport type Callback = () => void | null | undefined\n\nclass Root {\n  private renderer: Renderer\n  private internalRoot: OpaqueRoot\n\n  public constructor (renderer: Renderer, domContainer: TaroElement) {\n    this.renderer = renderer\n    this.internalRoot = renderer.createContainer(domContainer, 0/** LegacyRoot: react-reconciler/src/ReactRootTags.js */, false, null)\n  }\n\n  public render (children: ReactNode, cb: Callback) {\n    this.renderer.updateContainer(children, this.internalRoot, null, cb)\n\n    return this.renderer.getPublicRootInstance(this.internalRoot)\n  }\n\n  public unmount (cb: Callback) {\n    this.renderer.updateContainer(null, this.internalRoot, null, cb)\n  }\n}\n\nexport function render (element: ReactNode, domContainer: TaroElement, cb: Callback) {\n  const oldRoot = ContainerMap.get(domContainer)\n  if (oldRoot != null) {\n    return oldRoot.render(element, cb)\n  }\n\n  const root = new Root(TaroReconciler, domContainer)\n  ContainerMap.set(domContainer, root)\n  return root.render(element, cb)\n}\n", "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport { render, ContainerMap } from './render'\nimport { <PERSON>roReconciler } from './reconciler'\nimport { TaroElement } from '@tarojs/runtime'\nimport { ReactNode } from 'react'\nimport { ensure } from '@tarojs/shared'\n\nconst unstable_batchedUpdates = TaroReconciler.batchedUpdates\n\nfunction unmountComponentAtNode (dom: TaroElement) {\n  ensure(dom && [1, 8, 9, 11].includes(dom.nodeType), 'unmountComponentAtNode(...): Target container is not a DOM element.')\n\n  const root = ContainerMap.get(dom)\n\n  if (!root) return false\n\n  unstable_batchedUpdates(() => {\n    root.unmount(() => {\n      ContainerMap.delete(dom)\n    })\n  }, null)\n\n  return true\n}\n\nfunction findDOMNode (comp?: TaroElement | ReactNode) {\n  if (comp == null) {\n    return null\n  }\n\n  const nodeType = (comp as TaroElement).nodeType\n  if (nodeType === 1 || nodeType === 3) {\n    return comp\n  }\n\n  return TaroReconciler.findHostInstance(comp as Record<string, any>)\n}\n\nconst portalType = typeof Symbol === 'function' && Symbol.for\n  ? Symbol.for('react.portal')\n  : 0xeaca\n\nfunction createPortal (\n  children: ReactNode,\n  containerInfo: TaroElement,\n  key?: string\n) {\n  return {\n    $$typeof: portalType,\n    key: key == null ? null : String(key),\n    children,\n    containerInfo,\n    implementation: null\n  }\n}\n\nexport {\n  render,\n  unstable_batchedUpdates,\n  unmountComponentAtNode,\n  findDOMNode,\n  createPortal\n}\n\nexport default {\n  render,\n  unstable_batchedUpdates,\n  unmountComponentAtNode,\n  findDOMNode,\n  createPortal\n}\n", "/*! *****************************************************************************\nCopyright (C) Microsoft. All rights reserved.\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\nthis file except in compliance with the License. You may obtain a copy of the\nLicense at http://www.apache.org/licenses/LICENSE-2.0\n\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\nMERCHANTABLITY OR NON-INFRINGEMENT.\n\nSee the Apache Version 2.0 License for specific language governing permissions\nand limitations under the License.\n***************************************************************************** */\n\n/** https://github.com/rbuckton/reflect-metadata */\n\nif (process.env.TARO_ENV === 'h5') {\n  require('reflect-metadata')\n} else {\n  // var Reflect;\n  (function (Reflect) {\n      // Metadata Proposal\n      // https://rbuckton.github.io/reflect-metadata/\n      (function (factory) {\n          // var root = typeof global === \"object\" ? global :\n          //     typeof self === \"object\" ? self :\n          //         typeof this === \"object\" ? this :\n          //             Function(\"return this;\")();\n          var exporter = makeExporter(Reflect);\n          // if (typeof root.Reflect === \"undefined\") {\n          //     root.Reflect = Reflect;\n          // }\n          // else {\n          //     exporter = makeExporter(root.Reflect, exporter);\n          // }\n          factory(exporter);\n          function makeExporter(target, previous) {\n              return function (key, value) {\n                  if (typeof target[key] !== \"function\") {\n                      Object.defineProperty(target, key, { configurable: true, writable: true, value: value });\n                  }\n                  if (previous)\n                      previous(key, value);\n              };\n          }\n      })(function (exporter) {\n          var hasOwn = Object.prototype.hasOwnProperty;\n          // feature test for Symbol support\n          var supportsSymbol = typeof Symbol === \"function\";\n          var toPrimitiveSymbol = supportsSymbol && typeof Symbol.toPrimitive !== \"undefined\" ? Symbol.toPrimitive : \"@@toPrimitive\";\n          var iteratorSymbol = supportsSymbol && typeof Symbol.iterator !== \"undefined\" ? Symbol.iterator : \"@@iterator\";\n          var supportsCreate = typeof Object.create === \"function\"; // feature test for Object.create support\n          var supportsProto = { __proto__: [] } instanceof Array; // feature test for __proto__ support\n          var downLevel = !supportsCreate && !supportsProto;\n          var HashMap = {\n              // create an object in dictionary mode (a.k.a. \"slow\" mode in v8)\n              create: supportsCreate\n                  ? function () { return MakeDictionary(Object.create(null)); }\n                  : supportsProto\n                      ? function () { return MakeDictionary({ __proto__: null }); }\n                      : function () { return MakeDictionary({}); },\n              has: downLevel\n                  ? function (map, key) { return hasOwn.call(map, key); }\n                  : function (map, key) { return key in map; },\n              get: downLevel\n                  ? function (map, key) { return hasOwn.call(map, key) ? map[key] : undefined; }\n                  : function (map, key) { return map[key]; },\n          };\n          // Load global or shim versions of Map, Set, and WeakMap\n          var functionPrototype = Object.getPrototypeOf(Function);\n          var usePolyfill = typeof process === \"object\" && process.env && process.env[\"REFLECT_METADATA_USE_MAP_POLYFILL\"] === \"true\";\n          var _Map = !usePolyfill && typeof Map === \"function\" && typeof Map.prototype.entries === \"function\" ? Map : CreateMapPolyfill();\n          var _Set = !usePolyfill && typeof Set === \"function\" && typeof Set.prototype.entries === \"function\" ? Set : CreateSetPolyfill();\n          var _WeakMap = !usePolyfill && typeof WeakMap === \"function\" ? WeakMap : CreateWeakMapPolyfill();\n          // [[Metadata]] internal slot\n          // https://rbuckton.github.io/reflect-metadata/#ordinary-object-internal-methods-and-internal-slots\n          var Metadata = new _WeakMap();\n          /**\n           * Applies a set of decorators to a property of a target object.\n           * @param decorators An array of decorators.\n           * @param target The target object.\n           * @param propertyKey (Optional) The property key to decorate.\n           * @param attributes (Optional) The property descriptor for the target key.\n           * @remarks Decorators are applied in reverse order.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     Example = Reflect.decorate(decoratorsArray, Example);\n           *\n           *     // property (on constructor)\n           *     Reflect.decorate(decoratorsArray, Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     Reflect.decorate(decoratorsArray, Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     Object.defineProperty(Example, \"staticMethod\",\n           *         Reflect.decorate(decoratorsArray, Example, \"staticMethod\",\n           *             Object.getOwnPropertyDescriptor(Example, \"staticMethod\")));\n           *\n           *     // method (on prototype)\n           *     Object.defineProperty(Example.prototype, \"method\",\n           *         Reflect.decorate(decoratorsArray, Example.prototype, \"method\",\n           *             Object.getOwnPropertyDescriptor(Example.prototype, \"method\")));\n           *\n           */\n          function decorate(decorators, target, propertyKey, attributes) {\n              if (!IsUndefined(propertyKey)) {\n                  if (!IsArray(decorators))\n                      throw new TypeError();\n                  if (!IsObject(target))\n                      throw new TypeError();\n                  if (!IsObject(attributes) && !IsUndefined(attributes) && !IsNull(attributes))\n                      throw new TypeError();\n                  if (IsNull(attributes))\n                      attributes = undefined;\n                  propertyKey = ToPropertyKey(propertyKey);\n                  return DecorateProperty(decorators, target, propertyKey, attributes);\n              }\n              else {\n                  if (!IsArray(decorators))\n                      throw new TypeError();\n                  if (!IsConstructor(target))\n                      throw new TypeError();\n                  return DecorateConstructor(decorators, target);\n              }\n          }\n          exporter(\"decorate\", decorate);\n          // 4.1.2 Reflect.metadata(metadataKey, metadataValue)\n          // https://rbuckton.github.io/reflect-metadata/#reflect.metadata\n          /**\n           * A default metadata decorator factory that can be used on a class, class member, or parameter.\n           * @param metadataKey The key for the metadata entry.\n           * @param metadataValue The value for the metadata entry.\n           * @returns A decorator function.\n           * @remarks\n           * If `metadataKey` is already defined for the target and target key, the\n           * metadataValue for that key will be overwritten.\n           * @example\n           *\n           *     // constructor\n           *     @Reflect.metadata(key, value)\n           *     class Example {\n           *     }\n           *\n           *     // property (on constructor, TypeScript only)\n           *     class Example {\n           *         @Reflect.metadata(key, value)\n           *         static staticProperty;\n           *     }\n           *\n           *     // property (on prototype, TypeScript only)\n           *     class Example {\n           *         @Reflect.metadata(key, value)\n           *         property;\n           *     }\n           *\n           *     // method (on constructor)\n           *     class Example {\n           *         @Reflect.metadata(key, value)\n           *         static staticMethod() { }\n           *     }\n           *\n           *     // method (on prototype)\n           *     class Example {\n           *         @Reflect.metadata(key, value)\n           *         method() { }\n           *     }\n           *\n           */\n          function metadata(metadataKey, metadataValue) {\n              function decorator(target, propertyKey) {\n                  if (!IsObject(target))\n                      throw new TypeError();\n                  if (!IsUndefined(propertyKey) && !IsPropertyKey(propertyKey))\n                      throw new TypeError();\n                  OrdinaryDefineOwnMetadata(metadataKey, metadataValue, target, propertyKey);\n              }\n              return decorator;\n          }\n          exporter(\"metadata\", metadata);\n          /**\n           * Define a unique metadata entry on the target.\n           * @param metadataKey A key used to store and retrieve metadata.\n           * @param metadataValue A value that contains attached metadata.\n           * @param target The target object on which to define metadata.\n           * @param propertyKey (Optional) The property key for the target.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     Reflect.defineMetadata(\"custom:annotation\", options, Example);\n           *\n           *     // property (on constructor)\n           *     Reflect.defineMetadata(\"custom:annotation\", options, Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     Reflect.defineMetadata(\"custom:annotation\", options, Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     Reflect.defineMetadata(\"custom:annotation\", options, Example, \"staticMethod\");\n           *\n           *     // method (on prototype)\n           *     Reflect.defineMetadata(\"custom:annotation\", options, Example.prototype, \"method\");\n           *\n           *     // decorator factory as metadata-producing annotation.\n           *     function MyAnnotation(options): Decorator {\n           *         return (target, key?) => Reflect.defineMetadata(\"custom:annotation\", options, target, key);\n           *     }\n           *\n           */\n          function defineMetadata(metadataKey, metadataValue, target, propertyKey) {\n              if (!IsObject(target))\n                  throw new TypeError();\n              if (!IsUndefined(propertyKey))\n                  propertyKey = ToPropertyKey(propertyKey);\n              return OrdinaryDefineOwnMetadata(metadataKey, metadataValue, target, propertyKey);\n          }\n          exporter(\"defineMetadata\", defineMetadata);\n          /**\n           * Gets a value indicating whether the target object or its prototype chain has the provided metadata key defined.\n           * @param metadataKey A key used to store and retrieve metadata.\n           * @param target The target object on which the metadata is defined.\n           * @param propertyKey (Optional) The property key for the target.\n           * @returns `true` if the metadata key was defined on the target object or its prototype chain; otherwise, `false`.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     result = Reflect.hasMetadata(\"custom:annotation\", Example);\n           *\n           *     // property (on constructor)\n           *     result = Reflect.hasMetadata(\"custom:annotation\", Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     result = Reflect.hasMetadata(\"custom:annotation\", Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     result = Reflect.hasMetadata(\"custom:annotation\", Example, \"staticMethod\");\n           *\n           *     // method (on prototype)\n           *     result = Reflect.hasMetadata(\"custom:annotation\", Example.prototype, \"method\");\n           *\n           */\n          function hasMetadata(metadataKey, target, propertyKey) {\n              if (!IsObject(target))\n                  throw new TypeError();\n              if (!IsUndefined(propertyKey))\n                  propertyKey = ToPropertyKey(propertyKey);\n              return OrdinaryHasMetadata(metadataKey, target, propertyKey);\n          }\n          exporter(\"hasMetadata\", hasMetadata);\n          /**\n           * Gets a value indicating whether the target object has the provided metadata key defined.\n           * @param metadataKey A key used to store and retrieve metadata.\n           * @param target The target object on which the metadata is defined.\n           * @param propertyKey (Optional) The property key for the target.\n           * @returns `true` if the metadata key was defined on the target object; otherwise, `false`.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example);\n           *\n           *     // property (on constructor)\n           *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example, \"staticMethod\");\n           *\n           *     // method (on prototype)\n           *     result = Reflect.hasOwnMetadata(\"custom:annotation\", Example.prototype, \"method\");\n           *\n           */\n          function hasOwnMetadata(metadataKey, target, propertyKey) {\n              if (!IsObject(target))\n                  throw new TypeError();\n              if (!IsUndefined(propertyKey))\n                  propertyKey = ToPropertyKey(propertyKey);\n              return OrdinaryHasOwnMetadata(metadataKey, target, propertyKey);\n          }\n          exporter(\"hasOwnMetadata\", hasOwnMetadata);\n          /**\n           * Gets the metadata value for the provided metadata key on the target object or its prototype chain.\n           * @param metadataKey A key used to store and retrieve metadata.\n           * @param target The target object on which the metadata is defined.\n           * @param propertyKey (Optional) The property key for the target.\n           * @returns The metadata value for the metadata key if found; otherwise, `undefined`.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     result = Reflect.getMetadata(\"custom:annotation\", Example);\n           *\n           *     // property (on constructor)\n           *     result = Reflect.getMetadata(\"custom:annotation\", Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     result = Reflect.getMetadata(\"custom:annotation\", Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     result = Reflect.getMetadata(\"custom:annotation\", Example, \"staticMethod\");\n           *\n           *     // method (on prototype)\n           *     result = Reflect.getMetadata(\"custom:annotation\", Example.prototype, \"method\");\n           *\n           */\n          function getMetadata(metadataKey, target, propertyKey) {\n              if (!IsObject(target))\n                  throw new TypeError();\n              if (!IsUndefined(propertyKey))\n                  propertyKey = ToPropertyKey(propertyKey);\n              return OrdinaryGetMetadata(metadataKey, target, propertyKey);\n          }\n          exporter(\"getMetadata\", getMetadata);\n          /**\n           * Gets the metadata value for the provided metadata key on the target object.\n           * @param metadataKey A key used to store and retrieve metadata.\n           * @param target The target object on which the metadata is defined.\n           * @param propertyKey (Optional) The property key for the target.\n           * @returns The metadata value for the metadata key if found; otherwise, `undefined`.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example);\n           *\n           *     // property (on constructor)\n           *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example, \"staticMethod\");\n           *\n           *     // method (on prototype)\n           *     result = Reflect.getOwnMetadata(\"custom:annotation\", Example.prototype, \"method\");\n           *\n           */\n          function getOwnMetadata(metadataKey, target, propertyKey) {\n              if (!IsObject(target))\n                  throw new TypeError();\n              if (!IsUndefined(propertyKey))\n                  propertyKey = ToPropertyKey(propertyKey);\n              return OrdinaryGetOwnMetadata(metadataKey, target, propertyKey);\n          }\n          exporter(\"getOwnMetadata\", getOwnMetadata);\n          /**\n           * Gets the metadata keys defined on the target object or its prototype chain.\n           * @param target The target object on which the metadata is defined.\n           * @param propertyKey (Optional) The property key for the target.\n           * @returns An array of unique metadata keys.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     result = Reflect.getMetadataKeys(Example);\n           *\n           *     // property (on constructor)\n           *     result = Reflect.getMetadataKeys(Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     result = Reflect.getMetadataKeys(Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     result = Reflect.getMetadataKeys(Example, \"staticMethod\");\n           *\n           *     // method (on prototype)\n           *     result = Reflect.getMetadataKeys(Example.prototype, \"method\");\n           *\n           */\n          function getMetadataKeys(target, propertyKey) {\n              if (!IsObject(target))\n                  throw new TypeError();\n              if (!IsUndefined(propertyKey))\n                  propertyKey = ToPropertyKey(propertyKey);\n              return OrdinaryMetadataKeys(target, propertyKey);\n          }\n          exporter(\"getMetadataKeys\", getMetadataKeys);\n          /**\n           * Gets the unique metadata keys defined on the target object.\n           * @param target The target object on which the metadata is defined.\n           * @param propertyKey (Optional) The property key for the target.\n           * @returns An array of unique metadata keys.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     result = Reflect.getOwnMetadataKeys(Example);\n           *\n           *     // property (on constructor)\n           *     result = Reflect.getOwnMetadataKeys(Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     result = Reflect.getOwnMetadataKeys(Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     result = Reflect.getOwnMetadataKeys(Example, \"staticMethod\");\n           *\n           *     // method (on prototype)\n           *     result = Reflect.getOwnMetadataKeys(Example.prototype, \"method\");\n           *\n           */\n          function getOwnMetadataKeys(target, propertyKey) {\n              if (!IsObject(target))\n                  throw new TypeError();\n              if (!IsUndefined(propertyKey))\n                  propertyKey = ToPropertyKey(propertyKey);\n              return OrdinaryOwnMetadataKeys(target, propertyKey);\n          }\n          exporter(\"getOwnMetadataKeys\", getOwnMetadataKeys);\n          /**\n           * Deletes the metadata entry from the target object with the provided key.\n           * @param metadataKey A key used to store and retrieve metadata.\n           * @param target The target object on which the metadata is defined.\n           * @param propertyKey (Optional) The property key for the target.\n           * @returns `true` if the metadata entry was found and deleted; otherwise, false.\n           * @example\n           *\n           *     class Example {\n           *         // property declarations are not part of ES6, though they are valid in TypeScript:\n           *         // static staticProperty;\n           *         // property;\n           *\n           *         constructor(p) { }\n           *         static staticMethod(p) { }\n           *         method(p) { }\n           *     }\n           *\n           *     // constructor\n           *     result = Reflect.deleteMetadata(\"custom:annotation\", Example);\n           *\n           *     // property (on constructor)\n           *     result = Reflect.deleteMetadata(\"custom:annotation\", Example, \"staticProperty\");\n           *\n           *     // property (on prototype)\n           *     result = Reflect.deleteMetadata(\"custom:annotation\", Example.prototype, \"property\");\n           *\n           *     // method (on constructor)\n           *     result = Reflect.deleteMetadata(\"custom:annotation\", Example, \"staticMethod\");\n           *\n           *     // method (on prototype)\n           *     result = Reflect.deleteMetadata(\"custom:annotation\", Example.prototype, \"method\");\n           *\n           */\n          function deleteMetadata(metadataKey, target, propertyKey) {\n              if (!IsObject(target))\n                  throw new TypeError();\n              if (!IsUndefined(propertyKey))\n                  propertyKey = ToPropertyKey(propertyKey);\n              var metadataMap = GetOrCreateMetadataMap(target, propertyKey, /*Create*/ false);\n              if (IsUndefined(metadataMap))\n                  return false;\n              if (!metadataMap.delete(metadataKey))\n                  return false;\n              if (metadataMap.size > 0)\n                  return true;\n              var targetMetadata = Metadata.get(target);\n              targetMetadata.delete(propertyKey);\n              if (targetMetadata.size > 0)\n                  return true;\n              Metadata.delete(target);\n              return true;\n          }\n          exporter(\"deleteMetadata\", deleteMetadata);\n          function DecorateConstructor(decorators, target) {\n              for (var i = decorators.length - 1; i >= 0; --i) {\n                  var decorator = decorators[i];\n                  var decorated = decorator(target);\n                  if (!IsUndefined(decorated) && !IsNull(decorated)) {\n                      if (!IsConstructor(decorated))\n                          throw new TypeError();\n                      target = decorated;\n                  }\n              }\n              return target;\n          }\n          function DecorateProperty(decorators, target, propertyKey, descriptor) {\n              for (var i = decorators.length - 1; i >= 0; --i) {\n                  var decorator = decorators[i];\n                  var decorated = decorator(target, propertyKey, descriptor);\n                  if (!IsUndefined(decorated) && !IsNull(decorated)) {\n                      if (!IsObject(decorated))\n                          throw new TypeError();\n                      descriptor = decorated;\n                  }\n              }\n              return descriptor;\n          }\n          function GetOrCreateMetadataMap(O, P, Create) {\n              var targetMetadata = Metadata.get(O);\n              if (IsUndefined(targetMetadata)) {\n                  if (!Create)\n                      return undefined;\n                  targetMetadata = new _Map();\n                  Metadata.set(O, targetMetadata);\n              }\n              var metadataMap = targetMetadata.get(P);\n              if (IsUndefined(metadataMap)) {\n                  if (!Create)\n                      return undefined;\n                  metadataMap = new _Map();\n                  targetMetadata.set(P, metadataMap);\n              }\n              return metadataMap;\n          }\n          // ******* OrdinaryHasMetadata(MetadataKey, O, P)\n          // https://rbuckton.github.io/reflect-metadata/#ordinaryhasmetadata\n          function OrdinaryHasMetadata(MetadataKey, O, P) {\n              var hasOwn = OrdinaryHasOwnMetadata(MetadataKey, O, P);\n              if (hasOwn)\n                  return true;\n              var parent = OrdinaryGetPrototypeOf(O);\n              if (!IsNull(parent))\n                  return OrdinaryHasMetadata(MetadataKey, parent, P);\n              return false;\n          }\n          // 3.1.2.1 OrdinaryHasOwnMetadata(MetadataKey, O, P)\n          // https://rbuckton.github.io/reflect-metadata/#ordinaryhasownmetadata\n          function OrdinaryHasOwnMetadata(MetadataKey, O, P) {\n              var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ false);\n              if (IsUndefined(metadataMap))\n                  return false;\n              return ToBoolean(metadataMap.has(MetadataKey));\n          }\n          // 3.1.3.1 OrdinaryGetMetadata(MetadataKey, O, P)\n          // https://rbuckton.github.io/reflect-metadata/#ordinarygetmetadata\n          function OrdinaryGetMetadata(MetadataKey, O, P) {\n              var hasOwn = OrdinaryHasOwnMetadata(MetadataKey, O, P);\n              if (hasOwn)\n                  return OrdinaryGetOwnMetadata(MetadataKey, O, P);\n              var parent = OrdinaryGetPrototypeOf(O);\n              if (!IsNull(parent))\n                  return OrdinaryGetMetadata(MetadataKey, parent, P);\n              return undefined;\n          }\n          // 3.1.4.1 OrdinaryGetOwnMetadata(MetadataKey, O, P)\n          // https://rbuckton.github.io/reflect-metadata/#ordinarygetownmetadata\n          function OrdinaryGetOwnMetadata(MetadataKey, O, P) {\n              var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ false);\n              if (IsUndefined(metadataMap))\n                  return undefined;\n              return metadataMap.get(MetadataKey);\n          }\n          // 3.1.5.1 OrdinaryDefineOwnMetadata(MetadataKey, MetadataValue, O, P)\n          // https://rbuckton.github.io/reflect-metadata/#ordinarydefineownmetadata\n          function OrdinaryDefineOwnMetadata(MetadataKey, MetadataValue, O, P) {\n              var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ true);\n              metadataMap.set(MetadataKey, MetadataValue);\n          }\n          // 3.1.6.1 OrdinaryMetadataKeys(O, P)\n          // https://rbuckton.github.io/reflect-metadata/#ordinarymetadatakeys\n          function OrdinaryMetadataKeys(O, P) {\n              var ownKeys = OrdinaryOwnMetadataKeys(O, P);\n              var parent = OrdinaryGetPrototypeOf(O);\n              if (parent === null)\n                  return ownKeys;\n              var parentKeys = OrdinaryMetadataKeys(parent, P);\n              if (parentKeys.length <= 0)\n                  return ownKeys;\n              if (ownKeys.length <= 0)\n                  return parentKeys;\n              var set = new _Set();\n              var keys = [];\n              for (var _i = 0, ownKeys_1 = ownKeys; _i < ownKeys_1.length; _i++) {\n                  var key = ownKeys_1[_i];\n                  var hasKey = set.has(key);\n                  if (!hasKey) {\n                      set.add(key);\n                      keys.push(key);\n                  }\n              }\n              for (var _a = 0, parentKeys_1 = parentKeys; _a < parentKeys_1.length; _a++) {\n                  var key = parentKeys_1[_a];\n                  var hasKey = set.has(key);\n                  if (!hasKey) {\n                      set.add(key);\n                      keys.push(key);\n                  }\n              }\n              return keys;\n          }\n          // 3.1.7.1 OrdinaryOwnMetadataKeys(O, P)\n          // https://rbuckton.github.io/reflect-metadata/#ordinaryownmetadatakeys\n          function OrdinaryOwnMetadataKeys(O, P) {\n              var keys = [];\n              var metadataMap = GetOrCreateMetadataMap(O, P, /*Create*/ false);\n              if (IsUndefined(metadataMap))\n                  return keys;\n              var keysObj = metadataMap.keys();\n              var iterator = GetIterator(keysObj);\n              var k = 0;\n              while (true) {\n                  var next = IteratorStep(iterator);\n                  if (!next) {\n                      keys.length = k;\n                      return keys;\n                  }\n                  var nextValue = IteratorValue(next);\n                  try {\n                      keys[k] = nextValue;\n                  }\n                  catch (e) {\n                      try {\n                          IteratorClose(iterator);\n                      }\n                      finally {\n                          throw e;\n                      }\n                  }\n                  k++;\n              }\n          }\n          // 6 ECMAScript Data Typ0es and Values\n          // https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values\n          function Type(x) {\n              if (x === null)\n                  return 1 /* Null */;\n              switch (typeof x) {\n                  case \"undefined\": return 0 /* Undefined */;\n                  case \"boolean\": return 2 /* Boolean */;\n                  case \"string\": return 3 /* String */;\n                  case \"symbol\": return 4 /* Symbol */;\n                  case \"number\": return 5 /* Number */;\n                  case \"object\": return x === null ? 1 /* Null */ : 6 /* Object */;\n                  default: return 6 /* Object */;\n              }\n          }\n          // 6.1.1 The Undefined Type\n          // https://tc39.github.io/ecma262/#sec-ecmascript-language-types-undefined-type\n          function IsUndefined(x) {\n              return x === undefined;\n          }\n          // 6.1.2 The Null Type\n          // https://tc39.github.io/ecma262/#sec-ecmascript-language-types-null-type\n          function IsNull(x) {\n              return x === null;\n          }\n          // 6.1.5 The Symbol Type\n          // https://tc39.github.io/ecma262/#sec-ecmascript-language-types-symbol-type\n          function IsSymbol(x) {\n              return typeof x === \"symbol\";\n          }\n          // 6.1.7 The Object Type\n          // https://tc39.github.io/ecma262/#sec-object-type\n          function IsObject(x) {\n              return typeof x === \"object\" ? x !== null : typeof x === \"function\";\n          }\n          // 7.1 Type Conversion\n          // https://tc39.github.io/ecma262/#sec-type-conversion\n          // 7.1.1 ToPrimitive(input [, PreferredType])\n          // https://tc39.github.io/ecma262/#sec-toprimitive\n          function ToPrimitive(input, PreferredType) {\n              switch (Type(input)) {\n                  case 0 /* Undefined */: return input;\n                  case 1 /* Null */: return input;\n                  case 2 /* Boolean */: return input;\n                  case 3 /* String */: return input;\n                  case 4 /* Symbol */: return input;\n                  case 5 /* Number */: return input;\n              }\n              var hint = PreferredType === 3 /* String */ ? \"string\" : PreferredType === 5 /* Number */ ? \"number\" : \"default\";\n              var exoticToPrim = GetMethod(input, toPrimitiveSymbol);\n              if (exoticToPrim !== undefined) {\n                  var result = exoticToPrim.call(input, hint);\n                  if (IsObject(result))\n                      throw new TypeError();\n                  return result;\n              }\n              return OrdinaryToPrimitive(input, hint === \"default\" ? \"number\" : hint);\n          }\n          // 7.1.1.1 OrdinaryToPrimitive(O, hint)\n          // https://tc39.github.io/ecma262/#sec-ordinarytoprimitive\n          function OrdinaryToPrimitive(O, hint) {\n              if (hint === \"string\") {\n                  var toString_1 = O.toString;\n                  if (IsCallable(toString_1)) {\n                      var result = toString_1.call(O);\n                      if (!IsObject(result))\n                          return result;\n                  }\n                  var valueOf = O.valueOf;\n                  if (IsCallable(valueOf)) {\n                      var result = valueOf.call(O);\n                      if (!IsObject(result))\n                          return result;\n                  }\n              }\n              else {\n                  var valueOf = O.valueOf;\n                  if (IsCallable(valueOf)) {\n                      var result = valueOf.call(O);\n                      if (!IsObject(result))\n                          return result;\n                  }\n                  var toString_2 = O.toString;\n                  if (IsCallable(toString_2)) {\n                      var result = toString_2.call(O);\n                      if (!IsObject(result))\n                          return result;\n                  }\n              }\n              throw new TypeError();\n          }\n          // 7.1.2 ToBoolean(argument)\n          // https://tc39.github.io/ecma262/2016/#sec-toboolean\n          function ToBoolean(argument) {\n              return !!argument;\n          }\n          // 7.1.12 ToString(argument)\n          // https://tc39.github.io/ecma262/#sec-tostring\n          function ToString(argument) {\n              return \"\" + argument;\n          }\n          // 7.1.14 ToPropertyKey(argument)\n          // https://tc39.github.io/ecma262/#sec-topropertykey\n          function ToPropertyKey(argument) {\n              var key = ToPrimitive(argument, 3 /* String */);\n              if (IsSymbol(key))\n                  return key;\n              return ToString(key);\n          }\n          // 7.2 Testing and Comparison Operations\n          // https://tc39.github.io/ecma262/#sec-testing-and-comparison-operations\n          // 7.2.2 IsArray(argument)\n          // https://tc39.github.io/ecma262/#sec-isarray\n          function IsArray(argument) {\n              return Array.isArray\n                  ? Array.isArray(argument)\n                  : argument instanceof Object\n                      ? argument instanceof Array\n                      : Object.prototype.toString.call(argument) === \"[object Array]\";\n          }\n          // 7.2.3 IsCallable(argument)\n          // https://tc39.github.io/ecma262/#sec-iscallable\n          function IsCallable(argument) {\n              // NOTE: This is an approximation as we cannot check for [[Call]] internal method.\n              return typeof argument === \"function\";\n          }\n          // 7.2.4 IsConstructor(argument)\n          // https://tc39.github.io/ecma262/#sec-isconstructor\n          function IsConstructor(argument) {\n              // NOTE: This is an approximation as we cannot check for [[Construct]] internal method.\n              return typeof argument === \"function\";\n          }\n          // 7.2.7 IsPropertyKey(argument)\n          // https://tc39.github.io/ecma262/#sec-ispropertykey\n          function IsPropertyKey(argument) {\n              switch (Type(argument)) {\n                  case 3 /* String */: return true;\n                  case 4 /* Symbol */: return true;\n                  default: return false;\n              }\n          }\n          // 7.3 Operations on Objects\n          // https://tc39.github.io/ecma262/#sec-operations-on-objects\n          // 7.3.9 GetMethod(V, P)\n          // https://tc39.github.io/ecma262/#sec-getmethod\n          function GetMethod(V, P) {\n              var func = V[P];\n              if (func === undefined || func === null)\n                  return undefined;\n              if (!IsCallable(func))\n                  throw new TypeError();\n              return func;\n          }\n          // 7.4 Operations on Iterator Objects\n          // https://tc39.github.io/ecma262/#sec-operations-on-iterator-objects\n          function GetIterator(obj) {\n              var method = GetMethod(obj, iteratorSymbol);\n              if (!IsCallable(method))\n                  throw new TypeError(); // from Call\n              var iterator = method.call(obj);\n              if (!IsObject(iterator))\n                  throw new TypeError();\n              return iterator;\n          }\n          // 7.4.4 IteratorValue(iterResult)\n          // https://tc39.github.io/ecma262/2016/#sec-iteratorvalue\n          function IteratorValue(iterResult) {\n              return iterResult.value;\n          }\n          // 7.4.5 IteratorStep(iterator)\n          // https://tc39.github.io/ecma262/#sec-iteratorstep\n          function IteratorStep(iterator) {\n              var result = iterator.next();\n              return result.done ? false : result;\n          }\n          // 7.4.6 IteratorClose(iterator, completion)\n          // https://tc39.github.io/ecma262/#sec-iteratorclose\n          function IteratorClose(iterator) {\n              var f = iterator[\"return\"];\n              if (f)\n                  f.call(iterator);\n          }\n          // 9.1 Ordinary Object Internal Methods and Internal Slots\n          // https://tc39.github.io/ecma262/#sec-ordinary-object-internal-methods-and-internal-slots\n          // ******* OrdinaryGetPrototypeOf(O)\n          // https://tc39.github.io/ecma262/#sec-ordinarygetprototypeof\n          function OrdinaryGetPrototypeOf(O) {\n              var proto = Object.getPrototypeOf(O);\n              if (typeof O !== \"function\" || O === functionPrototype)\n                  return proto;\n              // TypeScript doesn't set __proto__ in ES5, as it's non-standard.\n              // Try to determine the superclass constructor. Compatible implementations\n              // must either set __proto__ on a subclass constructor to the superclass constructor,\n              // or ensure each class has a valid `constructor` property on its prototype that\n              // points back to the constructor.\n              // If this is not the same as Function.[[Prototype]], then this is definately inherited.\n              // This is the case when in ES6 or when using __proto__ in a compatible browser.\n              if (proto !== functionPrototype)\n                  return proto;\n              // If the super prototype is Object.prototype, null, or undefined, then we cannot determine the heritage.\n              var prototype = O.prototype;\n              var prototypeProto = prototype && Object.getPrototypeOf(prototype);\n              if (prototypeProto == null || prototypeProto === Object.prototype)\n                  return proto;\n              // If the constructor was not a function, then we cannot determine the heritage.\n              var constructor = prototypeProto.constructor;\n              if (typeof constructor !== \"function\")\n                  return proto;\n              // If we have some kind of self-reference, then we cannot determine the heritage.\n              if (constructor === O)\n                  return proto;\n              // we have a pretty good guess at the heritage.\n              return constructor;\n          }\n          // naive Map shim\n          function CreateMapPolyfill() {\n              var cacheSentinel = {};\n              var arraySentinel = [];\n              var MapIterator = /** @class */ (function () {\n                  function MapIterator(keys, values, selector) {\n                      this._index = 0;\n                      this._keys = keys;\n                      this._values = values;\n                      this._selector = selector;\n                  }\n                  MapIterator.prototype[\"@@iterator\"] = function () { return this; };\n                  MapIterator.prototype[iteratorSymbol] = function () { return this; };\n                  MapIterator.prototype.next = function () {\n                      var index = this._index;\n                      if (index >= 0 && index < this._keys.length) {\n                          var result = this._selector(this._keys[index], this._values[index]);\n                          if (index + 1 >= this._keys.length) {\n                              this._index = -1;\n                              this._keys = arraySentinel;\n                              this._values = arraySentinel;\n                          }\n                          else {\n                              this._index++;\n                          }\n                          return { value: result, done: false };\n                      }\n                      return { value: undefined, done: true };\n                  };\n                  MapIterator.prototype.throw = function (error) {\n                      if (this._index >= 0) {\n                          this._index = -1;\n                          this._keys = arraySentinel;\n                          this._values = arraySentinel;\n                      }\n                      throw error;\n                  };\n                  MapIterator.prototype.return = function (value) {\n                      if (this._index >= 0) {\n                          this._index = -1;\n                          this._keys = arraySentinel;\n                          this._values = arraySentinel;\n                      }\n                      return { value: value, done: true };\n                  };\n                  return MapIterator;\n              }());\n              return /** @class */ (function () {\n                  function Map() {\n                      this._keys = [];\n                      this._values = [];\n                      this._cacheKey = cacheSentinel;\n                      this._cacheIndex = -2;\n                  }\n                  Object.defineProperty(Map.prototype, \"size\", {\n                      get: function () { return this._keys.length; },\n                      enumerable: true,\n                      configurable: true\n                  });\n                  Map.prototype.has = function (key) { return this._find(key, /*insert*/ false) >= 0; };\n                  Map.prototype.get = function (key) {\n                      var index = this._find(key, /*insert*/ false);\n                      return index >= 0 ? this._values[index] : undefined;\n                  };\n                  Map.prototype.set = function (key, value) {\n                      var index = this._find(key, /*insert*/ true);\n                      this._values[index] = value;\n                      return this;\n                  };\n                  Map.prototype.delete = function (key) {\n                      var index = this._find(key, /*insert*/ false);\n                      if (index >= 0) {\n                          var size = this._keys.length;\n                          for (var i = index + 1; i < size; i++) {\n                              this._keys[i - 1] = this._keys[i];\n                              this._values[i - 1] = this._values[i];\n                          }\n                          this._keys.length--;\n                          this._values.length--;\n                          if (key === this._cacheKey) {\n                              this._cacheKey = cacheSentinel;\n                              this._cacheIndex = -2;\n                          }\n                          return true;\n                      }\n                      return false;\n                  };\n                  Map.prototype.clear = function () {\n                      this._keys.length = 0;\n                      this._values.length = 0;\n                      this._cacheKey = cacheSentinel;\n                      this._cacheIndex = -2;\n                  };\n                  Map.prototype.keys = function () { return new MapIterator(this._keys, this._values, getKey); };\n                  Map.prototype.values = function () { return new MapIterator(this._keys, this._values, getValue); };\n                  Map.prototype.entries = function () { return new MapIterator(this._keys, this._values, getEntry); };\n                  Map.prototype[\"@@iterator\"] = function () { return this.entries(); };\n                  Map.prototype[iteratorSymbol] = function () { return this.entries(); };\n                  Map.prototype._find = function (key, insert) {\n                      if (this._cacheKey !== key) {\n                          this._cacheIndex = this._keys.indexOf(this._cacheKey = key);\n                      }\n                      if (this._cacheIndex < 0 && insert) {\n                          this._cacheIndex = this._keys.length;\n                          this._keys.push(key);\n                          this._values.push(undefined);\n                      }\n                      return this._cacheIndex;\n                  };\n                  return Map;\n              }());\n              function getKey(key, _) {\n                  return key;\n              }\n              function getValue(_, value) {\n                  return value;\n              }\n              function getEntry(key, value) {\n                  return [key, value];\n              }\n          }\n          // naive Set shim\n          function CreateSetPolyfill() {\n              return /** @class */ (function () {\n                  function Set() {\n                      this._map = new _Map();\n                  }\n                  Object.defineProperty(Set.prototype, \"size\", {\n                      get: function () { return this._map.size; },\n                      enumerable: true,\n                      configurable: true\n                  });\n                  Set.prototype.has = function (value) { return this._map.has(value); };\n                  Set.prototype.add = function (value) { return this._map.set(value, value), this; };\n                  Set.prototype.delete = function (value) { return this._map.delete(value); };\n                  Set.prototype.clear = function () { this._map.clear(); };\n                  Set.prototype.keys = function () { return this._map.keys(); };\n                  Set.prototype.values = function () { return this._map.values(); };\n                  Set.prototype.entries = function () { return this._map.entries(); };\n                  Set.prototype[\"@@iterator\"] = function () { return this.keys(); };\n                  Set.prototype[iteratorSymbol] = function () { return this.keys(); };\n                  return Set;\n              }());\n          }\n          // naive WeakMap shim\n          function CreateWeakMapPolyfill() {\n              var UUID_SIZE = 16;\n              var keys = HashMap.create();\n              var rootKey = CreateUniqueKey();\n              return /** @class */ (function () {\n                  function WeakMap() {\n                      this._key = CreateUniqueKey();\n                  }\n                  WeakMap.prototype.has = function (target) {\n                      var table = GetOrCreateWeakMapTable(target, /*create*/ false);\n                      return table !== undefined ? HashMap.has(table, this._key) : false;\n                  };\n                  WeakMap.prototype.get = function (target) {\n                      var table = GetOrCreateWeakMapTable(target, /*create*/ false);\n                      return table !== undefined ? HashMap.get(table, this._key) : undefined;\n                  };\n                  WeakMap.prototype.set = function (target, value) {\n                      var table = GetOrCreateWeakMapTable(target, /*create*/ true);\n                      table[this._key] = value;\n                      return this;\n                  };\n                  WeakMap.prototype.delete = function (target) {\n                      var table = GetOrCreateWeakMapTable(target, /*create*/ false);\n                      return table !== undefined ? delete table[this._key] : false;\n                  };\n                  WeakMap.prototype.clear = function () {\n                      // NOTE: not a real clear, just makes the previous data unreachable\n                      this._key = CreateUniqueKey();\n                  };\n                  return WeakMap;\n              }());\n              function CreateUniqueKey() {\n                  var key;\n                  do\n                      key = \"@@WeakMap@@\" + CreateUUID();\n                  while (HashMap.has(keys, key));\n                  keys[key] = true;\n                  return key;\n              }\n              function GetOrCreateWeakMapTable(target, create) {\n                  if (!hasOwn.call(target, rootKey)) {\n                      if (!create)\n                          return undefined;\n                      Object.defineProperty(target, rootKey, { value: HashMap.create() });\n                  }\n                  return target[rootKey];\n              }\n              function FillRandomBytes(buffer, size) {\n                  for (var i = 0; i < size; ++i)\n                      buffer[i] = Math.random() * 0xff | 0;\n                  return buffer;\n              }\n              function GenRandomBytes(size) {\n                  if (typeof Uint8Array === \"function\") {\n                      if (typeof crypto !== \"undefined\")\n                          return crypto.getRandomValues(new Uint8Array(size));\n                      if (typeof msCrypto !== \"undefined\")\n                          return msCrypto.getRandomValues(new Uint8Array(size));\n                      return FillRandomBytes(new Uint8Array(size), size);\n                  }\n                  return FillRandomBytes(new Array(size), size);\n              }\n              function CreateUUID() {\n                  var data = GenRandomBytes(UUID_SIZE);\n                  // mark as random - RFC 4122 § 4.4\n                  data[6] = data[6] & 0x4f | 0x40;\n                  data[8] = data[8] & 0xbf | 0x80;\n                  var result = \"\";\n                  for (var offset = 0; offset < UUID_SIZE; ++offset) {\n                      var byte = data[offset];\n                      if (offset === 4 || offset === 6 || offset === 8)\n                          result += \"-\";\n                      if (byte < 16)\n                          result += \"0\";\n                      result += byte.toString(16).toLowerCase();\n                  }\n                  return result;\n              }\n          }\n          // uses a heuristic used by v8 and chakra to force an object into dictionary mode.\n          function MakeDictionary(obj) {\n              obj.__ = undefined;\n              delete obj.__;\n              return obj;\n          }\n      });\n  })(Reflect || (Reflect = {}))\n}\n", "const SERVICE_IDENTIFIER = {\n  TaroElement: 'TaroElement',\n  TaroElementFactory: 'Factory<TaroElement>',\n  TaroText: 'TaroText',\n  TaroTextFactory: 'Factory<TaroText>',\n  TaroNodeImpl: 'TaroNodeImpl',\n  TaroElementImpl: 'TaroElementImpl',\n  Hooks: 'hooks',\n  onRemoveAttribute: 'onRemoveAttribute',\n  getLifecycle: 'getLifecycle',\n  getPathIndex: 'getPathIndex',\n  getEventCenter: 'getEventCenter',\n  isBubbleEvents: 'isBubbleEvents',\n  getSpecialNodes: 'getSpecialNodes',\n  eventCenter: 'eventCenter',\n  modifyMpEvent: 'modifyMpEvent',\n  modifyTaroEvent: 'modifyTaroEvent',\n  batchedEventUpdates: 'batchedEventUpdates',\n  mergePageInstance: 'mergePageInstance',\n  createPullDownComponent: 'createPullDownComponent',\n  getDOMNode: 'getDOMNode',\n  initNativeApi: 'initNativeApi',\n  modifyHydrateData: 'modifyHydrateData',\n  modifySetAttrPayload: 'modifySetAttrPayload',\n  modifyRmAttrPayload: 'modifyRmAttrPayload',\n  onAddEvent: 'onAddEvent',\n  patchElement: 'patchElement'\n}\n\nexport default SERVICE_IDENTIFIER\n", "export const PROPERTY_THRESHOLD = 2046\nexport const TARO_RUNTIME = 'Taro runtime'\nexport const HOOKS_APP_ID = 'taro-app'\nexport const SET_DATA = '小程序 setData'\nexport const PAGE_INIT = '页面初始化'\nexport const ROOT_STR = 'root'\nexport const HTML = 'html'\nexport const HEAD = 'head'\nexport const BODY = 'body'\nexport const APP = 'app'\nexport const CONTAINER = 'container'\nexport const DOCUMENT_ELEMENT_NAME = '#document'\nexport const DOCUMENT_FRAGMENT = 'document-fragment'\nexport const ID = 'id'\nexport const UID = 'uid'\nexport const CLASS = 'class'\nexport const STYLE = 'style'\nexport const FOCUS = 'focus'\nexport const VIEW = 'view'\nexport const STATIC_VIEW = 'static-view'\nexport const PURE_VIEW = 'pure-view'\nexport const PROPS = 'props'\nexport const DATASET = 'dataset'\nexport const OBJECT = 'object'\nexport const VALUE = 'value'\nexport const INPUT = 'input'\nexport const CHANGE = 'change'\nexport const CUSTOM_WRAPPER = 'custom-wrapper'\nexport const TARGET = 'target'\nexport const CURRENT_TARGET = 'currentTarget'\nexport const TYPE = 'type'\nexport const CONFIRM = 'confirm'\nexport const TIME_STAMP = 'timeStamp'\nexport const KEY_CODE = 'keyCode'\nexport const TOUCHMOVE = 'touchmove'\nexport const DATE = 'Date'\nexport const SET_TIMEOUT = 'setTimeout'\nexport const CATCHMOVE = 'catchMove'\nexport const CATCH_VIEW = 'catch-view'\nexport const COMMENT = 'comment'\n", "import { Shortcuts } from '@tarojs/shared'\nimport { NodeType } from '../dom/node_types'\nimport {\n  ROOT_STR,\n  STYLE,\n  ID,\n  UID,\n  CLASS,\n  COMMENT\n} from '../constants'\n\nimport type { TaroElement } from '../dom/element'\nimport type { TaroText } from '../dom/text'\nimport type { TaroNode } from '../dom/node'\n\nexport const incrementId = () => {\n  let id = 0\n  return () => (id++).toString()\n}\n\nexport function isElement (node: TaroNode): node is TaroElement {\n  return node.nodeType === NodeType.ELEMENT_NODE\n}\n\nexport function isText (node: TaroNode): node is TaroText {\n  return node.nodeType === NodeType.TEXT_NODE\n}\n\nexport function isComment (node: TaroNode): boolean {\n  return node.nodeName === COMMENT\n}\n\nexport function isHasExtractProp (el: TaroElement): boolean {\n  const res = Object.keys(el.props).find(prop => {\n    return !(/^(class|style|id)$/.test(prop) || prop.startsWith('data-'))\n  })\n  return Boolean(res)\n}\n\n/**\n * 往上寻找组件树直到 root，寻找是否有祖先组件绑定了同类型的事件\n * @param node 当前组件\n * @param type 事件类型\n */\nexport function isParentBinded (node: TaroElement | null, type: string): boolean {\n  let res = false\n  while (node?.parentElement && node.parentElement._path !== ROOT_STR) {\n    if (node.parentElement.__handlers[type]?.length) {\n      res = true\n      break\n    }\n    node = node.parentElement\n  }\n  return res\n}\n\nexport function shortcutAttr (key: string): string {\n  switch (key) {\n    case STYLE:\n      return Shortcuts.Style\n    case ID:\n      return UID\n    case CLASS:\n      return Shortcuts.Class\n    default:\n      return key\n  }\n}\n", "import { inject, injectable } from 'inversify'\nimport { isArray, isObject, warn } from '@tarojs/shared'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\n\nimport type { EventHandler, AddEventListenerOptions, IHooks } from '../interface'\n\n@injectable()\nexport class TaroEventTarget {\n  public __handlers: Record<string, EventHandler[]> = {}\n  public hooks: IHooks\n\n  public constructor (// eslint-disable-next-line @typescript-eslint/indent\n    @inject(SERVICE_IDENTIFIER.Hooks) hooks: IHooks\n  ) {\n    this.hooks = hooks\n  }\n\n  public addEventListener (type: string, handler: EventHandler, options?: boolean | AddEventListenerOptions) {\n    this.hooks.onAddEvent?.(type, handler, options, this)\n    if (type === 'regionchange') {\n      // map 组件的 regionchange 事件非常特殊，详情：https://github.com/NervJS/taro/issues/5766\n      this.addEventListener('begin', handler, options)\n      this.addEventListener('end', handler, options)\n      return\n    }\n    type = type.toLowerCase()\n    const handlers = this.__handlers[type]\n    let isCapture = Boolean(options)\n    let isOnce = false\n    if (isObject<AddEventListenerOptions>(options)) {\n      isCapture = Boolean(options.capture)\n      isOnce = Boolean(options.once)\n    }\n\n    if (isOnce) {\n      const wrapper = function () {\n        handler.apply(this, arguments) // this 指向 Element\n        this.removeEventListener(type, wrapper)\n      }\n      this.addEventListener(type, wrapper, {\n        ...(options as AddEventListenerOptions),\n        once: false\n      })\n      return\n    }\n\n    process.env.NODE_ENV !== 'production' && warn(isCapture, 'Taro 暂未实现 event 的 capture 特性。')\n\n    if (isArray(handlers)) {\n      handlers.push(handler)\n    } else {\n      this.__handlers[type] = [handler]\n    }\n  }\n\n  public removeEventListener (type: string, handler: EventHandler) {\n    type = type.toLowerCase()\n    if (handler == null) {\n      return\n    }\n\n    const handlers = this.__handlers[type]\n    if (!isArray(handlers)) {\n      return\n    }\n\n    const index = handlers.indexOf(handler)\n\n    process.env.NODE_ENV !== 'production' && warn(index === -1, `事件: '${type}' 没有注册在 DOM 中，因此不会被移除。`)\n\n    handlers.splice(index, 1)\n  }\n\n  public isAnyEventBinded (): boolean {\n    const handlers = this.__handlers\n    const isAnyEventBinded = Object.keys(handlers).find(key => handlers[key].length)\n    return Boolean(isAnyEventBinded)\n  }\n}\n", "import { Shortcuts, toCamelCase } from '@tarojs/shared'\nimport { isText, isHasExtractProp, isComment } from './utils'\nimport {\n  VIEW,\n  CLASS,\n  STYLE,\n  ID,\n  PURE_VIEW,\n  CATCHMOVE,\n  CATCH_VIEW\n} from './constants'\n\nimport type { MiniData, MiniElementData } from './interface'\nimport type { TaroElement } from './dom/element'\nimport type { TaroText } from './dom/text'\n\n/**\n * React also has a fancy function's name for this: `hydrate()`.\n * You may have been heard `hydrate` as a SSR-related function,\n * actually, `hydrate` basicly do the `render()` thing, but ignore some properties,\n * it's a vnode traverser and modifier: that's exactly what <PERSON><PERSON>'s doing in here.\n */\nexport function hydrate (node: TaroElement | TaroText): MiniData {\n  const nodeName = node.nodeName\n\n  if (isText(node)) {\n    return {\n      [Shortcuts.Text]: node.nodeValue,\n      [Shortcuts.NodeName]: nodeName\n    }\n  }\n\n  const data: MiniElementData = {\n    [Shortcuts.NodeName]: nodeName,\n    uid: node.uid\n  }\n  const { props } = node\n  const SPECIAL_NODES = node.hooks.getSpecialNodes()\n\n  if (!node.isAnyEventBinded() && SPECIAL_NODES.indexOf(nodeName) > -1) {\n    data[Shortcuts.NodeName] = `static-${nodeName}`\n    if (nodeName === VIEW && !isHasExtractProp(node)) {\n      data[Shortcuts.NodeName] = PURE_VIEW\n    }\n  }\n\n  for (const prop in props) {\n    const propInCamelCase = toCamelCase(prop)\n    if (\n      !prop.startsWith('data-') && // 在 node.dataset 的数据\n      prop !== CLASS &&\n      prop !== STYLE &&\n      prop !== ID &&\n      propInCamelCase !== CATCHMOVE\n    ) {\n      data[propInCamelCase] = props[prop]\n    }\n    if (nodeName === VIEW && propInCamelCase === CATCHMOVE && props[prop] !== false) {\n      data[Shortcuts.NodeName] = CATCH_VIEW\n    }\n  }\n\n  let { childNodes } = node\n\n  // 过滤 comment 节点\n  childNodes = childNodes.filter(node => !isComment(node))\n\n  if (childNodes.length > 0) {\n    data[Shortcuts.Childnodes] = childNodes.map(hydrate)\n  } else {\n    data[Shortcuts.Childnodes] = []\n  }\n\n  if (node.className !== '') {\n    data[Shortcuts.Class] = node.className\n  }\n\n  if (node.cssText !== '' && nodeName !== 'swiper-item') {\n    data[Shortcuts.Style] = node.cssText\n  }\n\n  node.hooks.modifyHydrateData?.(data)\n\n  return data\n}\n", "import type { TaroNode } from './node'\nexport const eventSource = new Map<string | undefined | null, TaroNode>()\n", "export enum ElementNames {\n  Element = 'Element',\n  Document = 'Document',\n  RootElement = 'RootElement',\n  FormElement = 'FormElement'\n}\n\nexport interface InstanceFactory<T> {\n  (...args: any[]): T\n}\n\nexport interface InstanceNamedFactory {\n  <T>(named: string): (...args: any[]) => T\n}\n", "import { inject, injectable } from 'inversify'\nimport { Shortcuts, ensure } from '@tarojs/shared'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { NodeType } from './node_types'\nimport { incrementId, isComment } from '../utils'\nimport { TaroEventTarget } from './event-target'\nimport { hydrate } from '../hydrate'\nimport { eventSource } from './event-source'\nimport { ElementNames } from '../interface'\nimport {\n  DOCUMENT_FRAGMENT\n} from '../constants'\n\nimport type { UpdatePayload, InstanceNamedFactory } from '../interface'\nimport type { TaroDocument } from './document'\nimport type { TaroRootElement } from './root'\nimport type { TaroElement } from './element'\nimport type { TaroNodeImpl } from '../dom-external/node-impl'\nimport type { Hooks } from '../hooks'\n\nconst nodeId = incrementId()\n\n@injectable()\nexport class TaroNode extends TaroEventTarget {\n  public uid: string\n  public nodeType: NodeType\n  public nodeName: string\n  public parentNode: TaroNode | null = null\n  public childNodes: TaroNode[] = []\n\n  protected _getElement: InstanceNamedFactory\n\n  public constructor (// eslint-disable-next-line @typescript-eslint/indent\n    @inject(SERVICE_IDENTIFIER.TaroNodeImpl) impl: TaroNodeImpl,\n    @inject(SERVICE_IDENTIFIER.TaroElementFactory) getElement: InstanceNamedFactory,\n    @inject(SERVICE_IDENTIFIER.Hooks) hooks: Hooks\n  ) {\n    super(hooks)\n    impl.bind(this)\n    this._getElement = getElement\n    this.uid = `_n_${nodeId()}`\n    eventSource.set(this.uid, this)\n  }\n\n  private hydrate = (node: TaroNode) => () => hydrate(node as TaroElement)\n\n  /**\n   * like jQuery's $.empty()\n   */\n  private _empty () {\n    while (this.childNodes.length > 0) {\n      const child = this.childNodes[0]\n      child.parentNode = null\n      eventSource.delete(child.uid)\n      this.childNodes.shift()\n    }\n  }\n\n  protected get _root (): TaroRootElement | null {\n    return this.parentNode?._root || null\n  }\n\n  protected findIndex (refChild: TaroNode): number {\n    const index = this.childNodes.indexOf(refChild)\n\n    ensure(index !== -1, 'The node to be replaced is not a child of this node.')\n\n    return index\n  }\n\n  public get _path (): string {\n    const parentNode = this.parentNode\n\n    if (parentNode) {\n      // 计算路径时，先过滤掉 comment 节点\n      const list = parentNode.childNodes.filter(node => !isComment(node))\n      const indexOfNode = list.indexOf(this)\n      const index = this.hooks.getPathIndex(indexOfNode)\n\n      return `${parentNode._path}.${Shortcuts.Childnodes}.${index}`\n    }\n\n    return ''\n  }\n\n  public get nextSibling (): TaroNode | null {\n    const parentNode = this.parentNode\n    return parentNode?.childNodes[parentNode.findIndex(this) + 1] || null\n  }\n\n  public get previousSibling (): TaroNode | null {\n    const parentNode = this.parentNode\n    return parentNode?.childNodes[parentNode.findIndex(this) - 1] || null\n  }\n\n  public get parentElement (): TaroElement | null {\n    const parentNode = this.parentNode\n    if (parentNode?.nodeType === NodeType.ELEMENT_NODE) {\n      return parentNode as TaroElement\n    }\n    return null\n  }\n\n  public get firstChild (): TaroNode | null {\n    return this.childNodes[0] || null\n  }\n\n  public get lastChild (): TaroNode | null {\n    const childNodes = this.childNodes\n    return childNodes[childNodes.length - 1] || null\n  }\n\n  /**\n   * @textContent 目前只能置空子元素\n   * @TODO 等待完整 innerHTML 实现\n   */\n  public set textContent (text: string) {\n    this._empty()\n    if (text === '') {\n      this.enqueueUpdate({\n        path: `${this._path}.${Shortcuts.Childnodes}`,\n        value: () => []\n      })\n    } else {\n      const document = this._getElement<TaroDocument>(ElementNames.Document)()\n      this.appendChild(document.createTextNode(text))\n    }\n  }\n\n  public insertBefore<T extends TaroNode> (newChild: T, refChild?: TaroNode | null, isReplace?: boolean): T {\n    if (newChild.nodeName === DOCUMENT_FRAGMENT) {\n      newChild.childNodes.reduceRight((previousValue, currentValue) => {\n        this.insertBefore(currentValue, previousValue)\n        return currentValue\n      }, refChild)\n      return newChild\n    }\n\n    newChild.remove()\n    newChild.parentNode = this\n    let payload: UpdatePayload\n\n    if (refChild) {\n      const index = this.findIndex(refChild)\n      this.childNodes.splice(index, 0, newChild)\n      if (isReplace) {\n        payload = {\n          path: newChild._path,\n          value: this.hydrate(newChild)\n        }\n      } else {\n        payload = {\n          path: `${this._path}.${Shortcuts.Childnodes}`,\n          value: () => {\n            const childNodes = this.childNodes.filter(node => !isComment(node))\n            return childNodes.map(hydrate)\n          }\n        }\n      }\n    } else {\n      this.childNodes.push(newChild)\n      payload = {\n        path: newChild._path,\n        value: this.hydrate(newChild)\n      }\n    }\n\n    this.enqueueUpdate(payload)\n\n    if (!eventSource.has(newChild.uid)) {\n      eventSource.set(newChild.uid, newChild)\n    }\n\n    return newChild\n  }\n\n  public appendChild (child: TaroNode) {\n    this.insertBefore(child)\n  }\n\n  public replaceChild (newChild: TaroNode, oldChild: TaroNode) {\n    if (oldChild.parentNode === this) {\n      this.insertBefore(newChild, oldChild, true)\n      oldChild.remove(true)\n      return oldChild\n    }\n  }\n\n  public removeChild<T extends TaroNode> (child: T, isReplace?: boolean): T {\n    const index = this.findIndex(child)\n    this.childNodes.splice(index, 1)\n    if (!isReplace) {\n      this.enqueueUpdate({\n        path: `${this._path}.${Shortcuts.Childnodes}`,\n        value: () => {\n          const childNodes = this.childNodes.filter(node => !isComment(node))\n          return childNodes.map(hydrate)\n        }\n      })\n    }\n    child.parentNode = null\n    eventSource.delete(child.uid)\n    // @TODO: eventSource memory overflow\n    // child._empty()\n    return child\n  }\n\n  public remove (isReplace?: boolean) {\n    this.parentNode?.removeChild(this, isReplace)\n  }\n\n  public hasChildNodes () {\n    return this.childNodes.length > 0\n  }\n\n  public enqueueUpdate (payload: UpdatePayload) {\n    this._root?.enqueueUpdate(payload)\n  }\n\n  public contains (node: TaroNode & { id?: string }): boolean {\n    let isContains = false\n    this.childNodes.some(childNode => {\n      const { uid } = childNode\n      if (uid === node.uid || uid === node.id || childNode.contains(node)) {\n        isContains = true\n        return true\n      }\n    })\n    return isContains\n  }\n\n  public get ownerDocument () {\n    const document = this._getElement<TaroDocument>(ElementNames.Document)()\n    return document\n  }\n}\n", "import { inject, injectable } from 'inversify'\nimport { Shortcuts } from '@tarojs/shared'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { TaroNode } from './node'\nimport { NodeType } from './node_types'\n\nimport type { <PERSON>roNodeImpl } from '../dom-external/node-impl'\nimport type { InstanceNamedFactory } from '../interface'\nimport type { Hooks } from '../hooks'\n\n@injectable()\nexport class TaroText extends TaroNode {\n  public _value: string\n\n  public constructor (// eslint-disable-next-line @typescript-eslint/indent\n    @inject(SERVICE_IDENTIFIER.TaroNodeImpl) nodeImpl: TaroNodeImpl,\n    @inject(SERVICE_IDENTIFIER.TaroElementFactory) getElement: InstanceNamedFactory,\n    @inject(SERVICE_IDENTIFIER.Hooks) hooks: Hooks\n  ) {\n    super(nodeImpl, getElement, hooks)\n    this.nodeType = NodeType.TEXT_NODE\n    this.nodeName = '#text'\n  }\n\n  public set textContent (text: string) {\n    this._value = text\n    this.enqueueUpdate({\n      path: `${this._path}.${Shortcuts.Text}`,\n      value: text\n    })\n  }\n\n  public get textContent (): string {\n    return this._value\n  }\n\n  public set nodeValue (text: string) {\n    this.textContent = text\n  }\n\n  public get nodeValue (): string {\n    return this._value\n  }\n}\n", "/*\n *\n * https://www.w3.org/Style/CSS/all-properties.en.html\n */\n\nconst styleProperties = [\n  'all',\n  'appearance',\n  'blockOverflow',\n  'blockSize',\n  'bottom',\n  'clear',\n  'contain',\n  'content',\n  'continue',\n  'cursor',\n  'direction',\n  'display',\n  'filter',\n  'float',\n  'gap',\n  'height',\n  'inset',\n  'isolation',\n  'left',\n  'letterSpacing',\n  'lightingColor',\n  'markerSide',\n  'mixBlendMode',\n  'opacity',\n  'order',\n  'position',\n  'quotes',\n  'resize',\n  'right',\n  'rowGap',\n  'tabSize',\n  'tableLayout',\n  'top',\n  'userSelect',\n  'verticalAlign',\n  'visibility',\n  'voiceFamily',\n  'volume',\n  'whiteSpace',\n  'widows',\n  'width',\n  'zIndex',\n  'pointerEvents'\n\n  /** 非常用 style */\n  // 'azimuth',\n  // 'backfaceVisibility',\n  // 'baselineShift',\n  // 'captionSide',\n  // 'chains',\n  // 'dominantBaseline',\n  // 'elevation',\n  // 'emptyCells',\n  // 'forcedColorAdjust',\n  // 'glyphOrientationVertical',\n  // 'hangingPunctuation',\n  // 'hyphenateCharacter',\n  // 'hyphens',\n  // 'imageOrientation',\n  // 'imageResolution',\n  // 'orphans',\n  // 'playDuring',\n  // 'pointerEvents',\n  // 'regionFragment',\n  // 'richness',\n  // 'running',\n  // 'scrollBehavior',\n  // 'speechRate',\n  // 'stress',\n  // 'stringSet',\n  // 'unicodeBidi',\n  // 'willChange',\n  // 'writingMode',\n]\n\n// 减少文件体积\nfunction combine (prefix: string, list: string[], excludeSelf?: boolean) {\n  !excludeSelf && styleProperties.push(prefix)\n  list.forEach(item => {\n    styleProperties.push(prefix + item)\n  })\n}\n\nconst color = 'Color'\nconst style = 'Style'\nconst width = 'Width'\nconst image = 'Image'\nconst size = 'Size'\nconst color_style_width = [color, style, width]\nconst fitlength_fitwidth_image = ['FitLength', 'FitWidth', image]\nconst fitlength_fitwidth_image_radius = [...fitlength_fitwidth_image, 'Radius']\nconst color_style_width_fitlength_fitwidth_image = [...color_style_width, ...fitlength_fitwidth_image]\nconst endRadius_startRadius = ['EndRadius', 'StartRadius']\nconst bottom_left_right_top = ['Bottom', 'Left', 'Right', 'Top']\nconst end_start = ['End', 'Start']\nconst content_items_self = ['Content', 'Items', 'Self']\nconst blockSize_height_inlineSize_width = ['BlockSize', 'Height', 'InlineSize', width]\nconst after_before = ['After', 'Before']\n\ncombine('borderBlock', color_style_width)\ncombine('borderBlockEnd', color_style_width)\ncombine('borderBlockStart', color_style_width)\ncombine('outline', [...color_style_width, 'Offset'])\ncombine('border', [...color_style_width, 'Boundary', 'Break', 'Collapse', 'Radius', 'Spacing'])\ncombine('borderFit', ['Length', width])\ncombine('borderInline', color_style_width)\ncombine('borderInlineEnd', color_style_width)\ncombine('borderInlineStart', color_style_width)\ncombine('borderLeft', color_style_width_fitlength_fitwidth_image)\ncombine('borderRight', color_style_width_fitlength_fitwidth_image)\ncombine('borderTop', color_style_width_fitlength_fitwidth_image)\ncombine('borderBottom', color_style_width_fitlength_fitwidth_image)\ncombine('textDecoration', [color, style, 'Line'])\ncombine('textEmphasis', [color, style, 'Position'])\ncombine('scrollMargin', bottom_left_right_top)\ncombine('scrollPadding', bottom_left_right_top)\ncombine('padding', bottom_left_right_top)\ncombine('margin', [...bottom_left_right_top, 'Trim'])\ncombine('scrollMarginBlock', end_start)\ncombine('scrollMarginInline', end_start)\ncombine('scrollPaddingBlock', end_start)\ncombine('scrollPaddingInline', end_start)\ncombine('gridColumn', end_start)\ncombine('gridRow', end_start)\ncombine('insetBlock', end_start)\ncombine('insetInline', end_start)\ncombine('marginBlock', end_start)\ncombine('marginInline', end_start)\ncombine('paddingBlock', end_start)\ncombine('paddingInline', end_start)\ncombine('pause', after_before)\ncombine('cue', after_before)\ncombine('mask', ['Clip', 'Composite', image, 'Mode', 'Origin', 'Position', 'Repeat', size, 'Type'])\ncombine('borderImage', ['Outset', 'Repeat', 'Slice', 'Source', 'Transform', width])\ncombine('maskBorder', ['Mode', 'Outset', 'Repeat', 'Slice', 'Source', width])\ncombine('font', ['Family', 'FeatureSettings', 'Kerning', 'LanguageOverride', 'MaxSize', 'MinSize', 'OpticalSizing', 'Palette', size, 'SizeAdjust', 'Stretch', style, 'Weight', 'VariationSettings'])\ncombine('fontSynthesis', ['SmallCaps', style, 'Weight'])\ncombine('transform', ['Box', 'Origin', style])\ncombine('background', [color, image, 'Attachment', 'BlendMode', 'Clip', 'Origin', 'Position', 'Repeat', size])\ncombine('listStyle', [image, 'Position', 'Type'])\ncombine('scrollSnap', ['Align', 'Stop', 'Type'])\ncombine('grid', ['Area', 'AutoColumns', 'AutoFlow', 'AutoRows'])\ncombine('gridTemplate', ['Areas', 'Columns', 'Rows'])\ncombine('overflow', ['Block', 'Inline', 'Wrap', 'X', 'Y'])\ncombine('transition', ['Delay', 'Duration', 'Property', 'TimingFunction'])\ncombine('lineStacking', ['Ruby', 'Shift', 'Strategy'])\ncombine('color', ['Adjust', 'InterpolationFilters', 'Scheme'])\ncombine('textAlign', ['All', 'Last'])\ncombine('page', ['BreakAfter', 'BreakBefore', 'BreakInside'])\ncombine('speak', ['Header', 'Numeral', 'Punctuation'])\ncombine('animation', ['Delay', 'Direction', 'Duration', 'FillMode', 'IterationCount', 'Name', 'PlayState', 'TimingFunction'])\ncombine('flex', ['Basis', 'Direction', 'Flow', 'Grow', 'Shrink', 'Wrap'])\ncombine('offset', [...after_before, ...end_start, 'Anchor', 'Distance', 'Path', 'Position', 'Rotate'])\ncombine('fontVariant', ['Alternates', 'Caps', 'EastAsian', 'Emoji', 'Ligatures', 'Numeric', 'Position'])\ncombine('perspective', ['Origin'])\ncombine('pitch', ['Range'])\ncombine('clip', ['Path', 'Rule'])\ncombine('flow', ['From', 'Into'])\n\ncombine('align', ['Content', 'Items', 'Self'], true)\ncombine('alignment', ['Adjust', 'Baseline'], true)\ncombine('bookmark', ['Label', 'Level', 'State'], true)\ncombine('borderStart', endRadius_startRadius, true)\ncombine('borderEnd', endRadius_startRadius, true)\ncombine('borderCorner', ['Fit', image, 'ImageTransform'], true)\ncombine('borderTopLeft', fitlength_fitwidth_image_radius, true)\ncombine('borderTopRight', fitlength_fitwidth_image_radius, true)\ncombine('borderBottomLeft', fitlength_fitwidth_image_radius, true)\ncombine('borderBottomRight', fitlength_fitwidth_image_radius, true)\ncombine('column', ['s', 'Count', 'Fill', 'Gap', 'Rule', 'RuleColor', 'RuleStyle', 'RuleWidth', 'Span', width], true)\ncombine('break', [...after_before, 'Inside'], true)\ncombine('wrap', [...after_before, 'Flow', 'Inside', 'Through'], true)\ncombine('justify', content_items_self, true)\ncombine('place', content_items_self, true)\ncombine('max', [...blockSize_height_inlineSize_width, 'Lines'], true)\ncombine('min', blockSize_height_inlineSize_width, true)\ncombine('line', ['Break', 'Clamp', 'Grid', 'Height', 'Padding', 'Snap'], true)\ncombine('inline', ['BoxAlign', size, 'Sizing'], true)\ncombine('text', ['CombineUpright', 'GroupAlign', 'Height', 'Indent', 'Justify', 'Orientation', 'Overflow', 'Shadow', 'SpaceCollapse', 'SpaceTrim', 'Spacing', 'Transform', 'UnderlinePosition', 'Wrap'], true)\ncombine('shape', ['ImageThreshold', 'Inside', 'Margin', 'Outside'], true)\ncombine('word', ['Break', 'Spacing', 'Wrap'], true)\ncombine('nav', ['Down', 'Left', 'Right', 'Up'], true)\ncombine('object', ['Fit', 'Position'], true)\ncombine('box', ['DecorationBreak', 'Shadow', 'Sizing', 'Snap'], true)\n\n/** 非常用 style */\n// combine('caret', [color, 'Shape'])\n// combine('counter', ['Increment', 'Reset', 'Set'], true)\n// combine('dropInitial', ['AfterAdjust', 'AfterAlign', 'BeforeAdjust', 'BeforeAlign', size, 'Value'], true)\n// combine('flood', [color, 'Opacity'], true)\n// combine('footnote', ['Display', 'Policy'], true)\n// combine('hyphenateLimit', ['Chars', 'Last', 'Lines', 'Zone'], true)\n// combine('initialLetters', ['Align', 'Wrap'])\n// combine('ruby', ['Align', 'Merge', 'Position'], true)\n\nexport { styleProperties }\n", "import { isUndefined, toCamelCase, toDashed, Shortcuts, warn, isString } from '@tarojs/shared'\nimport { styleProperties } from './style_properties'\nimport { TaroElement } from './element'\nimport { PROPERTY_THRESHOLD } from '../constants'\n\nfunction setStyle (this: Style, newVal: string, styleKey: string) {\n  const old = this[styleKey]\n  if (newVal) {\n    this._usedStyleProp.add(styleKey)\n  }\n\n  process.env.NODE_ENV !== 'production' && warn(\n    isString(newVal) && newVal.length > PROPERTY_THRESHOLD,\n    `Style 属性 ${styleKey} 的值数据量过大，可能会影响渲染性能，考虑使用 CSS 类或其它方案替代。`\n  )\n\n  if (old !== newVal) {\n    this._value[styleKey] = newVal\n    this._element.enqueueUpdate({\n      path: `${this._element._path}.${Shortcuts.Style}`,\n      value: this.cssText\n    })\n  }\n}\n\nfunction initStyle (ctor: typeof Style) {\n  const properties = {}\n\n  for (let i = 0; i < styleProperties.length; i++) {\n    const styleKey = styleProperties[i]\n    properties[styleKey] = {\n      get (this: Style) {\n        return this._value[styleKey] || ''\n      },\n      set (this: Style, newVal: string) {\n        setStyle.call(this, newVal, styleKey)\n      }\n    }\n  }\n\n  Object.defineProperties(ctor.prototype, properties)\n}\n\nfunction isCssVariable (propertyName) {\n  return /^--/.test(propertyName)\n}\n\nexport class Style {\n  public _usedStyleProp: Set<string>\n\n  public _value: Partial<CSSStyleDeclaration>\n\n  public _element: TaroElement\n\n  public constructor (element: TaroElement) {\n    this._element = element\n    this._usedStyleProp = new Set()\n    this._value = {}\n  }\n\n  private setCssVariables (styleKey: string) {\n    this.hasOwnProperty(styleKey) || Object.defineProperty(this, styleKey, {\n      enumerable: true,\n      configurable: true,\n      get: () => {\n        return this._value[styleKey] || ''\n      },\n      set: (newVal: string) => {\n        setStyle.call(this, newVal, styleKey)\n      }\n    })\n  }\n\n  public get cssText () {\n    let text = ''\n    this._usedStyleProp.forEach(key => {\n      const val = this[key]\n      if (!val) return\n      const styleName = isCssVariable(key) ? key : toDashed(key)\n      text += `${styleName}: ${val};`\n    })\n    return text\n  }\n\n  public set cssText (str: string) {\n    if (str == null) {\n      str = ''\n    }\n\n    this._usedStyleProp.forEach(prop => {\n      this.removeProperty(prop)\n    })\n\n    if (str === '') {\n      return\n    }\n\n    const rules = str.split(';')\n\n    for (let i = 0; i < rules.length; i++) {\n      const rule = rules[i].trim()\n      if (rule === '') {\n        continue\n      }\n\n      // 可能存在 'background: url(http:x/y/z)' 的情况\n      const [propName, ...valList] = rule.split(':')\n      const val = valList.join(':')\n\n      if (isUndefined(val)) {\n        continue\n      }\n      this.setProperty(propName.trim(), val.trim())\n    }\n  }\n\n  public setProperty (propertyName: string, value?: string | null) {\n    if (propertyName[0] === '-') {\n      // 支持 webkit 属性或 css 变量\n      this.setCssVariables(propertyName)\n    } else {\n      propertyName = toCamelCase(propertyName)\n    }\n    if (isUndefined(value)) {\n      return\n    }\n\n    if (value === null || value === '') {\n      this.removeProperty(propertyName)\n    } else {\n      this[propertyName] = value\n    }\n  }\n\n  public removeProperty (propertyName: string): string {\n    propertyName = toCamelCase(propertyName)\n    if (!this._usedStyleProp.has(propertyName)) {\n      return ''\n    }\n\n    const value = this[propertyName]\n    this[propertyName] = ''\n    this._usedStyleProp.delete(propertyName)\n    return value\n  }\n\n  public getPropertyValue (propertyName: string) {\n    propertyName = toCamelCase(propertyName)\n    const value = this[propertyName]\n    if (!value) {\n      return ''\n    }\n\n    return value\n  }\n}\n\ninitStyle(Style)\n", "import { NodeType } from './node_types'\n\nimport type { TaroElement } from './element'\n\ntype Filter = (element: TaroElement) => boolean\n\nfunction returnTrue () {\n  return true\n}\n\nexport function treeToArray (root: TaroElement, predict?: Filter): TaroElement[] {\n  const array: TaroElement[] = []\n  const filter = predict ?? returnTrue\n\n  let object: TaroElement | null = root\n\n  while (object) {\n    if (object.nodeType === NodeType.ELEMENT_NODE && filter(object)) {\n      array.push(object)\n    }\n\n    object = following(object, root)\n  }\n\n  return array\n}\n\nfunction following (el: TaroElement, root: TaroElement): TaroElement | null {\n  const firstChild = el.firstChild\n\n  if (firstChild) {\n    return firstChild as TaroElement\n  }\n\n  let current: TaroElement | null = el\n\n  do {\n    if (current === root) {\n      return null\n    }\n\n    const nextSibling = current.nextSibling\n\n    if (nextSibling) {\n      return nextSibling as TaroElement\n    }\n    current = current.parentElement\n  } while (current)\n\n  return null\n}\n", "import type { TaroElement } from './element'\n\nexport class ClassList extends Set<string> {\n  private el: TaroElement\n\n  constructor (className: string, el: TaroElement) {\n    super()\n    className.trim().split(/\\s+/).forEach(super.add.bind(this))\n    this.el = el\n  }\n\n  public get value () {\n    return [...this].join(' ')\n  }\n\n  public add (s: string) {\n    super.add(s)\n    this._update()\n\n    return this\n  }\n\n  public get length (): number {\n    return this.size\n  }\n\n  public remove (s: string) {\n    super.delete(s)\n    this._update()\n  }\n\n  public toggle (s: string) {\n    if (super.has(s)) {\n      super.delete(s)\n    } else {\n      super.add(s)\n    }\n\n    this._update()\n  }\n\n  public replace (s1: string, s2: string) {\n    super.delete(s1)\n    super.add(s2)\n\n    this._update()\n  }\n\n  public contains (s: string) {\n    return super.has(s)\n  }\n\n  public toString () {\n    return this.value\n  }\n\n  private _update () {\n    this.el.className = this.value\n  }\n}\n", "import { inject, injectable } from 'inversify'\nimport { isArray, isUndefined, Shortcuts, EMPTY_OBJ, warn, isString, toCamelCase, isFunction } from '@tarojs/shared'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { TaroNode } from './node'\nimport { NodeType } from './node_types'\nimport { eventSource } from './event-source'\nimport { isElement, isHasExtractProp, shortcutAttr } from '../utils'\nimport { Style } from './style'\nimport { treeToArray } from './tree'\nimport { ClassList } from './class-list'\nimport {\n  ID,\n  CLASS,\n  STYLE,\n  FOCUS,\n  VIEW,\n  STATIC_VIEW,\n  PURE_VIEW,\n  PROPERTY_THRESHOLD,\n  CATCHMOVE,\n  CATCH_VIEW\n} from '../constants'\n\nimport type { TaroEvent } from './event'\nimport type { Attributes, InstanceNamedFactory } from '../interface'\nimport type { TaroNodeImpl } from '../dom-external/node-impl'\nimport type { TaroElementImpl } from '../dom-external/element-impl'\nimport type { Hooks } from '../hooks'\n\n@injectable()\nexport class TaroElement extends TaroNode {\n  public tagName: string\n  public props: Record<string, any> = {}\n  public style: Style\n  public dataset: Record<string, unknown> = EMPTY_OBJ\n  public innerHTML: string\n\n  public constructor (// eslint-disable-next-line @typescript-eslint/indent\n    @inject(SERVICE_IDENTIFIER.TaroNodeImpl) nodeImpl: TaroNodeImpl,\n    @inject(SERVICE_IDENTIFIER.TaroElementFactory) getElement: InstanceNamedFactory,\n    @inject(SERVICE_IDENTIFIER.Hooks) hooks: Hooks,\n    @inject(SERVICE_IDENTIFIER.TaroElementImpl) elementImpl: TaroElementImpl\n  ) {\n    super(nodeImpl, getElement, hooks)\n    elementImpl.bind(this)\n    this.nodeType = NodeType.ELEMENT_NODE\n    this.style = new Style(this)\n    hooks.patchElement(this)\n  }\n\n  private _stopPropagation (event: TaroEvent) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let target = this\n    // eslint-disable-next-line no-cond-assign\n    while ((target = target.parentNode as this)) {\n      const listeners = target.__handlers[event.type]\n\n      if (!isArray(listeners)) {\n        continue\n      }\n\n      for (let i = listeners.length; i--;) {\n        const l = listeners[i]\n        l._stop = true\n      }\n    }\n  }\n\n  public get id (): string {\n    return this.getAttribute(ID)!\n  }\n\n  public set id (val: string) {\n    this.setAttribute(ID, val)\n  }\n\n  public get className (): string {\n    return this.getAttribute(CLASS) || ''\n  }\n\n  public set className (val: string) {\n    this.setAttribute(CLASS, val)\n  }\n\n  public get cssText (): string {\n    return this.getAttribute(STYLE) || ''\n  }\n\n  public get classList (): ClassList {\n    return new ClassList(this.className, this)\n  }\n\n  public get children (): TaroElement[] {\n    return this.childNodes.filter(isElement)\n  }\n\n  public get attributes (): Attributes[] {\n    const props = this.props\n    const propKeys = Object.keys(props)\n    const style = this.style.cssText\n    const attrs = propKeys.map(key => ({ name: key, value: props[key] }))\n    return attrs.concat(style ? { name: STYLE, value: style } : [])\n  }\n\n  public get textContent (): string {\n    let text = ''\n    const childNodes = this.childNodes\n\n    for (let i = 0; i < childNodes.length; i++) {\n      text += childNodes[i].textContent\n    }\n\n    return text\n  }\n\n  public set textContent (text: string) {\n    super.textContent = text\n  }\n\n  public hasAttribute (qualifiedName: string): boolean {\n    return !isUndefined(this.props[qualifiedName])\n  }\n\n  public hasAttributes (): boolean {\n    return this.attributes.length > 0\n  }\n\n  public get focus () {\n    return function () {\n      this.setAttribute(FOCUS, true)\n    }\n  }\n\n  // 兼容 Vue3，详情请见：https://github.com/NervJS/taro/issues/10579\n  public set focus (value) {\n    this.setAttribute(FOCUS, value)\n  }\n\n  public blur () {\n    this.setAttribute(FOCUS, false)\n  }\n\n  public setAttribute (qualifiedName: string, value: any): void {\n    process.env.NODE_ENV !== 'production' && warn(\n      isString(value) && value.length > PROPERTY_THRESHOLD,\n      `元素 ${this.nodeName} 的 属性 ${qualifiedName} 的值数据量过大，可能会影响渲染性能。考虑降低图片转为 base64 的阈值或在 CSS 中使用 base64。`\n    )\n\n    const isPureView = this.nodeName === VIEW && !isHasExtractProp(this) && !this.isAnyEventBinded()\n\n    switch (qualifiedName) {\n      case STYLE:\n        this.style.cssText = value as string\n        break\n      case ID:\n        eventSource.delete(this.uid)\n        value = String(value)\n        this.props[qualifiedName] = this.uid = value\n        eventSource.set(value, this)\n        break\n      default:\n        this.props[qualifiedName] = value as string\n\n        if (qualifiedName.startsWith('data-')) {\n          if (this.dataset === EMPTY_OBJ) {\n            this.dataset = Object.create(null)\n          }\n          this.dataset[toCamelCase(qualifiedName.replace(/^data-/, ''))] = value\n        }\n        break\n    }\n\n    qualifiedName = shortcutAttr(qualifiedName)\n\n    const payload = {\n      path: `${this._path}.${toCamelCase(qualifiedName)}`,\n      value: isFunction(value) ? () => value : value\n    }\n\n    this.hooks.modifySetAttrPayload?.(this, qualifiedName, payload)\n\n    this.enqueueUpdate(payload)\n\n    if (this.nodeName === VIEW) {\n      if (toCamelCase(qualifiedName) === CATCHMOVE) {\n        // catchMove = true: catch-view\n        // catchMove = false: view or static-view\n        this.enqueueUpdate({\n          path: `${this._path}.${Shortcuts.NodeName}`,\n          value: value ? CATCH_VIEW : (\n            this.isAnyEventBinded() ? VIEW : STATIC_VIEW\n          )\n        })\n      } else if (isPureView && isHasExtractProp(this)) {\n        // pure-view => static-view\n        this.enqueueUpdate({\n          path: `${this._path}.${Shortcuts.NodeName}`,\n          value: STATIC_VIEW\n        })\n      }\n    }\n  }\n\n  public removeAttribute (qualifiedName: string) {\n    const isStaticView = this.nodeName === VIEW && isHasExtractProp(this) && !this.isAnyEventBinded()\n\n    if (qualifiedName === STYLE) {\n      this.style.cssText = ''\n    } else {\n      const isInterrupt = this.hooks.onRemoveAttribute?.(this, qualifiedName)\n      if (isInterrupt) {\n        return\n      }\n\n      if (!this.props.hasOwnProperty(qualifiedName)) {\n        return\n      }\n      delete this.props[qualifiedName]\n    }\n\n    qualifiedName = shortcutAttr(qualifiedName)\n\n    const payload = {\n      path: `${this._path}.${toCamelCase(qualifiedName)}`,\n      value: ''\n    }\n\n    this.hooks.modifyRmAttrPayload?.(this, qualifiedName, payload)\n\n    this.enqueueUpdate(payload)\n\n    if (this.nodeName === VIEW) {\n      if (toCamelCase(qualifiedName) === CATCHMOVE) {\n        // catch-view => view or static-view or pure-view\n        this.enqueueUpdate({\n          path: `${this._path}.${Shortcuts.NodeName}`,\n          value: this.isAnyEventBinded() ? VIEW : (isHasExtractProp(this) ? STATIC_VIEW : PURE_VIEW)\n        })\n      } else if (isStaticView && !isHasExtractProp(this)) {\n        // static-view => pure-view\n        this.enqueueUpdate({\n          path: `${this._path}.${Shortcuts.NodeName}`,\n          value: PURE_VIEW\n        })\n      }\n    }\n  }\n\n  public getAttribute (qualifiedName: string): string {\n    const attr = qualifiedName === STYLE ? this.style.cssText : this.props[qualifiedName]\n    return attr ?? ''\n  }\n\n  public getElementsByTagName (tagName: string): TaroElement[] {\n    return treeToArray(this, (el) => {\n      return el.nodeName === tagName || (tagName === '*' && this !== el)\n    })\n  }\n\n  public getElementsByClassName (className: string): TaroElement[] {\n    return treeToArray(this, (el) => {\n      const classList = el.classList\n      const classNames = className.trim().split(/\\s+/)\n      return classNames.every(c => classList.has(c))\n    })\n  }\n\n  public dispatchEvent (event: TaroEvent): boolean {\n    const cancelable = event.cancelable\n\n    const listeners = this.__handlers[event.type]\n\n    if (!isArray(listeners)) {\n      return false\n    }\n\n    for (let i = listeners.length; i--;) {\n      const listener = listeners[i]\n      let result: unknown\n      if (listener._stop) {\n        listener._stop = false\n      } else {\n        result = listener.call(this, event)\n      }\n      if ((result === false || event._end) && cancelable) {\n        event.defaultPrevented = true\n      }\n\n      if (event._end && event._stop) {\n        break\n      }\n    }\n\n    if (event._stop) {\n      this._stopPropagation(event)\n    } else {\n      event._stop = true\n    }\n\n    return listeners != null\n  }\n\n  public addEventListener (type, handler, options) {\n    const name = this.nodeName\n    const SPECIAL_NODES = this.hooks.getSpecialNodes()\n\n    if (!this.isAnyEventBinded() && SPECIAL_NODES.indexOf(name) > -1) {\n      this.enqueueUpdate({\n        path: `${this._path}.${Shortcuts.NodeName}`,\n        value: name\n      })\n    }\n\n    super.addEventListener(type, handler, options)\n  }\n\n  public removeEventListener (type, handler) {\n    super.removeEventListener(type, handler)\n\n    const name = this.nodeName\n    const SPECIAL_NODES = this.hooks.getSpecialNodes()\n\n    if (!this.isAnyEventBinded() && SPECIAL_NODES.indexOf(name) > -1) {\n      this.enqueueUpdate({\n        path: `${this._path}.${Shortcuts.NodeName}`,\n        value: isHasExtractProp(this) ? `static-${name}` : `pure-${name}`\n      })\n    }\n  }\n}\n", "/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\nexport default isArray;\n", "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nexport default freeGlobal;\n", "import freeGlobal from './_freeGlobal.js';\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nexport default root;\n", "import root from './_root.js';\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nexport default Symbol;\n", "import Symbol from './_Symbol.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nexport default getRawTag;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nexport default objectToString;\n", "import Symbol from './_Symbol.js';\nimport getRawTag from './_getRawTag.js';\nimport objectToString from './_objectToString.js';\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nexport default baseGetTag;\n", "/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\nexport default isObjectLike;\n", "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && baseGetTag(value) == symbolTag);\n}\n\nexport default isSymbol;\n", "import isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used to match property names within property paths. */\nvar reIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,\n    reIsPlainProp = /^\\w*$/;\n\n/**\n * Checks if `value` is a property name and not a property path.\n *\n * @private\n * @param {*} value The value to check.\n * @param {Object} [object] The object to query keys on.\n * @returns {boolean} Returns `true` if `value` is a property name, else `false`.\n */\nfunction isKey(value, object) {\n  if (isArray(value)) {\n    return false;\n  }\n  var type = typeof value;\n  if (type == 'number' || type == 'symbol' || type == 'boolean' ||\n      value == null || isSymbol(value)) {\n    return true;\n  }\n  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) ||\n    (object != null && value in Object(object));\n}\n\nexport default isKey;\n", "/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nexport default isObject;\n", "import baseGetTag from './_baseGetTag.js';\nimport isObject from './isObject.js';\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\nexport default isFunction;\n", "import root from './_root.js';\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\nexport default coreJsData;\n", "import coreJsData from './_coreJsData.js';\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\nexport default isMasked;\n", "/** Used for built-in method references. */\nvar funcProto = Function.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\nexport default toSource;\n", "import isFunction from './isFunction.js';\nimport isMasked from './_isMasked.js';\nimport isObject from './isObject.js';\nimport toSource from './_toSource.js';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\nexport default baseIsNative;\n", "/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\nexport default getValue;\n", "import baseIsNative from './_baseIsNative.js';\nimport getValue from './_getValue.js';\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\nexport default getNative;\n", "import getNative from './_getNative.js';\n\n/* Built-in method references that are verified to be native. */\nvar nativeCreate = getNative(Object, 'create');\n\nexport default nativeCreate;\n", "import nativeCreate from './_nativeCreate.js';\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n  this.size = 0;\n}\n\nexport default hashClear;\n", "/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nexport default hashDelete;\n", "import nativeCreate from './_nativeCreate.js';\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\nexport default hashGet;\n", "import nativeCreate from './_nativeCreate.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);\n}\n\nexport default hashHas;\n", "import nativeCreate from './_nativeCreate.js';\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\nexport default hashSet;\n", "import hashClear from './_hashClear.js';\nimport hashDelete from './_hashDelete.js';\nimport hashGet from './_hashGet.js';\nimport hashHas from './_hashHas.js';\nimport hashSet from './_hashSet.js';\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\nexport default Hash;\n", "/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n  this.size = 0;\n}\n\nexport default listCacheClear;\n", "/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\nexport default eq;\n", "import eq from './eq.js';\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\nexport default assocIndexOf;\n", "import assocIndexOf from './_assocIndexOf.js';\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  --this.size;\n  return true;\n}\n\nexport default listCacheDelete;\n", "import assocIndexOf from './_assocIndexOf.js';\n\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  return index < 0 ? undefined : data[index][1];\n}\n\nexport default listCacheGet;\n", "import assocIndexOf from './_assocIndexOf.js';\n\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction listCacheHas(key) {\n  return assocIndexOf(this.__data__, key) > -1;\n}\n\nexport default listCacheHas;\n", "import assocIndexOf from './_assocIndexOf.js';\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\nexport default listCacheSet;\n", "import listCacheClear from './_listCacheClear.js';\nimport listCacheDelete from './_listCacheDelete.js';\nimport listCacheGet from './_listCacheGet.js';\nimport listCacheHas from './_listCacheHas.js';\nimport listCacheSet from './_listCacheSet.js';\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\nexport default ListCache;\n", "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map');\n\nexport default Map;\n", "import Hash from './_Hash.js';\nimport ListCache from './_ListCache.js';\nimport Map from './_Map.js';\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\nexport default mapCacheClear;\n", "/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\nexport default isKeyable;\n", "import isKeyable from './_isKeyable.js';\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\nexport default getMapData;\n", "import getMapData from './_getMapData.js';\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nexport default mapCacheDelete;\n", "import getMapData from './_getMapData.js';\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\nexport default mapCacheGet;\n", "import getMapData from './_getMapData.js';\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\nexport default mapCacheHas;\n", "import getMapData from './_getMapData.js';\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\nexport default mapCacheSet;\n", "import mapCacheClear from './_mapCacheClear.js';\nimport mapCacheDelete from './_mapCacheDelete.js';\nimport mapCacheGet from './_mapCacheGet.js';\nimport mapCacheHas from './_mapCacheHas.js';\nimport mapCacheSet from './_mapCacheSet.js';\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\nexport default MapCache;\n", "import MapCache from './_MapCache.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that memoizes the result of `func`. If `resolver` is\n * provided, it determines the cache key for storing the result based on the\n * arguments provided to the memoized function. By default, the first argument\n * provided to the memoized function is used as the map cache key. The `func`\n * is invoked with the `this` binding of the memoized function.\n *\n * **Note:** The cache is exposed as the `cache` property on the memoized\n * function. Its creation may be customized by replacing the `_.memoize.Cache`\n * constructor with one whose instances implement the\n * [`Map`](http://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-map-prototype-object)\n * method interface of `clear`, `delete`, `get`, `has`, and `set`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to have its output memoized.\n * @param {Function} [resolver] The function to resolve the cache key.\n * @returns {Function} Returns the new memoized function.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n * var other = { 'c': 3, 'd': 4 };\n *\n * var values = _.memoize(_.values);\n * values(object);\n * // => [1, 2]\n *\n * values(other);\n * // => [3, 4]\n *\n * object.a = 2;\n * values(object);\n * // => [1, 2]\n *\n * // Modify the result cache.\n * values.cache.set(object, ['a', 'b']);\n * values(object);\n * // => ['a', 'b']\n *\n * // Replace `_.memoize.Cache`.\n * _.memoize.Cache = WeakMap;\n */\nfunction memoize(func, resolver) {\n  if (typeof func != 'function' || (resolver != null && typeof resolver != 'function')) {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  var memoized = function() {\n    var args = arguments,\n        key = resolver ? resolver.apply(this, args) : args[0],\n        cache = memoized.cache;\n\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    var result = func.apply(this, args);\n    memoized.cache = cache.set(key, result) || cache;\n    return result;\n  };\n  memoized.cache = new (memoize.Cache || MapCache);\n  return memoized;\n}\n\n// Expose `MapCache`.\nmemoize.Cache = MapCache;\n\nexport default memoize;\n", "import memoize from './memoize.js';\n\n/** Used as the maximum memoize cache size. */\nvar MAX_MEMOIZE_SIZE = 500;\n\n/**\n * A specialized version of `_.memoize` which clears the memoized function's\n * cache when it exceeds `MAX_MEMOIZE_SIZE`.\n *\n * @private\n * @param {Function} func The function to have its output memoized.\n * @returns {Function} Returns the new memoized function.\n */\nfunction memoizeCapped(func) {\n  var result = memoize(func, function(key) {\n    if (cache.size === MAX_MEMOIZE_SIZE) {\n      cache.clear();\n    }\n    return key;\n  });\n\n  var cache = result.cache;\n  return result;\n}\n\nexport default memoizeCapped;\n", "import memoizeCapped from './_memoizeCapped.js';\n\n/** Used to match property names within property paths. */\nvar rePropName = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g;\n\n/** Used to match backslashes in property paths. */\nvar reEscapeChar = /\\\\(\\\\)?/g;\n\n/**\n * Converts `string` to a property path array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the property path array.\n */\nvar stringToPath = memoizeCapped(function(string) {\n  var result = [];\n  if (string.charCodeAt(0) === 46 /* . */) {\n    result.push('');\n  }\n  string.replace(rePropName, function(match, number, quote, subString) {\n    result.push(quote ? subString.replace(reEscapeChar, '$1') : (number || match));\n  });\n  return result;\n});\n\nexport default stringToPath;\n", "/**\n * A specialized version of `_.map` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction arrayMap(array, iteratee) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      result = Array(length);\n\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\n\nexport default arrayMap;\n", "import Symbol from './_Symbol.js';\nimport arrayMap from './_arrayMap.js';\nimport isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolToString = symbolProto ? symbolProto.toString : undefined;\n\n/**\n * The base implementation of `_.toString` which doesn't convert nullish\n * values to empty strings.\n *\n * @private\n * @param {*} value The value to process.\n * @returns {string} Returns the string.\n */\nfunction baseToString(value) {\n  // Exit early for strings to avoid a performance hit in some environments.\n  if (typeof value == 'string') {\n    return value;\n  }\n  if (isArray(value)) {\n    // Recursively convert values (susceptible to call stack limits).\n    return arrayMap(value, baseToString) + '';\n  }\n  if (isSymbol(value)) {\n    return symbolToString ? symbolToString.call(value) : '';\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\nexport default baseToString;\n", "import baseToString from './_baseToString.js';\n\n/**\n * Converts `value` to a string. An empty string is returned for `null`\n * and `undefined` values. The sign of `-0` is preserved.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n * @example\n *\n * _.toString(null);\n * // => ''\n *\n * _.toString(-0);\n * // => '-0'\n *\n * _.toString([1, 2, 3]);\n * // => '1,2,3'\n */\nfunction toString(value) {\n  return value == null ? '' : baseToString(value);\n}\n\nexport default toString;\n", "import isArray from './isArray.js';\nimport isKey from './_isKey.js';\nimport stringToPath from './_stringToPath.js';\nimport toString from './toString.js';\n\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {Object} [object] The object to query keys on.\n * @returns {Array} Returns the cast property path array.\n */\nfunction castPath(value, object) {\n  if (isArray(value)) {\n    return value;\n  }\n  return isKey(value, object) ? [value] : stringToPath(toString(value));\n}\n\nexport default castPath;\n", "import isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\nexport default to<PERSON>ey;\n", "import castPath from './_castPath.js';\nimport to<PERSON>ey from './_toKey.js';\n\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\nfunction baseGet(object, path) {\n  path = castPath(path, object);\n\n  var index = 0,\n      length = path.length;\n\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n  return (index && index == length) ? object : undefined;\n}\n\nexport default baseGet;\n", "import baseGet from './_baseGet.js';\n\n/**\n * Gets the value at `path` of `object`. If the resolved value is\n * `undefined`, the `defaultValue` is returned in its place.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @param {*} [defaultValue] The value returned for `undefined` resolved values.\n * @returns {*} Returns the resolved value.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 3 } }] };\n *\n * _.get(object, 'a[0].b.c');\n * // => 3\n *\n * _.get(object, ['a', '0', 'b', 'c']);\n * // => 3\n *\n * _.get(object, 'a.b.c', 'default');\n * // => 'default'\n */\nfunction get(object, path, defaultValue) {\n  var result = object == null ? undefined : baseGet(object, path);\n  return result === undefined ? defaultValue : result;\n}\n\nexport default get;\n", "import type { Options } from './interface'\n\nexport const options: Options = {\n  prerender: true,\n  debug: false\n}\n", "import { options } from './options'\n\nclass Performance {\n  private recorder = new Map<string, number>()\n\n  public start (id: string) {\n    if (!options.debug) {\n      return\n    }\n    this.recorder.set(id, Date.now())\n  }\n\n  public stop (id: string) {\n    if (!options.debug) {\n      return\n    }\n    const now = Date.now()\n    const prev = this.recorder.get(id)!\n    const time = now - prev\n    // eslint-disable-next-line no-console\n    console.log(`${id} 时长： ${time}ms`)\n  }\n}\n\nexport const perf = new Performance()\n", "import { inject, injectable } from 'inversify'\nimport { isFunction, Shortcuts } from '@tarojs/shared'\nimport get from 'lodash-es/get'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { TaroElement } from './element'\nimport { incrementId } from '../utils'\nimport { perf } from '../perf'\nimport { options } from '../options'\nimport {\n  SET_DATA,\n  PAGE_INIT,\n  ROOT_STR,\n  CUSTOM_WRAPPER\n} from '../constants'\n\nimport type { Func, UpdatePayload, UpdatePayloadValue, InstanceNamedFactory, MpInstance, HydratedData } from '../interface'\nimport type { TaroNodeImpl } from '../dom-external/node-impl'\nimport type { TaroElementImpl } from '../dom-external/element-impl'\nimport type { Hooks } from '../hooks'\nimport type { Events } from '../emitter/emitter'\n\nconst eventIncrementId = incrementId()\n\n@injectable()\nexport class TaroRootElement extends TaroElement {\n  private pendingFlush = false\n\n  private updatePayloads: UpdatePayload[] = []\n\n  private updateCallbacks: Func[]= []\n\n  private eventCenter: Events\n\n  public pendingUpdate = false\n\n  public ctx: null | MpInstance = null\n\n  public constructor (// eslint-disable-next-line @typescript-eslint/indent\n    @inject(SERVICE_IDENTIFIER.TaroNodeImpl) nodeImpl: TaroNodeImpl,\n    @inject(SERVICE_IDENTIFIER.TaroElementFactory) getElement: InstanceNamedFactory,\n    @inject(SERVICE_IDENTIFIER.Hooks) hooks: Hooks,\n    @inject(SERVICE_IDENTIFIER.TaroElementImpl) elementImpl: TaroElementImpl,\n    @inject(SERVICE_IDENTIFIER.eventCenter) eventCenter: Events\n  ) {\n    super(nodeImpl, getElement, hooks, elementImpl)\n    this.nodeName = ROOT_STR\n    this.eventCenter = eventCenter\n  }\n\n  public get _path (): string {\n    return ROOT_STR\n  }\n\n  protected get _root (): TaroRootElement {\n    return this\n  }\n\n  public enqueueUpdate (payload: UpdatePayload): void {\n    this.updatePayloads.push(payload)\n\n    if (!this.pendingUpdate && this.ctx !== null) {\n      this.performUpdate()\n    }\n  }\n\n  public performUpdate (initRender = false, prerender?: Func) {\n    this.pendingUpdate = true\n\n    const ctx = this.ctx!\n\n    setTimeout(() => {\n      perf.start(SET_DATA)\n      const data: Record<string, UpdatePayloadValue | ReturnType<HydratedData>> = Object.create(null)\n      const resetPaths = new Set<string>(\n        initRender\n          ? ['root.cn.[0]', 'root.cn[0]']\n          : []\n      )\n\n      while (this.updatePayloads.length > 0) {\n        const { path, value } = this.updatePayloads.shift()!\n        if (path.endsWith(Shortcuts.Childnodes)) {\n          resetPaths.add(path)\n        }\n        data[path] = value\n      }\n\n      for (const path in data) {\n        resetPaths.forEach(p => {\n          // 已经重置了数组，就不需要分别再设置了\n          if (path.includes(p) && path !== p) {\n            delete data[path]\n          }\n        })\n\n        const value = data[path]\n        if (isFunction(value)) {\n          data[path] = value()\n        }\n      }\n\n      if (isFunction(prerender)) {\n        prerender(data)\n      } else {\n        this.pendingUpdate = false\n        const customWrapperUpdate: { ctx: any, data: Record<string, any> }[] = []\n        const customWrapperMap: Map<Record<any, any>, Record<string, any>> = new Map()\n        const normalUpdate = {}\n        if (!initRender) {\n          for (const p in data) {\n            const dataPathArr = p.split('.')\n            let hasCustomWrapper = false\n            for (let i = dataPathArr.length; i > 0; i--) {\n              const allPath = dataPathArr.slice(0, i).join('.')\n              const getData = get(ctx.__data__ || ctx.data, allPath)\n              if (getData && getData.nn && getData.nn === CUSTOM_WRAPPER) {\n                const customWrapperId = getData.uid\n                const customWrapper = ctx.selectComponent(`#${customWrapperId}`)\n                const splitedPath = dataPathArr.slice(i).join('.')\n                if (customWrapper) {\n                  hasCustomWrapper = true\n                  customWrapperMap.set(customWrapper, { ...(customWrapperMap.get(customWrapper) || {}), [`i.${splitedPath}`]: data[p] })\n                }\n                break\n              }\n            }\n            if (!hasCustomWrapper) {\n              normalUpdate[p] = data[p]\n            }\n          }\n          if (customWrapperMap.size > 0) {\n            customWrapperMap.forEach((data, ctx) => {\n              customWrapperUpdate.push({ ctx, data })\n            })\n          }\n        }\n        const updateArrLen = customWrapperUpdate.length\n        if (updateArrLen) {\n          const eventId = `${this._path}_update_${eventIncrementId()}`\n          const eventCenter = this.eventCenter\n          let executeTime = 0\n          eventCenter.once(eventId, () => {\n            executeTime++\n            if (executeTime === updateArrLen + 1) {\n              perf.stop(SET_DATA)\n              if (!this.pendingFlush) {\n                this.flushUpdateCallback()\n              }\n              if (initRender) {\n                perf.stop(PAGE_INIT)\n              }\n            }\n          }, eventCenter)\n          customWrapperUpdate.forEach(item => {\n            if (process.env.NODE_ENV !== 'production' && options.debug) {\n              // eslint-disable-next-line no-console\n              console.log('custom wrapper setData: ', item.data)\n            }\n            item.ctx.setData(item.data, () => {\n              eventCenter.trigger(eventId)\n            })\n          })\n          if (Object.keys(normalUpdate).length) {\n            if (process.env.NODE_ENV !== 'production' && options.debug) {\n              // eslint-disable-next-line no-console\n              console.log('setData:', normalUpdate)\n            }\n            ctx.setData(normalUpdate, () => {\n              eventCenter.trigger(eventId)\n            })\n          }\n        } else {\n          if (process.env.NODE_ENV !== 'production' && options.debug) {\n            // eslint-disable-next-line no-console\n            console.log('setData:', data)\n          }\n          ctx.setData(data, () => {\n            perf.stop(SET_DATA)\n            if (!this.pendingFlush) {\n              this.flushUpdateCallback()\n            }\n            if (initRender) {\n              perf.stop(PAGE_INIT)\n            }\n          })\n        }\n      }\n    }, 0)\n  }\n\n  public enqueueUpdateCallback (cb: Func, ctx?: Record<string, any>) {\n    this.updateCallbacks.push(() => {\n      ctx ? cb.call(ctx) : cb()\n    })\n  }\n\n  public flushUpdateCallback () {\n    this.pendingFlush = false\n    const copies = this.updateCallbacks.slice(0)\n    this.updateCallbacks.length = 0\n    for (let i = 0; i < copies.length; i++) {\n      copies[i]()\n    }\n  }\n}\n", "import { TaroElement } from './element'\nimport {\n  VALUE,\n  INPUT,\n  CHANGE\n} from '../constants'\n\nimport type { TaroEvent } from './event'\n\nexport class FormElement extends TaroElement {\n  public get value () {\n    // eslint-disable-next-line dot-notation\n    const val = this.props[VALUE]\n    return val == null ? '' : val\n  }\n\n  public set value (val: string | boolean | number | any[]) {\n    this.setAttribute(VALUE, val)\n  }\n\n  public dispatchEvent (event: TaroEvent) {\n    if (event.mpEvent) {\n      const val = event.mpEvent.detail.value\n      if (event.type === CHANGE) {\n        this.props.value = val as string\n      } else if (event.type === INPUT) {\n        // Web 规范中表单组件的 value 应该跟着输入改变\n        // 只是改 this.props.value 的话不会进行 setData，因此这里修改 this.value。\n        // 只测试了 React、Vue、Vue3 input 组件的 onInput 事件，onChange 事件不确定有没有副作用，所以暂不修改。\n        this.value = val as string\n      }\n    }\n\n    return super.dispatchEvent(event)\n  }\n}\n", "import { TaroElement } from './element'\n\n// for Vue3\nexport class SVGElement extends TaroElement {}\n", "import { options } from '../../options'\n\ninterface Position {\n  index: number\n  column: number\n  line: number\n}\n\nexport interface Token {\n  type: string\n  content?: string\n  position?: {\n    start?: Position\n    end?: Position\n  }\n  close?: boolean\n}\n\nfunction initPosition (): Position {\n  return {\n    index: 0,\n    column: 0,\n    line: 0\n  }\n}\n\nfunction feedPosition (position: Position, str: string, len: number) {\n  const start = position.index\n  const end = position.index = start + len\n  for (let i = start; i < end; i++) {\n    const char = str.charAt(i)\n    if (char === '\\n') {\n      position.line++\n      position.column = 0\n    } else {\n      position.column++\n    }\n  }\n}\n\nfunction jumpPosition (position: Position, str: string, end: number) {\n  const len = end - position.index\n  return feedPosition(position, str, len)\n}\n\nfunction copyPosition (position: Position) {\n  return {\n    index: position.index,\n    line: position.line,\n    column: position.column\n  }\n}\n\nconst whitespace = /\\s/\nfunction isWhitespaceChar (char: string) {\n  return whitespace.test(char)\n}\n\nconst equalSign = /=/\nfunction isEqualSignChar (char: string) {\n  return equalSign.test(char)\n}\n\nfunction shouldBeIgnore (tagName: string) {\n  const name = tagName.toLowerCase()\n  if (options.html!.skipElements.has(name)) {\n    return true\n  }\n  return false\n}\n\nconst alphanumeric = /[A-Za-z0-9]/\n\nfunction findTextEnd (str: string, index: number) {\n  while (true) {\n    const textEnd = str.indexOf('<', index)\n    if (textEnd === -1) {\n      return textEnd\n    }\n    const char = str.charAt(textEnd + 1)\n    if (char === '/' || char === '!' || alphanumeric.test(char)) {\n      return textEnd\n    }\n    index = textEnd + 1\n  }\n}\n\nfunction isWordEnd (cursor: number, wordBegin: number, html: string) {\n  if (!isWhitespaceChar(html.charAt(cursor))) return false\n\n  const len = html.length\n\n  // backwrad\n  for (let i = cursor - 1; i > wordBegin; i--) {\n    const char = html.charAt(i)\n    if (!isWhitespaceChar(char)) {\n      if (isEqualSignChar(char)) return false\n      break\n    }\n  }\n\n  // forward\n  for (let i = cursor + 1; i < len; i++) {\n    const char = html.charAt(i)\n    if (!isWhitespaceChar(char)) {\n      if (isEqualSignChar(char)) return false\n      return true\n    }\n  }\n}\n\nexport class Scaner {\n  private tokens: Token[] = []\n\n  private position: Position = initPosition()\n\n  private html: string\n\n  constructor (html: string) {\n    this.html = html\n  }\n\n  public scan (): Token[] {\n    const { html, position } = this\n    const len = html.length\n\n    while (position.index < len) {\n      const start = position.index\n      this.scanText()\n      if (position.index === start) {\n        const isComment = html.startsWith('!--', start + 1)\n        if (isComment) {\n          this.scanComment()\n        } else {\n          const tagName = this.scanTag()\n          if (shouldBeIgnore(tagName)) {\n            this.scanSkipTag(tagName)\n          }\n        }\n      }\n    }\n\n    return this.tokens\n  }\n\n  private scanText () {\n    const type = 'text'\n    const { html, position } = this\n    let textEnd = findTextEnd(html, position.index)\n    if (textEnd === position.index) {\n      return\n    }\n    if (textEnd === -1) {\n      textEnd = html.length\n    }\n\n    const start = copyPosition(position)\n    const content = html.slice(position.index, textEnd)\n    jumpPosition(position, html, textEnd)\n    const end = copyPosition(position)\n    this.tokens.push({ type, content, position: { start, end } })\n  }\n\n  private scanComment () {\n    const type = 'comment'\n    const { html, position } = this\n    const start = copyPosition(position)\n    feedPosition(position, html, 4) // \"<!--\".length\n    let contentEnd = html.indexOf('-->', position.index)\n    let commentEnd = contentEnd + 3 // \"-->\".length\n    if (contentEnd === -1) {\n      contentEnd = commentEnd = html.length\n    }\n\n    const content = html.slice(position.index, contentEnd)\n    jumpPosition(position, html, commentEnd)\n    this.tokens.push({\n      type,\n      content,\n      position: {\n        start,\n        end: copyPosition(position)\n      }\n    })\n  }\n\n  private scanTag () {\n    this.scanTagStart()\n    const tagName = this.scanTagName()\n    this.scanAttrs()\n    this.scanTagEnd()\n\n    return tagName\n  }\n\n  private scanTagStart () {\n    const type = 'tag-start'\n    const { html, position } = this\n\n    const secondChar = html.charAt(position.index + 1)\n    const close = secondChar === '/'\n    const start = copyPosition(position)\n    feedPosition(position, html, close ? 2 : 1)\n    this.tokens.push({ type, close, position: { start } })\n  }\n\n  private scanTagEnd () {\n    const type = 'tag-end'\n    const { html, position } = this\n\n    const firstChar = html.charAt(position.index)\n    const close = firstChar === '/'\n    feedPosition(position, html, close ? 2 : 1)\n    const end = copyPosition(position)\n    this.tokens.push({ type, close, position: { end } })\n  }\n\n  private scanTagName (): string {\n    const type = 'tag'\n    const { html, position } = this\n    const len = html.length\n    let start = position.index\n    while (start < len) {\n      const char = html.charAt(start)\n      const isTagChar = !(isWhitespaceChar(char) || char === '/' || char === '>')\n      if (isTagChar) break\n      start++\n    }\n\n    let end = start + 1\n    while (end < len) {\n      const char = html.charAt(end)\n      const isTagChar = !(isWhitespaceChar(char) || char === '/' || char === '>')\n      if (!isTagChar) break\n      end++\n    }\n\n    jumpPosition(position, html, end)\n    const tagName = html.slice(start, end)\n    this.tokens.push({\n      type,\n      content: tagName\n    })\n\n    return tagName\n  }\n\n  private scanAttrs () {\n    const { html, position, tokens } = this\n    let cursor = position.index\n    let quote: string | null = null // null, single-, or double-quote\n    let wordBegin = cursor // index of word start\n    const words: string[] = [] // \"key\", \"key=value\", \"key='value'\", etc\n    const len = html.length\n    while (cursor < len) {\n      const char = html.charAt(cursor)\n      if (quote) {\n        const isQuoteEnd = char === quote\n        if (isQuoteEnd) {\n          quote = null\n        }\n        cursor++\n        continue\n      }\n\n      const isTagEnd = char === '/' || char === '>'\n      if (isTagEnd) {\n        if (cursor !== wordBegin) {\n          words.push(html.slice(wordBegin, cursor))\n        }\n        break\n      }\n\n      if (isWordEnd(cursor, wordBegin, html)) {\n        if (cursor !== wordBegin) {\n          words.push(html.slice(wordBegin, cursor))\n        }\n        wordBegin = cursor + 1\n        cursor++\n        continue\n      }\n\n      const isQuoteStart = char === '\\'' || char === '\"'\n      if (isQuoteStart) {\n        quote = char\n        cursor++\n        continue\n      }\n\n      cursor++\n    }\n\n    jumpPosition(position, html, cursor)\n\n    const wLen = words.length\n    const type = 'attribute'\n    for (let i = 0; i < wLen; i++) {\n      const word = words[i]\n      const isNotPair = word.includes('=')\n      if (isNotPair) {\n        const secondWord = words[i + 1]\n        if (secondWord && secondWord.startsWith('=')) {\n          if (secondWord.length > 1) {\n            const newWord = word + secondWord\n            tokens.push({ type, content: newWord })\n            i += 1\n            continue\n          }\n          const thirdWord = words[i + 2]\n          i += 1\n          if (thirdWord) {\n            const newWord = word + '=' + thirdWord\n            tokens.push({ type, content: newWord })\n            i += 1\n            continue\n          }\n        }\n      }\n      if (word.endsWith('=')) {\n        const secondWord = words[i + 1]\n        if (secondWord && !secondWord.includes('=')) {\n          const newWord = word + secondWord\n          tokens.push({ type, content: newWord })\n          i += 1\n          continue\n        }\n\n        const newWord = word.slice(0, -1)\n        tokens.push({ type, content: newWord })\n        continue\n      }\n\n      tokens.push({ type, content: word })\n    }\n  }\n\n  private scanSkipTag (tagName: string) {\n    const { html, position } = this\n    const safeTagName = tagName.toLowerCase()\n    const len = html.length\n    while (position.index < len) {\n      const nextTag = html.indexOf('</', position.index)\n      if (nextTag === -1) {\n        this.scanText()\n        break\n      }\n\n      jumpPosition(position, html, nextTag)\n      const name = this.scanTag()\n      if (safeTagName === name.toLowerCase()) {\n        break\n      }\n    }\n  }\n}\n", "import { internalComponents } from '@tarojs/shared'\n\nexport function makeMap (\n  str: string,\n  expectsLowerCase?: boolean\n): (key: string) => boolean {\n  const map: Record<string, boolean> = Object.create(null)\n  const list: Array<string> = str.split(',')\n  for (let i = 0; i < list.length; i++) {\n    map[list[i]] = true\n  }\n  return expectsLowerCase ? val => !!map[val.toLowerCase()] : val => !!map[val]\n}\n\nexport const specialMiniElements = {\n  img: 'image',\n  iframe: 'web-view'\n}\n\nconst internalCompsList = Object.keys(internalComponents)\n  .map(i => i.toLowerCase())\n  .join(',')\n\n// https://developers.weixin.qq.com/miniprogram/dev/component\nexport const isMiniElements = makeMap(internalCompsList, true)\n\n// https://developer.mozilla.org/en-US/docs/Web/HTML/Inline_elements\nexport const isInlineElements = makeMap('a,i,abbr,iframe,select,acronym,slot,small,span,bdi,kbd,strong,big,map,sub,sup,br,mark,mark,meter,template,canvas,textarea,cite,object,time,code,output,u,data,picture,tt,datalist,var,dfn,del,q,em,s,embed,samp,b', true)\n\n// https://developer.mozilla.org/en-US/docs/Web/HTML/Block-level_elements\nexport const isBlockElements = makeMap('address,fieldset,li,article,figcaption,main,aside,figure,nav,blockquote,footer,ol,details,form,p,dialog,h1,h2,h3,h4,h5,h6,pre,dd,header,section,div,hgroup,table,dl,hr,ul,dt', true)\n", "export function unquote (str: string): string {\n  const car = str.charAt(0)\n  const end = str.length - 1\n  const isQuoteStart = car === '\"' || car === \"'\"\n  if (isQuoteStart && car === str.charAt(end)) {\n    return str.slice(1, end)\n  }\n  return str\n}\n", "import { NodeType } from '../../dom/node_types'\nimport { unquote } from './utils'\n\nimport type { TaroNode } from '../../dom/node'\nimport type { ParsedTaroElement } from './parser'\n\nconst LEFT_BRACKET = '{'\nconst RIGHT_BRACKET = '}'\nconst CLASS_SELECTOR = '.'\nconst ID_SELECTOR = '#'\nconst CHILD_COMBINATOR = '>'\nconst GENERAL_SIBLING_COMBINATOR = '~'\nconst ADJACENT_SIBLING_COMBINATOR = '+'\n\ninterface ISelector {\n  isChild: boolean\n  isGeneralSibling: boolean\n  isAdjacentSibling: boolean\n  tag: string | null\n  id: string | null\n  class: string[]\n  attrs: {\n    all: boolean\n    key: string\n    value?: string | null\n  }[]\n}\n\ninterface IStyle {\n  content: string\n  selectorList: ISelector[]\n}\n\nexport default class StyleTagParser {\n  styles: IStyle[]= []\n\n  extractStyle (src: string) {\n    const REG_STYLE = /<style\\s?[^>]*>((.|\\n|\\s)+?)<\\/style>/g\n    let html = src\n    // let html = src.replace(/\\n/g, '')\n\n    html = html.replace(REG_STYLE, (_, $1: string) => {\n      const style = $1.trim()\n      this.stringToSelector(style)\n      return ''\n    })\n\n    return html.trim()\n  }\n\n  stringToSelector (style: string) {\n    let lb = style.indexOf(LEFT_BRACKET)\n\n    while (lb > -1) {\n      const rb = style.indexOf(RIGHT_BRACKET)\n      const selectors = style.slice(0, lb).trim()\n\n      let content = style.slice(lb + 1, rb)\n      content = content.replace(/:(.*);/g, function (_, $1) {\n        const t = $1.trim().replace(/ +/g, '+++')\n        return `:${t};`\n      })\n      content = content.replace(/ /g, '')\n      content = content.replace(/\\+\\+\\+/g, ' ')\n\n      if (!(/;$/.test(content))) {\n        content += ';'\n      }\n      selectors.split(',').forEach(src => {\n        const selectorList = this.parseSelector(src)\n        this.styles.push({\n          content,\n          selectorList\n        })\n      })\n      style = style.slice(rb + 1)\n      lb = style.indexOf(LEFT_BRACKET)\n    }\n    // console.log('res this.styles: ', this.styles)\n  }\n\n  parseSelector (src: string) {\n    const list = src\n      .trim()\n      .replace(/ *([>~+]) */g, ' $1')\n      .replace(/ +/g, ' ')\n      .replace(/\\[\\s*([^[\\]=\\s]+)\\s*=\\s*([^[\\]=\\s]+)\\s*\\]/g, '[$1=$2]')\n      .split(' ')\n    const selectors = list.map(item => {\n      const firstChar = item.charAt(0)\n      const selector: ISelector = {\n        isChild: firstChar === CHILD_COMBINATOR,\n        isGeneralSibling: firstChar === GENERAL_SIBLING_COMBINATOR,\n        isAdjacentSibling: firstChar === ADJACENT_SIBLING_COMBINATOR,\n        tag: null,\n        id: null,\n        class: [],\n        attrs: []\n      }\n\n      item = item.replace(/^[>~+]/, '')\n\n      // 属性选择器\n      item = item.replace(/\\[(.+?)\\]/g, function (_, $1: string) {\n        const [key, value] = $1.split('=')\n        const all = $1.indexOf('=') === -1\n\n        const attr = {\n          all,\n          key,\n          value: all ? null : value\n        }\n        selector.attrs.push(attr)\n        return ''\n      })\n\n      item = item.replace(/([.#][A-Za-z0-9-_]+)/g, function (_, $1: string) {\n        if ($1[0] === ID_SELECTOR) {\n          // id 选择器\n          selector.id = $1.substr(1)\n        } else if ($1[0] === CLASS_SELECTOR) {\n          // class 选择器\n          selector.class.push($1.substr(1))\n        }\n        return ''\n      })\n\n      // 标签选择器\n      if (item !== '') {\n        selector.tag = item\n      }\n\n      return selector\n    })\n    return selectors\n  }\n\n  matchStyle (tagName: string, el: ParsedTaroElement, list: number[]): string {\n    const res = sortStyles(this.styles).reduce((str, { content, selectorList }, i) => {\n      let idx = list[i]\n      let selector = selectorList[idx]\n      const nextSelector = selectorList[idx + 1]\n\n      if (nextSelector?.isGeneralSibling || nextSelector?.isAdjacentSibling) {\n        selector = nextSelector\n        idx += 1\n        list[i] += 1\n      }\n\n      let isMatch = this.matchCurrent(tagName, el, selector)\n\n      if (isMatch && selector.isGeneralSibling) {\n        let prev: ParsedTaroElement = getPreviousElement(el)\n        while (prev) {\n          if (prev.h5tagName && this.matchCurrent(prev.h5tagName, prev, selectorList[idx - 1])) {\n            isMatch = true\n            break\n          }\n          prev = getPreviousElement(prev)\n          isMatch = false\n        }\n      }\n      if (isMatch && selector.isAdjacentSibling) {\n        const prev: ParsedTaroElement = getPreviousElement(el)\n        if (!prev || !prev.h5tagName) {\n          isMatch = false\n        } else {\n          const isSiblingMatch = this.matchCurrent(prev.h5tagName, prev, selectorList[idx - 1])\n          if (!isSiblingMatch) {\n            isMatch = false\n          }\n        }\n      }\n\n      if (isMatch) {\n        if (idx === selectorList.length - 1) {\n          return str + content\n        } else if (idx < selectorList.length - 1) {\n          list[i] += 1\n        }\n      } else {\n        // 直接子代组合器: >\n        if (selector.isChild && idx > 0) {\n          list[i] -= 1\n          if (this.matchCurrent(tagName, el, selectorList[list[i]])) {\n            list[i] += 1\n          }\n        }\n      }\n\n      return str\n    }, '')\n    return res\n  }\n\n  matchCurrent (tagName: string, el: ParsedTaroElement, selector: ISelector): boolean {\n    // 标签选择器\n    if (selector.tag && selector.tag !== tagName) return false\n\n    // id 选择器\n    if (selector.id && selector.id !== el.id) return false\n\n    // class 选择器\n    if (selector.class.length) {\n      const classList = el.className.split(' ')\n      for (let i = 0; i < selector.class.length; i++) {\n        const cls = selector.class[i]\n        if (classList.indexOf(cls) === -1) {\n          return false\n        }\n      }\n    }\n\n    // 属性选择器\n    if (selector.attrs.length) {\n      for (let i = 0; i < selector.attrs.length; i++) {\n        const { all, key, value } = selector.attrs[i]\n        if (all && !el.hasAttribute(key)) {\n          return false\n        } else {\n          const attr = el.getAttribute(key)\n          if (attr !== unquote(value || '')) {\n            return false\n          }\n        }\n      }\n    }\n\n    return true\n  }\n}\n\nfunction getPreviousElement (el: TaroNode) {\n  const parent = el.parentElement\n  if (!parent) return null\n\n  const prev = el.previousSibling\n  if (!prev) return null\n\n  if (prev.nodeType === NodeType.ELEMENT_NODE) {\n    return prev\n  } else {\n    return getPreviousElement(prev)\n  }\n}\n\n// 根据 css selector 权重排序: 权重大的靠后\n// @WARN 不考虑伪类\n// https://developer.mozilla.org/en-US/docs/Learn/CSS/Building_blocks/Cascade_and_inheritance#specificity_2\nfunction sortStyles (styles: IStyle[]) {\n  return styles.sort((s1, s2) => {\n    const hundreds1 = getHundredsWeight(s1.selectorList)\n    const hundreds2 = getHundredsWeight(s2.selectorList)\n\n    if (hundreds1 !== hundreds2) return hundreds1 - hundreds2\n\n    const tens1 = getTensWeight(s1.selectorList)\n    const tens2 = getTensWeight(s2.selectorList)\n\n    if (tens1 !== tens2) return tens1 - tens2\n\n    const ones1 = getOnesWeight(s1.selectorList)\n    const ones2 = getOnesWeight(s2.selectorList)\n\n    return ones1 - ones2\n  })\n}\n\nfunction getHundredsWeight (selectors: ISelector[]) {\n  return selectors.reduce((pre, cur) => pre + (cur.id ? 1 : 0), 0)\n}\n\nfunction getTensWeight (selectors: ISelector[]) {\n  return selectors.reduce((pre, cur) => pre + cur.class.length + cur.attrs.length, 0)\n}\n\nfunction getOnesWeight (selectors: ISelector[]) {\n  return selectors.reduce((pre, cur) => pre + (cur.tag ? 1 : 0), 0)\n}\n", "import { isFunction } from '@tarojs/shared'\nimport { <PERSON><PERSON><PERSON>, Token } from './scaner'\nimport { options } from '../../options'\nimport { specialMiniElements, isMiniElements, isBlockElements, isInlineElements } from './tags'\nimport StyleTagParser from './style'\nimport { unquote } from './utils'\n\nimport type { TaroElement } from '../../dom/element'\nimport type { TaroDocument } from '../../dom/document'\n\ninterface State {\n  tokens: Token[]\n  cursor: number\n  stack: Element[]\n}\n\nconst closingTagAncestorBreakers = {\n  li: ['ul', 'ol', 'menu'],\n  dt: ['dl'],\n  dd: ['dl'],\n  tbody: ['table'],\n  thead: ['table'],\n  tfoot: ['table'],\n  tr: ['table'],\n  td: ['table']\n}\n\ninterface Node {\n  type: string;\n}\n\ninterface Comment extends Node {\n  type: 'comment'\n  content: string\n}\n\nexport interface Text extends Node {\n  type: 'text'\n  content: string\n}\n\nexport interface Element extends Node {\n  type: 'element'\n  tagName: string\n  children: ChildNode[]\n  attributes: string[]\n}\n\nexport interface ParsedTaroElement extends TaroElement{\n  h5tagName?: string\n}\n\ntype ChildNode = Comment | Text | Element\n\nfunction hasTerminalParent (tagName: string, stack: Element[]) {\n  const tagParents: undefined | string[] = closingTagAncestorBreakers[tagName]\n  if (tagParents) {\n    let currentIndex = stack.length - 1\n    while (currentIndex >= 0) {\n      const parentTagName = stack[currentIndex].tagName\n      if (parentTagName === tagName) {\n        break\n      }\n      if (tagParents && tagParents.includes(parentTagName!)) {\n        return true\n      }\n      currentIndex--\n    }\n  }\n  return false\n}\n\nfunction getTagName (tag: string) {\n  if (options.html!.renderHTMLTag) {\n    return tag\n  }\n\n  if (specialMiniElements[tag]) {\n    return specialMiniElements[tag]\n  } else if (isMiniElements(tag)) {\n    return tag\n  } else if (isBlockElements(tag)) {\n    return 'view'\n  } else if (isInlineElements(tag)) {\n    return 'text'\n  }\n\n  return 'view'\n}\n\nfunction splitEqual (str: string) {\n  const sep = '='\n  const idx = str.indexOf(sep)\n  if (idx === -1) return [str]\n  const key = str.slice(0, idx).trim()\n  const value = str.slice(idx + sep.length).trim()\n  return [key, value]\n}\n\nfunction format (\n  children: ChildNode[],\n  document: TaroDocument,\n  styleOptions: {\n    styleTagParser: StyleTagParser\n    descendantList: number[]\n  },\n  parent?: TaroElement\n) {\n  return children\n    .filter(child => {\n      // 过滤注释和空文本节点\n      if (child.type === 'comment') {\n        return false\n      } else if (child.type === 'text') {\n        return child.content !== ''\n      }\n      return true\n    })\n    .map((child: Text | Element) => {\n      // 文本节点\n      if (child.type === 'text') {\n        let text = document.createTextNode(child.content)\n        if (isFunction(options.html!.transformText)) {\n          text = options.html!.transformText(text, child)\n        }\n        parent?.appendChild(text)\n        return text\n      }\n\n      const el: ParsedTaroElement = document.createElement(getTagName(child.tagName))\n      el.h5tagName = child.tagName\n\n      parent?.appendChild(el)\n\n      if (!options.html!.renderHTMLTag) {\n        el.className = `h5-${child.tagName}`\n      }\n\n      for (let i = 0; i < child.attributes.length; i++) {\n        const attr = child.attributes[i]\n        const [key, value] = splitEqual(attr)\n        if (key === 'class') {\n          el.className += ' ' + unquote(value)\n        } else if (key[0] === 'o' && key[1] === 'n') {\n          continue\n        } else {\n          el.setAttribute(key, value == null ? true : unquote(value))\n        }\n      }\n\n      const { styleTagParser, descendantList } = styleOptions\n      const list = descendantList.slice()\n      const style = styleTagParser.matchStyle(child.tagName, el, list)\n\n      el.setAttribute('style', style + el.style.cssText)\n      // console.log('style, ', style)\n\n      format(child.children, document, {\n        styleTagParser,\n        descendantList: list\n      }, el)\n\n      if (isFunction(options.html!.transformElement)) {\n        return options.html!.transformElement(el, child)\n      }\n\n      return el\n    })\n}\n\nexport function parser (html: string, document: TaroDocument) {\n  const styleTagParser = new StyleTagParser()\n  html = styleTagParser.extractStyle(html)\n\n  const tokens = new Scaner(html).scan()\n\n  const root: Element = { tagName: '', children: [], type: 'element', attributes: [] }\n\n  const state = { tokens, options, cursor: 0, stack: [root] }\n  parse(state)\n\n  return format(root.children, document, {\n    styleTagParser,\n    descendantList: Array(styleTagParser.styles.length).fill(0)\n  })\n}\n\nfunction parse (state: State) {\n  const { tokens, stack } = state\n  let { cursor } = state\n\n  const len = tokens.length\n\n  let nodes = stack[stack.length - 1].children\n\n  while (cursor < len) {\n    const token = tokens[cursor]\n    if (token.type !== 'tag-start') {\n      // comment or text\n      nodes.push(token as ChildNode)\n      cursor++\n      continue\n    }\n\n    const tagToken = tokens[++cursor]\n    cursor++\n    const tagName = tagToken.content!.toLowerCase()\n    if (token.close) {\n      let index = stack.length\n      let shouldRewind = false\n      while (--index > -1) {\n        if (stack[index].tagName === tagName) {\n          shouldRewind = true\n          break\n        }\n      }\n      while (cursor < len) {\n        const endToken = tokens[cursor]\n        if (endToken.type !== 'tag-end') break\n        cursor++\n      }\n      if (shouldRewind) {\n        stack.splice(index)\n        break\n      } else {\n        continue\n      }\n    }\n\n    const isClosingTag = options.html!.closingElements.has(tagName)\n    let shouldRewindToAutoClose = isClosingTag\n    if (shouldRewindToAutoClose) {\n      shouldRewindToAutoClose = !hasTerminalParent(tagName, stack)\n    }\n\n    if (shouldRewindToAutoClose) {\n      let currentIndex = stack.length - 1\n      while (currentIndex > 0) {\n        if (tagName === stack[currentIndex].tagName) {\n          stack.splice(currentIndex)\n          const previousIndex = currentIndex - 1\n          nodes = stack[previousIndex].children\n          break\n        }\n        currentIndex = currentIndex - 1\n      }\n    }\n\n    const attributes: string[] = []\n    let attrToken: Token\n    while (cursor < len) {\n      attrToken = tokens[cursor]\n      if (attrToken.type === 'tag-end') break\n      attributes.push(attrToken.content!)\n      cursor++\n    }\n\n    cursor++\n    const children: Element[] = []\n    const element: Element = {\n      type: 'element',\n      tagName: tagToken.content!,\n      attributes,\n      children\n    }\n    nodes.push(element)\n\n    const hasChildren = !(attrToken!.close || options.html!.voidElements.has(tagName))\n    if (hasChildren) {\n      stack.push({ tagName, children } as any)\n      const innerState: State = { tokens, cursor, stack }\n      parse(innerState)\n      cursor = innerState.cursor\n    }\n  }\n\n  state.cursor = cursor\n}\n", "import { parser } from './parser'\nimport { options } from '../../options'\n\nimport type { TaroNode } from '../../dom/node'\nimport type { TaroDocument } from '../../dom/document'\n\noptions.html = {\n  skipElements: new Set(['style', 'script']),\n  voidElements: new Set([\n    '!doctype', 'area', 'base', 'br', 'col', 'command',\n    'embed', 'hr', 'img', 'input', 'keygen', 'link',\n    'meta', 'param', 'source', 'track', 'wbr'\n  ]),\n  closingElements: new Set([\n    'html', 'head', 'body', 'p', 'dt', 'dd', 'li', 'option',\n    'thead', 'th', 'tbody', 'tr', 'td', 'tfoot', 'colgroup'\n  ]),\n  renderHTMLTag: false\n}\n\nexport function setInnerHTML (element: TaroNode, html: string, getDoc: () => TaroDocument) {\n  while (element.firstChild) {\n    element.removeChild(element.firstChild)\n  }\n  const children = parser(html, getDoc())\n\n  for (let i = 0; i < children.length; i++) {\n    element.appendChild(children[i])\n  }\n}\n", "import {\n  STYLE,\n  DATASET,\n  PROPS,\n  OBJECT\n} from '../constants'\nimport { parser } from '../dom-external/inner-html/parser'\nimport { GetDoc } from '../interface'\nimport { NodeType } from '../dom/node_types'\n\nimport type { Ctx } from '../interface'\n\nexport type IPosition = 'beforebegin' | 'afterbegin' | 'beforeend' | 'afterend'\n\n/**\n * An implementation of `Element.insertAdjacentHTML()`\n * to support Vue 3 with a version of or greater than `vue@3.1.2`\n */\nexport function insertAdjacentHTMLImpl (\n  position: IPosition,\n  html: string,\n  getDoc: GetDoc\n) {\n  const parsedNodes = parser(html, getDoc())\n\n  for (let i = 0; i < parsedNodes.length; i++) {\n    const n = parsedNodes[i]\n\n    switch (position) {\n      case 'beforebegin':\n        this.parentNode?.insertBefore(n, this)\n        break\n      case 'afterbegin':\n        if (this.hasChildNodes()) {\n          this.insertBefore(n, this.childNodes[0])\n        } else {\n          this.appendChild(n)\n        }\n        break\n      case 'beforeend':\n        this.appendChild(n)\n        break\n      case 'afterend':\n        this.parentNode?.appendChild(n)\n        break\n    }\n  }\n}\n\nexport function cloneNode (ctx: Ctx, getDoc, isDeep = false) {\n  const document = getDoc()\n  let newNode\n\n  if (ctx.nodeType === NodeType.ELEMENT_NODE) {\n    newNode = document.createElement(ctx.nodeName)\n  } else if (ctx.nodeType === NodeType.TEXT_NODE) {\n    newNode = document.createTextNode('')\n  }\n\n  for (const key in this) {\n    const value: any = this[key]\n    if ([PROPS, DATASET].includes(key) && typeof value === OBJECT) {\n      newNode[key] = { ...value }\n    } else if (key === '_value') {\n      newNode[key] = value\n    } else if (key === STYLE) {\n      newNode.style._value = { ...value._value }\n      newNode.style._usedStyleProp = new Set(Array.from(value._usedStyleProp))\n    }\n  }\n\n  if (isDeep) {\n    newNode.childNodes = ctx.childNodes.map(node => node.cloneNode(true))\n  }\n\n  return newNode\n}\n", "import { inject, injectable } from 'inversify'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { ElementNames, InstanceNamedFactory } from '../interface'\nimport { setInnerHTML } from '../dom-external/inner-html/html'\nimport { cloneNode, insertAdjacentHTMLImpl } from './node'\n\nimport type { Ctx, GetDoc } from '../interface'\nimport type { TaroDocument } from '../dom/document'\nimport type { IPosition } from './node'\n\ndeclare const ENABLE_INNER_HTML: boolean\ndeclare const ENABLE_ADJACENT_HTML: boolean\ndeclare const ENABLE_CLONE_NODE: boolean\n\n@injectable()\nexport class TaroNodeImpl {\n  public getDoc: GetDoc\n\n  constructor (// eslint-disable-next-line @typescript-eslint/indent\n    @inject(SERVICE_IDENTIFIER.TaroElementFactory) getElement: InstanceNamedFactory\n  ) {\n    this.getDoc = () => getElement<TaroDocument>(ElementNames.Document)()\n  }\n\n  public bind (ctx: Ctx) {\n    const getDoc = this.getDoc\n\n    if (ENABLE_INNER_HTML) {\n      bindInnerHTML(ctx, getDoc)\n      if (ENABLE_ADJACENT_HTML) {\n        bindAdjacentHTML(ctx, getDoc)\n      }\n    }\n    if (ENABLE_CLONE_NODE) {\n      ctx.cloneNode = cloneNode.bind(ctx, ctx, getDoc)\n    }\n  }\n}\n\nfunction bindInnerHTML (ctx, getDoc) {\n  Object.defineProperty(ctx, 'innerHTML', {\n    configurable: true,\n    enumerable: true,\n    set (html: string) {\n      setInnerHTML.call(ctx, ctx, html, getDoc)\n    },\n    get (): string {\n      return ''\n    }\n  })\n}\n\nfunction bindAdjacentHTML (ctx, getDoc) {\n  ctx.insertAdjacentHTML = function (position: IPosition, html: string) {\n    insertAdjacentHTMLImpl.call(ctx, position, html, getDoc)\n  }\n}\n", "import { options } from '../options'\nimport { ElementNames } from '../interface'\nimport { DOCUMENT_FRAGMENT } from '../constants'\n\nimport type { Ctx } from '../interface'\n\nexport function getBoundingClientRectImpl (): Promise<null> {\n  if (!options.miniGlobal) return Promise.resolve(null)\n  return new Promise(resolve => {\n    const query = options.miniGlobal.createSelectorQuery()\n    query.select(`#${this.uid}`).boundingClientRect(res => {\n      resolve(res)\n    }).exec()\n  })\n}\n\nexport function getTemplateContent (ctx: Ctx): string | undefined {\n  if (ctx.nodeName === 'template') {\n    const content = ctx._getElement(ElementNames.Element)(DOCUMENT_FRAGMENT)\n    content.childNodes = ctx.childNodes\n    ctx.childNodes = [content]\n    content.parentNode = ctx\n    content.childNodes.forEach(nodes => {\n      nodes.parentNode = content\n    })\n    return content\n  }\n}\n", "import { injectable } from 'inversify'\nimport { getBoundingClientRectImpl, getTemplateContent } from './element'\n\nimport type { Ctx } from '../interface'\n\ndeclare const ENABLE_SIZE_APIS: boolean\ndeclare const ENABLE_TEMPLATE_CONTENT: boolean\n\n@injectable()\nexport class TaroElementImpl {\n  bind (ctx: Ctx) {\n    if (ENABLE_SIZE_APIS) {\n      ctx.getBoundingClientRect = async function (...args: any[]) {\n        return await getBoundingClientRectImpl.apply(ctx, args)\n      }\n    }\n    if (ENABLE_TEMPLATE_CONTENT) {\n      bindContent(ctx)\n    }\n  }\n}\n\nfunction bindContent (ctx: Ctx) {\n  Object.defineProperty(ctx, 'content', {\n    configurable: true,\n    enumerable: true,\n    get () {\n      return getTemplateContent(ctx)\n    }\n  })\n}\n", "import { inject, injectable } from 'inversify'\nimport { controlledComponent, isUndefined } from '@tarojs/shared'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { TaroElement } from '../dom/element'\nimport { NodeType } from '../dom/node_types'\nimport { eventSource } from '../dom/event-source'\nimport { ElementNames, InstanceFactory, InstanceNamedFactory } from '../interface'\nimport {\n  ROOT_STR,\n  DOCUMENT_ELEMENT_NAME,\n  COMMENT\n} from '../constants'\n\nimport type { FormElement } from '../dom/form'\nimport type { TaroRootElement } from '../dom/root'\nimport type { TaroText } from '../dom/text'\nimport type { TaroNodeImpl } from '../dom-external/node-impl'\nimport type { TaroElementImpl } from '../dom-external/element-impl'\nimport type { Hooks } from '../hooks'\n\n@injectable()\nexport class TaroDocument extends TaroElement {\n  private _getText: InstanceFactory<TaroText>\n\n  public constructor (// eslint-disable-next-line @typescript-eslint/indent\n    @inject(SERVICE_IDENTIFIER.TaroNodeImpl) nodeImpl: TaroNodeImpl,\n    @inject(SERVICE_IDENTIFIER.TaroElementFactory) getElement: InstanceNamedFactory,\n    @inject(SERVICE_IDENTIFIER.Hooks) hooks: Hooks,\n    @inject(SERVICE_IDENTIFIER.TaroElementImpl) elementImpl: TaroElementImpl,\n    @inject(SERVICE_IDENTIFIER.TaroTextFactory) getText: InstanceFactory<TaroText>\n  ) {\n    super(nodeImpl, getElement, hooks, elementImpl)\n    this._getText = getText\n    this.nodeType = NodeType.DOCUMENT_NODE\n    this.nodeName = DOCUMENT_ELEMENT_NAME\n  }\n\n  public createElement (type: string): TaroElement | TaroRootElement | FormElement {\n    if (type === ROOT_STR) {\n      return this._getElement<TaroRootElement>(ElementNames.RootElement)()\n    }\n\n    if (controlledComponent.has(type)) {\n      return this._getElement<FormElement>(ElementNames.FormElement)(type)\n    }\n\n    return this._getElement<TaroElement>(ElementNames.Element)(type)\n  }\n\n  // an ugly fake createElementNS to deal with @vue/runtime-dom's\n  // support mounting app to svg container since vue@3.0.8\n  public createElementNS (_svgNS: string, type: string): TaroElement | TaroRootElement | FormElement {\n    return this.createElement(type)\n  }\n\n  public createTextNode (text: string): TaroText {\n    return this._getText(text)\n  }\n\n  public getElementById<T extends TaroElement> (id: string | undefined | null): T | null {\n    const el = eventSource.get(id)\n    return isUndefined(el) ? null : el as T\n  }\n\n  public querySelector<T extends TaroElement> (query: string): T | null {\n    // 为了 Vue3 的乞丐版实现\n    if (/^#/.test(query)) {\n      return this.getElementById<T>(query.slice(1))\n    }\n    return null\n  }\n\n  public querySelectorAll () {\n    // fake hack\n    return []\n  }\n\n  // @TODO: @PERF: 在 hydrate 移除掉空的 node\n  public createComment (): TaroText {\n    const textnode = this._getText('')\n    textnode.nodeName = COMMENT\n    return textnode\n  }\n}\n", "import { inject, injectable, multiInject, /* multiInject, */ optional } from 'inversify'\nimport SERVICE_IDENTIFIER from './constants/identifiers'\n\nimport type {\n  IHooks,\n  OnRemoveAttribute,\n  GetLifecycle,\n  GetPathIndex,\n  GetEventCenter,\n  ModifyMpEvent,\n  ModifyTaroEvent,\n  IsBubbleEvents,\n  GetSpecialNodes,\n  BatchedEventUpdates,\n  MergePageInstance,\n  CreatePullDownComponent,\n  GetDOMNode,\n  InitNativeApi,\n  ModifySetAttrPayload,\n  ModifyHydrateData,\n  ModifyRmAttrPayload,\n  MpEvent,\n  OnAddEvent,\n  patchElement\n} from './interface'\nimport type { TaroElement } from './dom/element'\nimport type { TaroEvent } from './dom/event'\n\n@injectable()\nexport class Hooks implements IHooks {\n  @inject(SERVICE_IDENTIFIER.getLifecycle)\n  public getLifecycle: GetLifecycle\n\n  @inject(SERVICE_IDENTIFIER.getPathIndex)\n  public getPathIndex: GetPathIndex\n\n  @inject(SERVICE_IDENTIFIER.getEventCenter)\n  public getEventCenter: GetEventCenter\n\n  @inject(SERVICE_IDENTIFIER.isBubbleEvents)\n  public isBubbleEvents: IsBubbleEvents\n\n  @inject(SERVICE_IDENTIFIER.getSpecialNodes)\n  public getSpecialNodes: GetSpecialNodes\n\n  @inject(SERVICE_IDENTIFIER.onRemoveAttribute) @optional()\n  public onRemoveAttribute?: OnRemoveAttribute\n\n  @inject(SERVICE_IDENTIFIER.batchedEventUpdates) @optional()\n  public batchedEventUpdates?: BatchedEventUpdates\n\n  @inject(SERVICE_IDENTIFIER.mergePageInstance) @optional()\n  public mergePageInstance?: MergePageInstance\n\n  @inject(SERVICE_IDENTIFIER.createPullDownComponent) @optional()\n  public createPullDownComponent?: CreatePullDownComponent\n\n  @inject(SERVICE_IDENTIFIER.getDOMNode) @optional()\n  public getDOMNode?: GetDOMNode\n\n  @inject(SERVICE_IDENTIFIER.modifyHydrateData) @optional()\n  public modifyHydrateData?: ModifyHydrateData\n\n  @inject(SERVICE_IDENTIFIER.modifySetAttrPayload) @optional()\n  public modifySetAttrPayload?: ModifySetAttrPayload\n\n  @inject(SERVICE_IDENTIFIER.modifyRmAttrPayload) @optional()\n  public modifyRmAttrPayload?: ModifyRmAttrPayload\n\n  @inject(SERVICE_IDENTIFIER.onAddEvent) @optional()\n  public onAddEvent?: OnAddEvent\n\n  @multiInject(SERVICE_IDENTIFIER.modifyMpEvent) @optional()\n  private modifyMpEventImpls?: ModifyMpEvent[]\n\n  public modifyMpEvent (e: MpEvent) {\n    this.modifyMpEventImpls?.forEach(fn => fn(e))\n  }\n\n  @multiInject(SERVICE_IDENTIFIER.modifyTaroEvent) @optional()\n  private modifyTaroEventImpls?: ModifyTaroEvent[]\n\n  public modifyTaroEvent (e: TaroEvent, element: TaroElement) {\n    this.modifyTaroEventImpls?.forEach(fn => fn(e, element))\n  }\n\n  @multiInject(SERVICE_IDENTIFIER.initNativeApi) @optional()\n  public initNativeApiImpls?: InitNativeApi[]\n\n  public initNativeApi (taro: Record<string, any>) {\n    this.initNativeApiImpls?.forEach(fn => fn(taro))\n  }\n\n  @multiInject(SERVICE_IDENTIFIER.patchElement) @optional()\n  public patchElementImpls?: patchElement[]\n\n  public patchElement (element: TaroElement) {\n    this.patchElementImpls?.forEach(fn => fn(element))\n  }\n}\n", "/**\n * 支持冒泡的事件, 除 支付宝小程序外，其余的可冒泡事件都和微信保持一致\n * 详见 见 https://developers.weixin.qq.com/miniprogram/dev/framework/view/wxml/event.html\n */\nexport const BUBBLE_EVENTS = new Set([\n  'touchstart',\n  'touchmove',\n  'touchcancel',\n  'touchend',\n  'touchforcechange',\n  'tap',\n  'longpress',\n  'longtap',\n  'transitionend',\n  'animationstart',\n  'animationiteration',\n  'animationend'\n])\n", "import { ContainerModule } from 'inversify'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { BUBBLE_EVENTS } from '../constants/events'\n\nimport type { IsBubbleEvents, GetEventCenter, GetLifecycle, GetPathIndex, GetSpecialNodes } from '../interface'\n\nconst getLifecycle: GetLifecycle = function (instance, lifecycle) {\n  return instance[lifecycle]\n}\n\nconst getPathIndex: GetPathIndex = function (indexOfNode) {\n  return `[${indexOfNode}]`\n}\n\nconst getEventCenter: GetEventCenter = function (Events) {\n  return new Events()\n}\n\nconst isBubbleEvents = function (eventName) {\n  return BUBBLE_EVENTS.has(eventName)\n}\n\nconst getSpecialNodes = function () {\n  return ['view', 'text', 'image']\n}\n\nexport const DefaultHooksContainer = new ContainerModule(bind => {\n  bind<GetLifecycle>(SERVICE_IDENTIFIER.getLifecycle).toFunction(getLifecycle)\n  bind<GetPathIndex>(SERVICE_IDENTIFIER.getPathIndex).toFunction(getPathIndex)\n  bind<GetEventCenter>(SERVICE_IDENTIFIER.getEventCenter).toFunction(getEventCenter)\n  bind<IsBubbleEvents>(SERVICE_IDENTIFIER.isBubbleEvents).toFunction(isBubbleEvents)\n  bind<GetSpecialNodes>(SERVICE_IDENTIFIER.getSpecialNodes).toFunction(getSpecialNodes)\n})\n", "import { defaultReconciler, isArray } from '@tarojs/shared'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\n\nimport type { Container } from 'inversify'\n\nexport default function processPluginHooks (container: Container) {\n  const keys = Object.keys(defaultReconciler)\n  keys.forEach(key => {\n    if (key in SERVICE_IDENTIFIER) {\n      // is hooks\n      const identifier = SERVICE_IDENTIFIER[key]\n      const fn = defaultReconciler[key]\n\n      if (isArray(fn)) {\n        // is multi\n        fn.forEach(item => container.bind(identifier).toFunction(item))\n      } else {\n        if (container.isBound(identifier)) {\n          // 之前有绑定过，需要重新绑定以覆盖前者\n          container.rebind(identifier).toFunction(fn)\n        } else {\n          container.bind(identifier).toFunction(fn)\n        }\n      }\n    }\n  })\n}\n", "import { Container, interfaces } from 'inversify'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { TaroNodeImpl } from '../dom-external/node-impl'\nimport { TaroElementImpl } from '../dom-external/element-impl'\nimport { TaroElement } from '../dom/element'\nimport { TaroText } from '../dom/text'\nimport { TaroDocument } from '../dom/document'\nimport { TaroRootElement } from '../dom/root'\nimport { FormElement } from '../dom/form'\nimport { ElementNames, InstanceFactory, InstanceNamedFactory } from '../interface'\nimport { Hooks } from '../hooks'\nimport { DefaultHooksContainer } from './default-hooks'\nimport processPluginHooks from './plugin-hooks'\n\nconst container = new Container()\n\nif (process.env.TARO_ENV !== 'h5') {\n  container.bind<TaroElement>(SERVICE_IDENTIFIER.TaroElement).to(TaroElement).whenTargetNamed(ElementNames.Element)\n  container.bind<TaroDocument>(SERVICE_IDENTIFIER.TaroElement).to(TaroDocument).inSingletonScope().whenTargetNamed(ElementNames.Document)\n  container.bind<TaroRootElement>(SERVICE_IDENTIFIER.TaroElement).to(TaroRootElement).whenTargetNamed(ElementNames.RootElement)\n  container.bind<FormElement>(SERVICE_IDENTIFIER.TaroElement).to(FormElement).whenTargetNamed(ElementNames.FormElement)\n  container.bind<InstanceNamedFactory>(SERVICE_IDENTIFIER.TaroElementFactory).toFactory<TaroElement>((context: interfaces.Context) => {\n    return (named: ElementNames) => (nodeName?: string) => {\n      const el = context.container.getNamed<TaroElement>(SERVICE_IDENTIFIER.TaroElement, named)\n      if (nodeName) {\n        el.nodeName = nodeName\n      }\n      el.tagName = el.nodeName.toUpperCase()\n      return el\n    }\n  })\n\n  container.bind<TaroText>(SERVICE_IDENTIFIER.TaroText).to(TaroText)\n  container.bind<InstanceFactory<TaroText>>(SERVICE_IDENTIFIER.TaroTextFactory).toFactory<TaroText>((context: interfaces.Context) => {\n    return (text: string) => {\n      const textNode = context.container.get<TaroText>(SERVICE_IDENTIFIER.TaroText)\n      textNode._value = text\n      return textNode\n    }\n  })\n\n  container.bind<TaroNodeImpl>(SERVICE_IDENTIFIER.TaroNodeImpl).to(TaroNodeImpl).inSingletonScope()\n  container.bind<TaroElementImpl>(SERVICE_IDENTIFIER.TaroElementImpl).to(TaroElementImpl).inSingletonScope()\n}\n\ncontainer.bind<Hooks>(SERVICE_IDENTIFIER.Hooks).to(Hooks).inSingletonScope()\ncontainer.load(DefaultHooksContainer)\nprocessPluginHooks(container)\n\nexport default container\n", "import { EMPTY_OBJ } from '@tarojs/shared'\nimport container from '../container'\nimport { ElementNames } from '../interface'\nimport { isParentBinded } from '../utils'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport {\n  CONFIRM,\n  CURRENT_TARGET,\n  INPUT,\n  KEY_CODE,\n  TARGET,\n  TIME_STAMP,\n  TYPE,\n  TOUCHMOVE\n} from '../constants'\n\nimport type { TaroElement } from './element'\nimport type { InstanceNamedFactory, EventOptions, MpEvent, TaroDocumentInstance, IHooks } from '../interface'\n\nlet hooks\nlet getElement\nlet document\nif (process.env.TARO_ENV !== 'h5') {\n  hooks = container.get<IHooks>(SERVICE_IDENTIFIER.Hooks)\n  getElement = container.get<InstanceNamedFactory>(SERVICE_IDENTIFIER.TaroElementFactory)\n  document = getElement(ElementNames.Document)() as TaroDocumentInstance\n}\n\n// Taro 事件对象。以 Web 标准的事件对象为基础，加入小程序事件对象中携带的部分信息，并模拟实现事件冒泡。\nexport class TaroEvent {\n  public type: string\n\n  public bubbles: boolean\n\n  public cancelable: boolean\n\n  public _stop = false\n\n  public _end = false\n\n  public defaultPrevented = false\n\n  // timestamp can either be hi-res ( relative to page load) or low-res (relative to UNIX epoch)\n  // here use hi-res timestamp\n  public timeStamp = Date.now()\n\n  public mpEvent: MpEvent | undefined\n\n  public constructor (type: string, opts: EventOptions, event?: MpEvent) {\n    this.type = type.toLowerCase()\n    this.mpEvent = event\n    this.bubbles = Boolean(opts && opts.bubbles)\n    this.cancelable = Boolean(opts && opts.cancelable)\n  }\n\n  public stopPropagation () {\n    this._stop = true\n  }\n\n  public stopImmediatePropagation () {\n    this._end = this._stop = true\n  }\n\n  public preventDefault () {\n    this.defaultPrevented = true\n  }\n\n  get target () {\n    const element = document.getElementById(this.mpEvent?.target.id)\n    return {\n      ...this.mpEvent?.target,\n      ...this.mpEvent?.detail,\n      dataset: element !== null ? element.dataset : EMPTY_OBJ\n    }\n  }\n\n  get currentTarget () {\n    const element = document.getElementById(this.mpEvent?.currentTarget.id)\n\n    if (element === null) {\n      return this.target\n    }\n\n    return {\n      ...this.mpEvent?.currentTarget,\n      ...this.mpEvent?.detail,\n      dataset: element.dataset\n    }\n  }\n}\n\nexport function createEvent (event: MpEvent | string, node?: TaroElement) {\n  if (typeof event === 'string') {\n    // For Vue3 using document.createEvent\n    return new TaroEvent(event, { bubbles: true, cancelable: true })\n  }\n\n  const domEv = new TaroEvent(event.type, { bubbles: true, cancelable: true }, event)\n\n  for (const key in event) {\n    if (key === CURRENT_TARGET || key === TARGET || key === TYPE || key === TIME_STAMP) {\n      continue\n    } else {\n      domEv[key] = event[key]\n    }\n  }\n\n  if (domEv.type === CONFIRM && node?.nodeName === INPUT) {\n    // eslint-disable-next-line dot-notation\n    domEv[KEY_CODE] = 13\n  }\n\n  return domEv\n}\n\nconst eventsBatch = {}\n\n// 小程序的事件代理回调函数\nexport function eventHandler (event: MpEvent) {\n  hooks.modifyMpEvent?.(event)\n\n  if (event.currentTarget == null) {\n    event.currentTarget = event.target\n  }\n\n  const node = document.getElementById(event.currentTarget.id)\n  if (node) {\n    const dispatch = () => {\n      const e = createEvent(event, node)\n      hooks.modifyTaroEvent?.(e, node)\n      node.dispatchEvent(e)\n    }\n    if (typeof hooks.batchedEventUpdates === 'function') {\n      const type = event.type\n\n      if (\n        !hooks.isBubbleEvents(type) ||\n        !isParentBinded(node, type) ||\n        (type === TOUCHMOVE && !!node.props.catchMove)\n      ) {\n        // 最上层组件统一 batchUpdate\n        hooks.batchedEventUpdates(() => {\n          if (eventsBatch[type]) {\n            eventsBatch[type].forEach(fn => fn())\n            delete eventsBatch[type]\n          }\n          dispatch()\n        })\n      } else {\n        // 如果上层组件也有绑定同类型的组件，委托给上层组件调用事件回调\n        (eventsBatch[type] ||= []).push(dispatch)\n      }\n    } else {\n      dispatch()\n    }\n  }\n}\n", "import { EMPTY_OBJ } from '@tarojs/shared'\n\nexport const isBrowser = typeof document !== 'undefined' && !!document.scripts\nexport const doc: Document = isBrowser ? document : EMPTY_OBJ\nexport const win: Window = isBrowser ? window : EMPTY_OBJ\n", "import { createEvent } from '../dom/event'\nimport { isBrowser, doc } from '../env'\nimport ioc_container from '../container'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { ElementNames, InstanceNamedFactory } from '../interface'\nimport {\n  HTML,\n  HEAD,\n  BODY,\n  APP,\n  CONTAINER\n} from '../constants'\n\nimport type { TaroDocumentInstance } from '../interface'\n\nexport function createDocument () {\n  /**\n   * <document>\n   *   <html>\n   *     <head></head>\n   *     <body>\n   *       <container>\n   *         <app id=\"app\" />\n   *       </container>\n   *     </body>\n   *   </html>\n   * </document>\n   */\n  const getElement = ioc_container.get<InstanceNamedFactory>(SERVICE_IDENTIFIER.TaroElementFactory)\n  const doc = getElement(ElementNames.Document)() as TaroDocumentInstance\n  const documentCreateElement = doc.createElement.bind(doc)\n  const html = documentCreateElement(HTML)\n  const head = documentCreateElement(HEAD)\n  const body = documentCreateElement(BODY)\n  const app = documentCreateElement(APP)\n  app.id = APP\n  const container = documentCreateElement(CONTAINER) // 多包一层主要为了兼容 vue\n\n  doc.appendChild(html)\n  html.appendChild(head)\n  html.appendChild(body)\n  body.appendChild(container)\n  container.appendChild(app)\n\n  doc.documentElement = html\n  doc.head = head\n  doc.body = body\n  doc.createEvent = createEvent\n\n  return doc\n}\n\nexport const document = (isBrowser ? doc : createDocument()) as TaroDocumentInstance\n", "import { isBrowser, win } from '../env'\n\nconst machine = 'Macintosh'\nconst arch = 'Intel Mac OS X 10_14_5'\nconst engine = 'AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36'\n\nexport const navigator = isBrowser ? win.navigator : {\n  appCodeName: 'Mozilla',\n  appName: 'Netscape',\n  appVersion: '5.0 (' + machine + '; ' + arch + ') ' + engine,\n  cookieEnabled: true,\n  mimeTypes: [],\n  onLine: true,\n  platform: 'MacIntel',\n  plugins: [],\n  product: 'Taro',\n  productSub: '20030107',\n  userAgent: 'Mozilla/5.0 (' + machine + '; ' + arch + ') ' + engine,\n  vendor: 'Joyent',\n  vendorSub: ''\n}\n", "// https://github.com/myrne/performance-now\nexport let now\n\n(function () {\n  let loadTime\n  if ((typeof performance !== 'undefined' && performance !== null) && performance.now) {\n    now = function () {\n      return performance.now()\n    }\n  } else if (Date.now) {\n    now = function () {\n      return Date.now() - loadTime\n    }\n    loadTime = Date.now()\n  } else {\n    now = function () {\n      return new Date().getTime() - loadTime\n    }\n    loadTime = new Date().getTime()\n  }\n})()\n\nlet lastTime = 0\n\n// https://gist.github.com/paulirish/1579671\n// https://gist.github.com/jalbam/5fe05443270fa6d8136238ec72accbc0\nconst raf = typeof requestAnimationFrame !== 'undefined' && requestAnimationFrame !== null ? requestAnimationFrame : function (callback) {\n  const _now = now()\n  const nextTime = Math.max(lastTime + 16, _now) // First time will execute it immediately but barely noticeable and performance is gained.\n  return setTimeout(function () { callback(lastTime = nextTime) }, nextTime - _now)\n}\n\nconst caf = typeof cancelAnimationFrame !== 'undefined' && cancelAnimationFrame !== null\n  ? cancelAnimationFrame\n  : function (seed) {\n    // fix https://github.com/NervJS/taro/issues/7749\n    clearTimeout(seed)\n  }\n\nexport {\n  raf,\n  caf\n}\n", "import type { TaroElement } from '../dom/element'\nimport type { Style } from '../dom/style'\n\nexport function getComputedStyle (element: TaroElement): Style {\n  return element.style\n}\n", "import { navigator } from './navigator'\nimport { document } from './document'\nimport { isBrowser, win } from '../env'\nimport { raf, caf } from './raf'\nimport { getComputedStyle } from './getComputedStyle'\nimport { DATE } from '../constants'\n\nexport const window = isBrowser ? win : {\n  navigator,\n  document\n}\n\nif (!isBrowser) {\n  const globalProperties = [\n    ...Object.getOwnPropertyNames(global || win),\n    ...Object.getOwnPropertySymbols(global || win)\n  ]\n\n  globalProperties.forEach(property => {\n    if (property === 'atob') return\n    if (!Object.prototype.hasOwnProperty.call(window, property)) {\n      window[property] = global[property]\n    }\n  })\n\n  ;(document as any).defaultView = window\n}\n\nif (process.env.TARO_ENV && process.env.TARO_ENV !== 'h5') {\n  (window as any).requestAnimationFrame = raf;\n  (window as any).cancelAnimationFrame = caf;\n  (window as any).getComputedStyle = getComputedStyle;\n  (window as any).addEventListener = function () {};\n  (window as any).removeEventListener = function () {}\n  if (!(DATE in window)) {\n    (window as any).Date = Date\n  }\n  (window as any).setTimeout = function (cb, delay) {\n    setTimeout(cb, delay)\n  }\n  ;(window as any).clearTimeout = function (seed) {\n    clearTimeout(seed)\n  }\n}\n", "import { AppInstance, PageInstance } from './dsl/instance'\n\nexport interface Router {\n  params: Record<string, unknown>,\n  path: string,\n  onReady: string,\n  onHide: string,\n  onShow: string\n}\n\ninterface Current {\n  app: AppInstance | null,\n  router: Router | null,\n  page: PageInstance | null,\n  preloadData?: any\n}\n\nexport const Current: Current = {\n  app: null,\n  router: null,\n  page: null\n}\n\nexport const getCurrentInstance = () => Current\n", "import container from '../container'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\n\nimport type { IHooks } from '../interface'\n\n/* eslint-disable no-dupe-class-members */\ntype Callback1<T1> = (arg1: T1) => any;\ntype Callback2<T1, T2> = (arg1: T1, arg2: T2) => any;\ntype Callback3<T1, T2, T3> = (arg1: T1, arg2: T2, arg3: T3) => any;\ntype Callback4<T1, T2, T3, T4> = (arg1: T1, arg2: T2, arg3: T3, arg4: T4) => any;\ntype Callback5<T1, T2, T3, T4, T5> = (arg1: T1, arg2: T2, arg3: T3,\n  arg4: T4, arg5: T5) => any;\ntype Callback6Rest<T1, T2, T3, T4, T5, T6> = (arg1: T1, arg2: T2, arg3: T3,\n  arg4: T4, arg5: T5, arg6: T6,\n  ...rest: any[]) => any;\n\nexport class Events {\n  private callbacks?: Record<string, unknown>\n  static eventSplitter = /\\s+/\n\n  constructor (opts?) {\n    if (typeof opts !== 'undefined' && opts.callbacks) {\n      this.callbacks = opts.callbacks\n    } else {\n      this.callbacks = {}\n    }\n  }\n\n  on<T>(event: string, callback: Callback1<T>, context): this\n  on<T1, T2>(event: string, callback: Callback2<T1, T2>, context): this\n  on<T1, T2, T3>(event: string, callback: Callback3<T1, T2, T3>, context): this\n  on<T1, T2, T3, T4>(event: string, callback: Callback4<T1, T2, T3, T4>, comtext): this\n  on<T1, T2, T3, T4, T5>(event: string, callback: Callback5<T1, T2, T3, T4, T5>, context): this\n  on<T1, T2, T3, T4, T5, T6>(event: string, callback: Callback6Rest<T1, T2, T3, T4, T5, T6>, context): this\n  on (eventName, callback, context): this {\n    let event, node, tail, list\n    if (!callback) {\n      return this\n    }\n    eventName = eventName.split(Events.eventSplitter)\n    this.callbacks ||= {}\n    const calls = this.callbacks\n    while ((event = eventName.shift())) {\n      list = calls[event]\n      node = list ? list.tail : {}\n      node.next = tail = {}\n      node.context = context\n      node.callback = callback\n      calls[event] = {\n        tail,\n        next: list ? list.next : node\n      }\n    }\n    return this\n  }\n\n  once (events, callback, context) {\n    const wrapper = (...args) => {\n      callback.apply(this, args)\n      this.off(events, wrapper, context)\n    }\n\n    this.on(events, wrapper, context)\n\n    return this\n  }\n\n  off (events, callback, context) {\n    let event, calls, node, tail, cb, ctx\n    if (!(calls = this.callbacks)) {\n      return this\n    }\n    if (!(events || callback || context)) {\n      delete this.callbacks\n      return this\n    }\n    events = events ? events.split(Events.eventSplitter) : Object.keys(calls)\n    while ((event = events.shift())) {\n      node = calls[event]\n      delete calls[event]\n      if (!node || !(callback || context)) {\n        continue\n      }\n      tail = node.tail\n      while ((node = node.next) !== tail) {\n        cb = node.callback\n        ctx = node.context\n        if ((callback && cb !== callback) || (context && ctx !== context)) {\n          this.on(event, cb, ctx)\n        }\n      }\n    }\n    return this\n  }\n\n  trigger(event: string)\n  trigger<T1>(event: string, arg: T1)\n  trigger<T1, T2>(event: string, arg1: T1, arg2: T2)\n  trigger<T1, T2, T3>(event: string, arg1: T1, arg2: T2, arg3: T3)\n  trigger<T1, T2, T3, T4>(event: string, arg1: T1, arg2: T2, arg3: T3, arg4: T4)\n  trigger<T1, T2, T3, T4, T5>(event: string, arg1: T1, arg2: T2, arg3: T3, arg4: T4, arg5: T5)\n  trigger<T1, T2, T3, T4, T5, T6>(event: string, arg1: T1, arg2: T2, arg3: T3, arg4: T4, arg5: T5,\n    arg6: T6, ...rest: any[])\n\n  trigger (events) {\n    let event, node, calls, tail\n    if (!(calls = this.callbacks)) {\n      return this\n    }\n    events = events.split(Events.eventSplitter)\n    const rest = [].slice.call(arguments, 1)\n    while ((event = events.shift())) {\n      if ((node = calls[event])) {\n        tail = node.tail\n        while ((node = node.next) !== tail) {\n          node.callback.apply(node.context || this, rest)\n        }\n      }\n    }\n    return this\n  }\n}\n\nexport type EventsType = typeof Events\n\nconst hooks = container.get<IHooks>(SERVICE_IDENTIFIER.Hooks)\nconst eventCenter = hooks.getEventCenter(Events)\ncontainer.bind<Events>(SERVICE_IDENTIFIER.eventCenter).toConstantValue(eventCenter)\n\nexport { eventCenter }\n", "/* eslint-disable dot-notation */\nimport { isFunction, EMPTY_OBJ, ensure, Shortcuts, isUndefined, isArray } from '@tarojs/shared'\nimport container from '../container'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { eventHandler } from '../dom/event'\nimport { Current } from '../current'\nimport { document } from '../bom/document'\nimport { incrementId } from '../utils'\nimport { perf } from '../perf'\nimport { PAGE_INIT } from '../constants'\nimport { isBrowser } from '../env'\nimport { eventCenter } from '../emitter/emitter'\nimport { raf } from '../bom/raf'\n\nimport type { PageConfig } from '@tarojs/taro'\nimport type { Instance, PageInstance, PageProps } from './instance'\nimport type { Func, IHooks, MpInstance } from '../interface'\nimport type { TaroRootElement } from '../dom/root'\n\nconst instances = new Map<string, Instance>()\nconst pageId = incrementId()\nconst hooks = container.get<IHooks>(SERVICE_IDENTIFIER.Hooks)\n\nexport function injectPageInstance (inst: Instance<PageProps>, id: string) {\n  hooks.mergePageInstance?.(instances.get(id), inst)\n  instances.set(id, inst)\n}\n\nexport function getPageInstance (id: string): Instance | undefined {\n  return instances.get(id)\n}\n\nexport function addLeadingSlash (path?: string): string {\n  if (path == null) {\n    return ''\n  }\n  return path.charAt(0) === '/' ? path : '/' + path\n}\n\nexport function safeExecute (path: string, lifecycle: keyof PageInstance, ...args: unknown[]) {\n  const instance = instances.get(path)\n\n  if (instance == null) {\n    return\n  }\n\n  const func = hooks.getLifecycle(instance, lifecycle)\n\n  if (isArray(func)) {\n    const res = func.map(fn => fn.apply(instance, args))\n    return res[0]\n  }\n\n  if (!isFunction(func)) {\n    return\n  }\n\n  return func.apply(instance, args)\n}\n\nexport function stringify (obj?: Record<string, unknown>) {\n  if (obj == null) {\n    return ''\n  }\n  const path = Object.keys(obj).map((key) => {\n    return key + '=' + obj[key]\n  }).join('&')\n  return path === '' ? path : '?' + path\n}\n\nexport function getPath (id: string, options?: Record<string, unknown>): string {\n  let path = id\n  if (!isBrowser) {\n    path = id + stringify(options)\n  }\n  return path\n}\n\nexport function getOnReadyEventKey (path: string) {\n  return path + '.' + 'onReady'\n}\n\nexport function getOnShowEventKey (path: string) {\n  return path + '.' + 'onShow'\n}\n\nexport function getOnHideEventKey (path: string) {\n  return path + '.' + 'onHide'\n}\n\nexport function createPageConfig (component: any, pageName?: string, data?: Record<string, unknown>, pageConfig?: PageConfig) {\n  const id = pageName ?? `taro_page_${pageId()}`\n  // 小程序 Page 构造器是一个傲娇小公主，不能把复杂的对象挂载到参数上\n  let pageElement: TaroRootElement | null = null\n\n  let unmounting = false\n  let prepareMountList: (() => void)[] = []\n  let loadResolver: (...args: unknown[]) => void\n  let hasLoaded: Promise<void>\n  const config: PageInstance = {\n    onLoad (this: MpInstance, options = {}, cb?: Func) {\n      hasLoaded = new Promise(resolve => { loadResolver = resolve })\n\n      perf.start(PAGE_INIT)\n\n      Current.page = this as any\n      this.config = pageConfig || {}\n      options.$taroTimestamp = Date.now()\n\n      // this.$taroPath 是页面唯一标识，不可变，因此页面参数 options 也不可变\n      this.$taroPath = getPath(id, options)\n      // this.$taroParams 作为暴露给开发者的页面参数对象，可以被随意修改\n      if (this.$taroParams == null) {\n        this.$taroParams = Object.assign({}, options)\n      }\n\n      const router = isBrowser ? this.$taroPath : this.route || this.__route__\n      Current.router = {\n        params: this.$taroParams,\n        path: addLeadingSlash(router),\n        onReady: getOnReadyEventKey(id),\n        onShow: getOnShowEventKey(id),\n        onHide: getOnHideEventKey(id)\n      }\n\n      const mount = () => {\n        Current.app!.mount!(component, this.$taroPath, () => {\n          pageElement = document.getElementById<TaroRootElement>(this.$taroPath)\n\n          ensure(pageElement !== null, '没有找到页面实例。')\n          safeExecute(this.$taroPath, 'onLoad', this.$taroParams)\n          loadResolver()\n          if (!isBrowser) {\n            pageElement.ctx = this\n            pageElement.performUpdate(true, cb)\n          } else {\n            isFunction(cb) && cb()\n          }\n        })\n      }\n      if (unmounting) {\n        prepareMountList.push(mount)\n      } else {\n        mount()\n      }\n    },\n    onReady () {\n      raf(() => {\n        eventCenter.trigger(getOnReadyEventKey(id))\n      })\n\n      safeExecute(this.$taroPath, 'onReady')\n      this.onReady.called = true\n    },\n    onUnload () {\n      unmounting = true\n      Current.app!.unmount!(this.$taroPath, () => {\n        unmounting = false\n        instances.delete(this.$taroPath)\n        if (pageElement) {\n          pageElement.ctx = null\n        }\n        if (prepareMountList.length) {\n          prepareMountList.forEach(fn => fn())\n          prepareMountList = []\n        }\n      })\n    },\n    onShow () {\n      hasLoaded.then(() => {\n        Current.page = this as any\n        this.config = pageConfig || {}\n        const router = isBrowser ? this.$taroPath : this.route || this.__route__\n        Current.router = {\n          params: this.$taroParams,\n          path: addLeadingSlash(router),\n          onReady: getOnReadyEventKey(id),\n          onShow: getOnShowEventKey(id),\n          onHide: getOnHideEventKey(id)\n        }\n\n        raf(() => {\n          eventCenter.trigger(getOnShowEventKey(id))\n        })\n\n        safeExecute(this.$taroPath, 'onShow')\n      })\n    },\n    onHide () {\n      Current.page = null\n      Current.router = null\n      safeExecute(this.$taroPath, 'onHide')\n      eventCenter.trigger(getOnHideEventKey(id))\n    },\n    onPullDownRefresh () {\n      return safeExecute(this.$taroPath, 'onPullDownRefresh')\n    },\n    onReachBottom () {\n      return safeExecute(this.$taroPath, 'onReachBottom')\n    },\n    onPageScroll (options) {\n      return safeExecute(this.$taroPath, 'onPageScroll', options)\n    },\n    onResize (options) {\n      return safeExecute(this.$taroPath, 'onResize', options)\n    },\n    onTabItemTap (options) {\n      return safeExecute(this.$taroPath, 'onTabItemTap', options)\n    },\n    onTitleClick () {\n      return safeExecute(this.$taroPath, 'onTitleClick')\n    },\n    onOptionMenuClick () {\n      return safeExecute(this.$taroPath, 'onOptionMenuClick')\n    },\n    onPopMenuClick () {\n      return safeExecute(this.$taroPath, 'onPopMenuClick')\n    },\n    onPullIntercept () {\n      return safeExecute(this.$taroPath, 'onPullIntercept')\n    },\n    onAddToFavorites () {\n      return safeExecute(this.$taroPath, 'onAddToFavorites')\n    }\n  }\n\n  // onShareAppMessage 和 onShareTimeline 一样，会影响小程序右上方按钮的选项，因此不能默认注册。\n  if (component.onShareAppMessage ||\n      component.prototype?.onShareAppMessage ||\n      component.enableShareAppMessage) {\n    config.onShareAppMessage = function (options) {\n      const target = options?.target\n      if (target != null) {\n        const id = target.id\n        const element = document.getElementById(id)\n        if (element != null) {\n          options.target!.dataset = element.dataset\n        }\n      }\n      return safeExecute(this.$taroPath, 'onShareAppMessage', options)\n    }\n  }\n  if (component.onShareTimeline ||\n      component.prototype?.onShareTimeline ||\n      component.enableShareTimeline) {\n    config.onShareTimeline = function () {\n      return safeExecute(this.$taroPath, 'onShareTimeline')\n    }\n  }\n\n  config.eh = eventHandler\n\n  if (!isUndefined(data)) {\n    config.data = data\n  }\n\n  if (isBrowser) {\n    config.path = id\n  }\n\n  return config\n}\n\nexport function createComponentConfig (component: React.ComponentClass, componentName?: string, data?: Record<string, unknown>) {\n  const id = componentName ?? `taro_component_${pageId()}`\n  let componentElement: TaroRootElement | null = null\n\n  const config: any = {\n    attached () {\n      perf.start(PAGE_INIT)\n      const path = getPath(id, { id: this.getPageId?.() || pageId() })\n      Current.app!.mount!(component, path, () => {\n        componentElement = document.getElementById<TaroRootElement>(path)\n        ensure(componentElement !== null, '没有找到组件实例。')\n        safeExecute(path, 'onLoad')\n        if (!isBrowser) {\n          componentElement.ctx = this\n          componentElement.performUpdate(true)\n        }\n      })\n    },\n    detached () {\n      const path = getPath(id, { id: this.getPageId() })\n      Current.app!.unmount!(path, () => {\n        instances.delete(path)\n        if (componentElement) {\n          componentElement.ctx = null\n        }\n      })\n    },\n    methods: {\n      eh: eventHandler\n    }\n  }\n  if (!isUndefined(data)) {\n    config.data = data\n  }\n\n  config['options'] = component?.['options'] ?? EMPTY_OBJ\n  config['externalClasses'] = component?.['externalClasses'] ?? EMPTY_OBJ\n  config['behaviors'] = component?.['behaviors'] ?? EMPTY_OBJ\n  return config\n}\n\nexport function createRecursiveComponentConfig (componentName?: string) {\n  return {\n    properties: {\n      i: {\n        type: Object,\n        value: {\n          [Shortcuts.NodeName]: 'view'\n        }\n      },\n      l: {\n        type: String,\n        value: ''\n      }\n    },\n    options: {\n      addGlobalClass: true,\n      virtualHost: componentName !== 'custom-wrapper'\n    },\n    methods: {\n      eh: eventHandler\n    }\n  }\n}\n", "import type * as React from 'react'\nimport { isFunction, ensure, EMPTY_OBJ } from '@tarojs/shared'\nimport container from '../container'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { Current } from '../current'\nimport { document } from '../bom/document'\nimport { getPageInstance, injectPageInstance, safeExecute, addLeadingSlash } from './common'\nimport { isBrowser } from '../env'\nimport { incrementId } from '../utils'\nimport { HOOKS_APP_ID } from '../constants'\nimport { eventHandler } from '../dom/event'\n\nimport type { AppConfig, PageInstance } from '@tarojs/taro'\nimport type { AppInstance, ReactPageComponent, PageProps, Instance, ReactAppInstance } from './instance'\nimport type { IHooks } from '../interface'\nimport type { TaroRootElement } from '../dom/root'\n\ndeclare const getCurrentPages: () => PageInstance[]\nconst hooks = container.get<IHooks>(SERVICE_IDENTIFIER.Hooks)\n\nfunction isClassComponent (R: typeof React, component): boolean {\n  return isFunction(component.render) ||\n  !!component.prototype?.isReactComponent ||\n  component.prototype instanceof R.Component // compat for some others react-like library\n}\n\n// 初始值设置为 any 主要是为了过 TS 的校验\nexport let R: typeof React = EMPTY_OBJ\nexport let PageContext: React.Context<string> = EMPTY_OBJ\n\nexport function connectReactPage (\n  R: typeof React,\n  id: string\n) {\n  const h = R.createElement\n  return (component: ReactPageComponent): React.ComponentClass<PageProps> => {\n    // eslint-disable-next-line dot-notation\n    const isReactComponent = isClassComponent(R, component)\n\n    const inject = (node?: Instance) => node && injectPageInstance(node, id)\n    const refs = isReactComponent ? { ref: inject } : {\n      forwardedRef: inject,\n      // 兼容 react-redux 7.20.1+\n      reactReduxForwardedRef: inject\n    }\n\n    if (PageContext === EMPTY_OBJ) {\n      PageContext = R.createContext('')\n    }\n\n    return class Page extends R.Component<PageProps, { hasError: boolean }> {\n      state = {\n        hasError: false\n      }\n\n      static getDerivedStateFromError (error: Error) {\n        process.env.NODE_ENV !== 'production' && console.warn(error)\n        return { hasError: true }\n      }\n\n      // React 16 uncaught error 会导致整个应用 crash，\n      // 目前把错误缩小到页面\n      componentDidCatch (error: Error, info: React.ErrorInfo) {\n        process.env.NODE_ENV !== 'production' && console.warn(error)\n        process.env.NODE_ENV !== 'production' && console.error(info.componentStack)\n      }\n\n      render () {\n        const children = this.state.hasError\n          ? []\n          : h(PageContext.Provider, { value: id }, h(component, {\n            ...this.props,\n            ...refs\n          }))\n\n        if (isBrowser) {\n          return h(\n            'div',\n            { id, className: 'taro_page' },\n            children\n          )\n        }\n\n        return h(\n          'root',\n          { id },\n          children\n        )\n      }\n    }\n  }\n}\n\nlet ReactDOM\n\ntype PageComponent = React.CElement<PageProps, React.Component<PageProps, any, any>>\n\nfunction setReconciler () {\n  const getLifecycle = function (instance, lifecycle) {\n    lifecycle = lifecycle.replace(/^on(Show|Hide)$/, 'componentDid$1')\n    return instance[lifecycle]\n  }\n\n  const modifyMpEvent = function (event) {\n    event.type = event.type.replace(/-/g, '')\n  }\n\n  const batchedEventUpdates = function (cb) {\n    ReactDOM.unstable_batchedUpdates(cb)\n  }\n\n  const mergePageInstance = function (prev, next) {\n    if (!prev || !next) return\n\n    // 子组件使用 lifecycle hooks 注册了生命周期后，会存在 prev，里面是注册的生命周期回调。\n\n    // prev 使用 Object.create(null) 创建，H5 的 fast-refresh 可能也会导致存在 prev，要排除这些意外产生的 prev\n    if ('constructor' in prev) return\n\n    Object.keys(prev).forEach(item => {\n      if (isFunction(next[item])) {\n        next[item] = [next[item], ...prev[item]]\n      } else {\n        next[item] = [...(next[item] || []), ...prev[item]]\n      }\n    })\n  }\n\n  hooks.getLifecycle = getLifecycle\n  hooks.modifyMpEvent = modifyMpEvent\n  hooks.batchedEventUpdates = batchedEventUpdates\n  hooks.mergePageInstance = mergePageInstance\n\n  if (process.env.TARO_ENV === 'h5') {\n    hooks.createPullDownComponent = (\n      el: React.FunctionComponent<PageProps> | React.ComponentClass<PageProps>,\n      _,\n      R: typeof React,\n      customWrapper\n    ) => {\n      const isReactComponent = isClassComponent(R, el)\n\n      return R.forwardRef((props, ref) => {\n        const newProps: React.Props<any> = { ...props }\n        const refs = isReactComponent ? { ref: ref } : {\n          forwardedRef: ref,\n          // 兼容 react-redux 7.20.1+\n          reactReduxForwardedRef: ref\n        }\n\n        return R.createElement(\n          customWrapper || 'taro-pull-to-refresh',\n          null,\n          R.createElement(el, {\n            ...newProps,\n            ...refs\n          })\n        )\n      })\n    }\n\n    hooks.getDOMNode = (inst) => {\n      return ReactDOM.findDOMNode(inst)\n    }\n  }\n}\n\nconst pageKeyId = incrementId()\n\nexport function createReactApp (App: React.ComponentClass, react: typeof React, dom, config: AppConfig) {\n  R = react\n  ReactDOM = dom\n  ensure(!!ReactDOM, '构建 React/Nerv 项目请把 process.env.FRAMEWORK 设置为 \\'react\\'/\\'nerv\\' ')\n\n  const ref = R.createRef<ReactAppInstance>()\n  const isReactComponent = isClassComponent(R, App)\n\n  setReconciler()\n  class AppWrapper extends R.Component {\n    // run createElement() inside the render function to make sure that owner is right\n    private pages: Array<() => PageComponent> = []\n    private elements: Array<PageComponent> = []\n\n    public mount (component: React.ComponentClass<PageProps>, id: string, cb: () => void) {\n      const key = id + pageKeyId()\n      const page = () => R.createElement(component, { key, tid: id })\n      this.pages.push(page)\n      this.forceUpdate(cb)\n    }\n\n    public unmount (id: string, cb: () => void) {\n      for (let i = 0; i < this.elements.length; i++) {\n        const element = this.elements[i]\n        if (element.props.tid === id) {\n          this.elements.splice(i, 1)\n          break\n        }\n      }\n\n      this.forceUpdate(cb)\n    }\n\n    public render () {\n      while (this.pages.length > 0) {\n        const page = this.pages.pop()!\n        this.elements.push(page())\n      }\n\n      let props: React.Props<any> | null = null\n\n      if (isReactComponent) {\n        props = { ref }\n      }\n\n      return R.createElement(\n        App,\n        props,\n        isBrowser ? R.createElement('div', null, this.elements.slice()) : this.elements.slice()\n      )\n    }\n  }\n\n  let wrapper: AppWrapper\n  if (!isBrowser) {\n    wrapper = ReactDOM.render?.(R.createElement(AppWrapper), document.getElementById('app'))\n  }\n\n  const app: AppInstance = Object.create({\n    render (cb: () => void) {\n      wrapper.forceUpdate(cb)\n    },\n\n    mount (component: ReactPageComponent, id: string, cb: () => void) {\n      const page = connectReactPage(R, id)(component)\n      wrapper.mount(page, id, cb)\n    },\n\n    unmount (id: string, cb: () => void) {\n      wrapper.unmount(id, cb)\n    }\n  }, {\n    config: {\n      writable: true,\n      enumerable: true,\n      configurable: true,\n      value: config\n    },\n\n    onLaunch: {\n      enumerable: true,\n      writable: true,\n      value (options) {\n        Current.router = {\n          params: options?.query,\n          ...options\n        }\n        if (isBrowser) {\n          // 由于 H5 路由初始化的时候会清除 app 下的 dom 元素，所以需要在路由初始化后执行 render\n          wrapper = ReactDOM.render?.(R.createElement(AppWrapper), document.getElementById(config?.appId || 'app'))\n        }\n        const app = ref.current\n\n        // For taroize\n        // 把 App Class 上挂载的额外属性同步到全局 app 对象中\n        if (app?.taroGlobalData) {\n          const globalData = app.taroGlobalData\n          const keys = Object.keys(globalData)\n          const descriptors = Object.getOwnPropertyDescriptors(globalData)\n          keys.forEach(key => {\n            Object.defineProperty(this, key, {\n              configurable: true,\n              enumerable: true,\n              get () {\n                return globalData[key]\n              },\n              set (value) {\n                globalData[key] = value\n              }\n            })\n          })\n          Object.defineProperties(this, descriptors)\n        }\n        this.$app = app\n\n        if (app != null && isFunction(app.onLaunch)) {\n          app.onLaunch(options)\n        }\n      }\n    },\n\n    onShow: {\n      enumerable: true,\n      writable: true,\n      value (options) {\n        const app = ref.current\n        Current.router = {\n          params: options?.query,\n          ...options\n        }\n        if (app != null && isFunction(app.componentDidShow)) {\n          app.componentDidShow(options)\n        }\n\n        // app useDidShow\n        triggerAppHook('onShow')\n      }\n    },\n\n    onHide: {\n      enumerable: true,\n      writable: true,\n      value (options: unknown) {\n        const app = ref.current\n        if (app != null && isFunction(app.componentDidHide)) {\n          app.componentDidHide(options)\n        }\n\n        // app useDidHide\n        triggerAppHook('onHide')\n      }\n    },\n\n    onPageNotFound: {\n      enumerable: true,\n      writable: true,\n      value (res: unknown) {\n        const app = ref.current\n        if (app != null && isFunction(app.onPageNotFound)) {\n          app.onPageNotFound(res)\n        }\n      }\n    }\n  })\n\n  function triggerAppHook (lifecycle) {\n    const instance = getPageInstance(HOOKS_APP_ID)\n    if (instance) {\n      const app = ref.current\n      const func = hooks.getLifecycle(instance, lifecycle)\n      if (Array.isArray(func)) {\n        func.forEach(cb => cb.apply(app))\n      }\n    }\n  }\n\n  Current.app = app\n  return Current.app\n}\n\nconst getNativeCompId = incrementId()\n\nfunction initNativeComponentEntry (R: typeof React, ReactDOM) {\n  interface IEntryState {\n    components: {\n      compId: string\n      element: React.ReactElement\n    }[]\n  }\n\n  interface IWrapperProps {\n    getCtx: () => any\n    renderComponent: (ctx: any) => React.ReactElement\n  }\n\n  class NativeComponentWrapper extends R.Component<IWrapperProps, Record<any, any>> {\n    root = R.createRef<TaroRootElement>()\n    ctx = this.props.getCtx()\n\n    componentDidMount () {\n      this.ctx.component = this\n      const rootElement = this.root.current!\n      rootElement.ctx = this.ctx\n      rootElement.performUpdate(true)\n    }\n\n    render () {\n      return (\n        R.createElement(\n          'root',\n          {\n            ref: this.root\n          },\n          this.props.renderComponent(this.ctx)\n        )\n      )\n    }\n  }\n\n  class Entry extends R.Component<Record<any, any>, IEntryState> {\n    state: IEntryState = {\n      components: []\n    }\n\n    componentDidMount () {\n      Current.app = this\n    }\n\n    mount (Component, compId, getCtx) {\n      const isReactComponent = isClassComponent(R, Component)\n      const inject = (node?: Instance) => node && injectPageInstance(node, compId)\n      const refs = isReactComponent ? { ref: inject } : {\n        forwardedRef: inject,\n        reactReduxForwardedRef: inject\n      }\n      const item = {\n        compId,\n        element: R.createElement(NativeComponentWrapper, {\n          key: compId,\n          getCtx,\n          renderComponent (ctx) {\n            return R.createElement(Component, { ...(ctx.data ||= {}).props, ...refs })\n          }\n        })\n      }\n      this.setState({\n        components: [...this.state.components, item]\n      })\n    }\n\n    unmount (compId) {\n      const components = this.state.components\n      const index = components.findIndex(item => item.compId === compId)\n      const next = [...components.slice(0, index), ...components.slice(index + 1)]\n      this.setState({\n        components: next\n      })\n    }\n\n    render () {\n      const components = this.state.components\n      return (\n        components.map(({ element }) => element)\n      )\n    }\n  }\n\n  setReconciler()\n\n  const app = document.getElementById('app')\n\n  ReactDOM.render(\n    R.createElement(Entry, {}),\n    app\n  )\n}\n\nexport function createNativeComponentConfig (Component, react: typeof React, reactdom, componentConfig) {\n  R = react\n  ReactDOM = reactdom\n\n  setReconciler()\n\n  const config = {\n    properties: {\n      props: {\n        type: null,\n        value: null,\n        observer (_newVal, oldVal) {\n          oldVal && this.component.forceUpdate()\n        }\n      }\n    },\n    created () {\n      if (!Current.app) {\n        initNativeComponentEntry(R, ReactDOM)\n      }\n    },\n    attached () {\n      setCurrent()\n      this.compId = getNativeCompId()\n      this.config = componentConfig\n      Current.app!.mount!(Component, this.compId, () => this)\n    },\n    ready () {\n      safeExecute(this.compId, 'onReady')\n    },\n    detached () {\n      Current.app!.unmount!(this.compId)\n    },\n    pageLifetimes: {\n      show () {\n        safeExecute(this.compId, 'onShow')\n      },\n      hide () {\n        safeExecute(this.compId, 'onHide')\n      }\n    },\n    methods: {\n      eh: eventHandler\n    }\n  }\n\n  function setCurrent () {\n    const pages = getCurrentPages()\n    const currentPage = pages[pages.length - 1]\n    if (Current.page === currentPage) return\n\n    Current.page = currentPage\n\n    const route = (currentPage as any).route || (currentPage as any).__route__\n    const router = {\n      params: currentPage.options || {},\n      path: addLeadingSlash(route),\n      onReady: '',\n      onHide: '',\n      onShow: ''\n    }\n    Current.router = router\n\n    if (!currentPage.options) {\n      // 例如在微信小程序中，页面 options 的设置时机比组件 attached 慢\n      Object.defineProperty(currentPage, 'options', {\n        enumerable: true,\n        configurable: true,\n        get () {\n          return this._optionsValue\n        },\n        set (value) {\n          router.params = value\n          this._optionsValue = value\n        }\n      })\n    }\n  }\n\n  return config\n}\n", "import { isFunction, noop, ensure, isBoolean } from '@tarojs/shared'\nimport container from '../container'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { injectPageInstance } from './common'\nimport { Current } from '../current'\nimport { document } from '../bom/document'\nimport { isBrowser } from '../env'\n\n/* eslint-disable import/no-duplicates */\nimport type VueCtor from 'vue'\nimport type { ComponentOptions, VueConstructor, VNode } from 'vue'\nimport type { AppConfig } from '@tarojs/taro'\nimport type { AppInstance, VueAppInstance, VueInstance } from './instance'\nimport type { IHooks } from '../interface'\n\nexport type V = typeof VueCtor\n\nexport function connectVuePage (Vue: VueConstructor, id: string) {\n  return (component: ComponentOptions<VueCtor>) => {\n    const injectedPage = Vue.extend({\n      props: {\n        tid: String\n      },\n      mixins: [component, {\n        created () {\n          injectPageInstance(this, id)\n        }\n      }]\n    })\n\n    const options: ComponentOptions<VueCtor> = {\n      render (h) {\n        return h(\n          isBrowser ? 'div' : 'root',\n          {\n            attrs: {\n              id,\n              class: isBrowser ? 'taro_page' : ''\n            }\n          },\n          [\n            h(injectedPage, { props: { tid: id } })\n          ]\n        )\n      }\n    }\n\n    return options\n  }\n}\n\nfunction setReconciler () {\n  const hooks = container.get<IHooks>(SERVICE_IDENTIFIER.Hooks)\n\n  const onRemoveAttribute = function (dom, qualifiedName) {\n    // 处理原因: https://github.com/NervJS/taro/pull/5990\n    const props = dom.props\n    if (!props.hasOwnProperty(qualifiedName) || isBoolean(props[qualifiedName])) {\n      dom.setAttribute(qualifiedName, false)\n      return true\n    }\n  }\n\n  const getLifecycle = function (instance, lifecycle) {\n    return instance.$options[lifecycle]\n  }\n\n  hooks.onRemoveAttribute = onRemoveAttribute\n  hooks.getLifecycle = getLifecycle\n\n  if (process.env.TARO_ENV === 'h5') {\n    hooks.createPullDownComponent = (el, path, vue: VueConstructor) => {\n      const injectedPage = vue.extend({\n        props: {\n          tid: String\n        },\n        mixins: [el as ComponentOptions<Vue>, {\n          created () {\n            injectPageInstance(this, path)\n          }\n        }]\n      })\n\n      const options: ComponentOptions<Vue> = {\n        name: 'PullToRefresh',\n        render (h) {\n          return h(\n            'taro-pull-to-refresh',\n            {\n              class: ['hydrated']\n            },\n            [h(injectedPage, this.$slots.default)]\n          )\n        }\n      }\n\n      return options\n    }\n\n    hooks.getDOMNode = (el) => {\n      return el.$el as any\n    }\n  }\n}\n\nlet Vue\n\nexport function createVueApp (App: ComponentOptions<VueCtor>, vue: V, config: AppConfig) {\n  Vue = vue\n  ensure(!!Vue, '构建 Vue 项目请把 process.env.FRAMEWORK 设置为 \\'vue\\'')\n\n  setReconciler()\n\n  Vue.config.getTagNamespace = noop\n\n  const elements: VNode[] = []\n  const pages: Array<(h: Vue.CreateElement) => VNode> = []\n  let appInstance: VueAppInstance\n\n  const wrapper = new (Vue as VueConstructor)({\n    render (h) {\n      while (pages.length > 0) {\n        const page = pages.pop()!\n        elements.push(page(h))\n      }\n      return h(App, { ref: 'app' }, elements.slice())\n    },\n    methods: {\n      mount (component: ComponentOptions<VueCtor>, id: string, cb: () => void) {\n        pages.push((h) => h(component, { key: id }))\n        this.updateSync(cb)\n      },\n      updateSync (this: VueInstance, cb: () => void) {\n        this._update(this._render(), false)\n        this.$children.forEach((child: VueInstance) => child._update(child._render(), false))\n        cb()\n      },\n      unmount (id: string, cb: () => void) {\n        for (let i = 0; i < elements.length; i++) {\n          const element = elements[i]\n          if (element.key === id) {\n            elements.splice(i, 1)\n            break\n          }\n        }\n\n        this.updateSync(cb)\n      }\n    }\n  })\n  if (!isBrowser) {\n    wrapper.$mount(document.getElementById('app') as any)\n  }\n  const app: AppInstance = Object.create({\n    mount (component: ComponentOptions<VueCtor>, id: string, cb: () => void) {\n      const page = connectVuePage(Vue, id)(component)\n      wrapper.mount(page, id, cb)\n    },\n\n    unmount (id: string, cb: () => void) {\n      wrapper.unmount(id, cb)\n    }\n  }, {\n    config: {\n      writable: true,\n      enumerable: true,\n      configurable: true,\n      value: config\n    },\n\n    onLaunch: {\n      writable: true,\n      enumerable: true,\n      value (options) {\n        Current.router = {\n          params: options?.query,\n          ...options\n        }\n        if (isBrowser) {\n          // 由于 H5 路由初始化的时候会清除 app 下的 dom 元素，所以需要在路由初始化后再执行 render\n          wrapper.$mount(document.getElementById(config?.appId || 'app') as any)\n        }\n        appInstance = wrapper.$refs.app as VueAppInstance\n        if (appInstance != null && isFunction(appInstance.$options.onLaunch)) {\n          appInstance.$options.onLaunch.call(appInstance, options)\n        }\n      }\n    },\n\n    onShow: {\n      writable: true,\n      enumerable: true,\n      value (options) {\n        Current.router = {\n          params: options?.query,\n          ...options\n        }\n        if (appInstance != null && isFunction(appInstance.$options.onShow)) {\n          appInstance.$options.onShow.call(appInstance, options)\n        }\n      }\n    },\n\n    onHide: {\n      writable: true,\n      enumerable: true,\n      value (options) {\n        if (appInstance != null && isFunction(appInstance.$options.onHide)) {\n          appInstance.$options.onHide.call(appInstance, options)\n        }\n      }\n    }\n  })\n\n  Current.app = app\n\n  return Current.app\n}\n", "import { isFunction, isArray, ensure } from '@tarojs/shared'\nimport container from '../container'\nimport SERVICE_IDENTIFIER from '../constants/identifiers'\nimport { Current } from '../current'\nimport { injectPageInstance } from './common'\nimport { isBrowser } from '../env'\n\nimport type {\n  App,\n  Component,\n  ComponentPublicInstance,\n  VNode,\n  h as createElement\n} from '@vue/runtime-core'\nimport type { TaroElement } from '../dom/element'\nimport type { AppConfig as Config } from '@tarojs/taro'\nimport type { GetLifecycle, IHooks } from '../interface'\nimport type { AppInstance } from './instance'\n\nfunction createVue3Page (h: typeof createElement, id: string) {\n  return function (component): VNode {\n    const inject = {\n      props: {\n        tid: String\n      },\n      created () {\n        injectPageInstance(this, id)\n      }\n    }\n\n    if (isArray(component.mixins)) {\n      const mixins = component.mixins\n      const idx = mixins.length - 1\n      if (!mixins[idx].props?.tid) {\n        // mixins 里还没注入过，直接推入数组\n        component.mixins.push(inject)\n      } else {\n        // mixins 里已经注入过，代替前者\n        component.mixins[idx] = inject\n      }\n    } else {\n      component.mixins = [inject]\n    }\n\n    return h(\n      isBrowser ? 'div' : 'root',\n      {\n        key: id,\n        id,\n        class: isBrowser ? 'taro_page' : ''\n      },\n      [\n        h(Object.assign({}, component), {\n          tid: id\n        })\n      ]\n    )\n  }\n}\n\nfunction setReconciler () {\n  const hooks = container.get<IHooks>(SERVICE_IDENTIFIER.Hooks)\n\n  const getLifecycle: GetLifecycle = function (instance, lifecycle) {\n    return instance.$options[lifecycle]\n  }\n\n  const modifyMpEvent = function (event) {\n    event.type = event.type.replace(/-/g, '')\n  }\n\n  hooks.getLifecycle = getLifecycle\n  hooks.modifyMpEvent = modifyMpEvent\n\n  if (process.env.TARO_ENV === 'h5') {\n    hooks.createPullDownComponent = (component, path, h: typeof createElement) => {\n      const inject = {\n        props: {\n          tid: String\n        },\n        created () {\n          injectPageInstance(this, path)\n        }\n      }\n\n      component.mixins = isArray(component.mixins)\n        ? component.mixins.push(inject)\n        : [inject]\n\n      return {\n        render () {\n          return h(\n            'taro-pull-to-refresh',\n            {\n              class: 'hydrated'\n            },\n            [h(component, this.$slots.default)]\n          )\n        }\n      }\n    }\n\n    hooks.getDOMNode = (el) => {\n      return el.$el as any\n    }\n  }\n}\n\nexport function createVue3App (app: App<TaroElement>, h: typeof createElement, config: Config) {\n  let pages: VNode[] = []\n  let appInstance: ComponentPublicInstance\n\n  ensure(!isFunction(app._component), '入口组件不支持使用函数式组件')\n\n  setReconciler()\n\n  app._component.render = function () {\n    return pages.slice()\n  }\n  if (!isBrowser) {\n    appInstance = app.mount('#app')\n  }\n  const appConfig: AppInstance = Object.create({\n    mount (component: Component, id: string, cb: () => void) {\n      const page = createVue3Page(h, id)(component)\n      pages.push(page)\n      this.updateAppInstance(cb)\n    },\n\n    unmount (id: string, cb: () => void) {\n      pages = pages.filter(page => page.key !== id)\n      this.updateAppInstance(cb)\n    },\n\n    updateAppInstance (cb?: (() => void | undefined)) {\n      appInstance.$forceUpdate()\n      appInstance.$nextTick(cb)\n    }\n  }, {\n    config: {\n      writable: true,\n      enumerable: true,\n      configurable: true,\n      value: config\n    },\n\n    onLaunch: {\n      writable: true,\n      enumerable: true,\n      value (options) {\n        Current.router = {\n          params: options?.query,\n          ...options\n        }\n        if (isBrowser) {\n          appInstance = app.mount('#' + config.appId || 'app')\n        }\n\n        // 把 App Class 上挂载的额外属性同步到全局 app 对象中\n        // eslint-disable-next-line dot-notation\n        if (app['taroGlobalData']) {\n          // eslint-disable-next-line dot-notation\n          const globalData = app['taroGlobalData']\n          const keys = Object.keys(globalData)\n          const descriptors = Object.getOwnPropertyDescriptors(globalData)\n          keys.forEach(key => {\n            Object.defineProperty(this, key, {\n              configurable: true,\n              enumerable: true,\n              get () {\n                return globalData[key]\n              },\n              set (value) {\n                globalData[key] = value\n              }\n            })\n          })\n          Object.defineProperties(this, descriptors)\n        }\n\n        const onLaunch = appInstance?.$options?.onLaunch\n        isFunction(onLaunch) && onLaunch.call(appInstance, options)\n      }\n    },\n\n    onShow: {\n      writable: true,\n      enumerable: true,\n      value (options) {\n        Current.router = {\n          params: options?.query,\n          ...options\n        }\n        const onShow = appInstance?.$options?.onShow\n        isFunction(onShow) && onShow.call(appInstance, options)\n      }\n    },\n\n    onHide: {\n      writable: true,\n      enumerable: true,\n      value (options) {\n        const onHide = appInstance?.$options?.onHide\n        isFunction(onHide) && onHide.call(appInstance, options)\n      }\n    }\n  })\n\n  Current.app = appConfig\n\n  return Current.app\n}\n", "import { isFunction, isArray } from '@tarojs/shared'\nimport { PageContext, R as React } from './react'\nimport { getPageInstance, injectPageInstance } from './common'\nimport { PageLifeCycle } from './instance'\nimport { Current } from '../current'\nimport { HOOKS_APP_ID } from '../constants'\n\nimport type { Func } from '../interface'\n\nconst taroHooks = (lifecycle: keyof PageLifeCycle) => {\n  return (fn: Func) => {\n    const id = React.useContext(PageContext) || HOOKS_APP_ID\n\n    // hold fn ref and keep up to date\n    const fnRef = React.useRef(fn)\n    if (fnRef.current !== fn) fnRef.current = fn\n\n    React.useLayoutEffect(() => {\n      let inst = getPageInstance(id)\n      let first = false\n      if (inst == null) {\n        first = true\n        inst = Object.create(null)\n      }\n\n      inst = inst!\n\n      // callback is immutable but inner function is up to date\n      const callback = (...args: any) => fnRef.current(...args)\n\n      if (isFunction(inst[lifecycle])) {\n        (inst[lifecycle] as any) = [inst[lifecycle], callback]\n      } else {\n        (inst[lifecycle] as any) = [\n          ...((inst[lifecycle] as any) || []),\n          callback\n        ]\n      }\n\n      if (first) {\n        injectPageInstance(inst!, id)\n      }\n      return () => {\n        const inst = getPageInstance(id)\n        const list = inst![lifecycle]\n        if (list === callback) {\n          (inst![lifecycle] as any) = undefined\n        } else if (isArray(list)) {\n          (inst![lifecycle] as any) = list.filter(item => item !== callback)\n        }\n      }\n    }, [])\n  }\n}\n\nexport const useDidShow = taroHooks('componentDidShow')\n\nexport const useDidHide = taroHooks('componentDidHide')\n\nexport const usePullDownRefresh = taroHooks('onPullDownRefresh')\n\nexport const useReachBottom = taroHooks('onReachBottom')\n\nexport const usePageScroll = taroHooks('onPageScroll')\n\nexport const useResize = taroHooks('onResize')\n\nexport const useShareAppMessage = taroHooks('onShareAppMessage')\n\nexport const useTabItemTap = taroHooks('onTabItemTap')\n\nexport const useTitleClick = taroHooks('onTitleClick')\n\nexport const useOptionMenuClick = taroHooks('onOptionMenuClick')\n\nexport const usePullIntercept = taroHooks('onPullIntercept')\n\nexport const useShareTimeline = taroHooks('onShareTimeline')\n\nexport const useAddToFavorites = taroHooks('onAddToFavorites')\n\nexport const useReady = taroHooks('onReady')\n\nexport const useRouter = (dynamic = false) => {\n  return dynamic ? Current.router : React.useMemo(() => Current.router, [])\n}\n\nexport const useScope = () => undefined\n", "import { Current } from './current'\nimport { getPath } from './dsl/common'\nimport { TaroRootElement } from './dom/root'\nimport { document } from './bom/document'\nimport { isBrowser } from './env'\n\nimport type { Func } from './interface'\n\nfunction removeLeadingSlash (path?: string) {\n  if (path == null) {\n    return ''\n  }\n  return path.charAt(0) === '/' ? path.slice(1) : path\n}\n\nexport const nextTick = (cb: Func, ctx?: Record<string, any>) => {\n  const router = Current.router\n  const timerFunc = () => {\n    setTimeout(function () {\n      ctx ? cb.call(ctx) : cb()\n    }, 1)\n  }\n\n  if (router !== null) {\n    let pageElement: TaroRootElement | null = null\n    const path = getPath(removeLeadingSlash(router.path), router.params)\n    pageElement = document.getElementById<TaroRootElement>(path)\n    if (pageElement?.pendingUpdate) {\n      if (isBrowser) {\n        // eslint-disable-next-line dot-notation\n        pageElement.firstChild?.['componentOnReady']?.().then(() => {\n          timerFunc()\n        }) ?? timerFunc()\n      } else {\n        pageElement.enqueueUpdateCallback(cb, ctx)\n      }\n    } else {\n      timerFunc()\n    }\n  } else {\n    timerFunc()\n  }\n}\n", "import { internalComponents } from './components'\nimport { isArray } from './is'\n\nexport const EMPTY_OBJ: any = {}\n\nexport const EMPTY_ARR = []\n\nexport const noop = (..._: unknown[]) => {}\n\nexport const defaultReconciler = Object.create(null)\n\n/**\n * Boxed value.\n *\n * @typeparam T Value type.\n */\nexport interface Box<T> {\n  v: T;\n}\n\n/**\n * box creates a boxed value.\n *\n * @typeparam T Value type.\n * @param v Value.\n * @returns Boxed value.\n */\nexport const box = <T>(v: T) => ({ v })\n\n/**\n * box creates a boxed value.\n *\n * @typeparam T Value type.\n * @param b Boxed value.\n * @returns Value.\n */\nexport const unbox = <T>(b: Box<T>) => b.v\n\nexport function toDashed (s: string) {\n  return s.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()\n}\n\nexport function toCamelCase (s: string) {\n  let camel = ''\n  let nextCap = false\n  for (let i = 0; i < s.length; i++) {\n    if (s[i] !== '-') {\n      camel += nextCap ? s[i].toUpperCase() : s[i]\n      nextCap = false\n    } else {\n      nextCap = true\n    }\n  }\n  return camel\n}\n\nexport const toKebabCase = function (string) {\n  return string.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()\n}\n\nexport function capitalize (s: string) {\n  return s.charAt(0).toUpperCase() + s.slice(1)\n}\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty\n\nexport const hasOwn = (\n  val: Record<any, any>,\n  key: string | symbol\n) => hasOwnProperty.call(val, key)\n\nconst reportIssue = '如有疑问，请提交 issue 至：https://github.com/nervjs/taro/issues'\n\n/**\n * ensure takes a condition and throw a error if the condition fails,\n * like failure::ensure: https://docs.rs/failure/0.1.1/failure/macro.ensure.html\n * @param condition condition.\n * @param msg error message.\n */\nexport function ensure (condition: boolean, msg: string): asserts condition {\n  if (!condition) {\n    throw new Error(msg + '\\n' + reportIssue)\n  }\n}\n\nexport function warn (condition: boolean, msg: string) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (condition) {\n      console.warn(`[taro warn] ${msg}`)\n    }\n  }\n}\n\nexport function queryToJson (str) {\n  const dec = decodeURIComponent\n  const qp = str.split('&')\n  const ret = {}\n  let name\n  let val\n  for (let i = 0, l = qp.length, item; i < l; ++i) {\n    item = qp[i]\n    if (item.length) {\n      const s = item.indexOf('=')\n      if (s < 0) {\n        name = dec(item)\n        val = ''\n      } else {\n        name = dec(item.slice(0, s))\n        val = dec(item.slice(s + 1))\n      }\n      if (typeof ret[name] === 'string') { // inline'd type check\n        ret[name] = [ret[name]]\n      }\n\n      if (Array.isArray(ret[name])) {\n        ret[name].push(val)\n      } else {\n        ret[name] = val\n      }\n    }\n  }\n  return ret // Object\n}\n\nlet _uniqueId = 1\nconst _loadTime = (new Date()).getTime().toString()\n\nexport function getUniqueKey () {\n  return _loadTime + (_uniqueId++)\n}\n\nconst cacheData = {}\n\nexport function cacheDataSet (key, val) {\n  cacheData[key] = val\n}\n\nexport function cacheDataGet (key, delelteAfterGet?) {\n  const temp = cacheData[key]\n  delelteAfterGet && delete cacheData[key]\n  return temp\n}\n\nexport function cacheDataHas (key) {\n  return key in cacheData\n}\n\nexport function mergeInternalComponents (components) {\n  Object.keys(components).forEach(name => {\n    if (name in internalComponents) {\n      Object.assign(internalComponents[name], components[name])\n    } else {\n      internalComponents[name] = components[name]\n    }\n  })\n}\n\nexport function mergeReconciler (hostConfig) {\n  Object.keys(hostConfig).forEach(key => {\n    const value = hostConfig[key]\n    const raw = defaultReconciler[key]\n    if (!raw) {\n      defaultReconciler[key] = value\n    } else {\n      if (isArray(raw)) {\n        defaultReconciler[key] = raw.push(value)\n      } else {\n        defaultReconciler[key] = [raw, value]\n      }\n    }\n  })\n}\n\nexport function unsupport (api) {\n  return function () {\n    console.warn(`小程序暂不支持 ${api}`)\n  }\n}\n\nexport function setUniqueKeyToRoute (key: string, obj) {\n  const routerParamsPrivateKey = '__key_'\n  const useDataCacheApis = [\n    'navigateTo',\n    'redirectTo',\n    'reLaunch',\n    'switchTab'\n  ]\n\n  if (useDataCacheApis.indexOf(key) > -1) {\n    const url = obj.url = obj.url || ''\n    const hasMark = url.indexOf('?') > -1\n    const cacheKey = getUniqueKey()\n    obj.url += (hasMark ? '&' : '?') + `${routerParamsPrivateKey}=${cacheKey}`\n  }\n}\n\nexport function indent (str: string, size: number): string {\n  return str.split('\\n')\n    .map((line, index) => {\n      const indent = index === 0 ? '' : Array(size).fill(' ').join('')\n      return indent + line\n    })\n    .join('\\n')\n}\n", "import { unsupport, setUniqueKeyToRoute } from './utils'\n\ndeclare const getCurrentPages: () => any\ndeclare const getApp: () => any\ndeclare const requirePlugin: () => void\n\ntype IObject = Record<string, any>\n\ninterface IProcessApisIOptions {\n  noPromiseApis?: Set<string>\n  needPromiseApis?: Set<string>\n  handleSyncApis?: (key: string, global: IObject, args: any[]) => any\n  transformMeta?: (key: string, options: IObject) => { key: string, options: IObject },\n  modifyApis?: (apis: Set<string>) => void\n  modifyAsyncResult?: (key: string, res) => void\n  isOnlyPromisify?: boolean\n  [propName: string]: any\n}\n\nconst needPromiseApis = new Set<string>([\n  'addPhoneContact',\n  'authorize',\n  'canvasGetImageData',\n  'canvasPutImageData',\n  'canvasToTempFilePath',\n  'checkSession',\n  'chooseAddress',\n  'chooseImage',\n  'chooseInvoiceTitle',\n  'chooseLocation',\n  'chooseVideo',\n  'clearStorage',\n  'closeBLEConnection',\n  'closeBluetoothAdapter',\n  'closeSocket',\n  'compressImage',\n  'connectSocket',\n  'createBLEConnection',\n  'downloadFile',\n  'exitMiniProgram',\n  'getAvailableAudioSources',\n  'getBLEDeviceCharacteristics',\n  'getBLEDeviceServices',\n  'getBatteryInfo',\n  'getBeacons',\n  'getBluetoothAdapterState',\n  'getBluetoothDevices',\n  'getClipboardData',\n  'getConnectedBluetoothDevices',\n  'getConnectedWifi',\n  'getExtConfig',\n  'getFileInfo',\n  'getImageInfo',\n  'getLocation',\n  'getNetworkType',\n  'getSavedFileInfo',\n  'getSavedFileList',\n  'getScreenBrightness',\n  'getSetting',\n  'getStorage',\n  'getStorageInfo',\n  'getSystemInfo',\n  'getUserInfo',\n  'getWifiList',\n  'hideHomeButton',\n  'hideShareMenu',\n  'hideTabBar',\n  'hideTabBarRedDot',\n  'loadFontFace',\n  'login',\n  'makePhoneCall',\n  'navigateBack',\n  'navigateBackMiniProgram',\n  'navigateTo',\n  'navigateToBookshelf',\n  'navigateToMiniProgram',\n  'notifyBLECharacteristicValueChange',\n  'hideKeyboard',\n  'hideLoading',\n  'hideNavigationBarLoading',\n  'hideToast',\n  'openBluetoothAdapter',\n  'openDocument',\n  'openLocation',\n  'openSetting',\n  'pageScrollTo',\n  'previewImage',\n  'queryBookshelf',\n  'reLaunch',\n  'readBLECharacteristicValue',\n  'redirectTo',\n  'removeSavedFile',\n  'removeStorage',\n  'removeTabBarBadge',\n  'requestSubscribeMessage',\n  'saveFile',\n  'saveImageToPhotosAlbum',\n  'saveVideoToPhotosAlbum',\n  'scanCode',\n  'sendSocketMessage',\n  'setBackgroundColor',\n  'setBackgroundTextStyle',\n  'setClipboardData',\n  'setEnableDebug',\n  'setInnerAudioOption',\n  'setKeepScreenOn',\n  'setNavigationBarColor',\n  'setNavigationBarTitle',\n  'setScreenBrightness',\n  'setStorage',\n  'setTabBarBadge',\n  'setTabBarItem',\n  'setTabBarStyle',\n  'showActionSheet',\n  'showFavoriteGuide',\n  'showLoading',\n  'showModal',\n  'showShareMenu',\n  'showTabBar',\n  'showTabBarRedDot',\n  'showToast',\n  'startBeaconDiscovery',\n  'startBluetoothDevicesDiscovery',\n  'startDeviceMotionListening',\n  'startPullDownRefresh',\n  'stopBeaconDiscovery',\n  'stopBluetoothDevicesDiscovery',\n  'stopCompass',\n  'startCompass',\n  'startAccelerometer',\n  'stopAccelerometer',\n  'showNavigationBarLoading',\n  'stopDeviceMotionListening',\n  'stopPullDownRefresh',\n  'switchTab',\n  'uploadFile',\n  'vibrateLong',\n  'vibrateShort',\n  'writeBLECharacteristicValue'\n])\n\nfunction getCanIUseWebp (taro) {\n  return function () {\n    if (typeof taro.getSystemInfoSync !== 'function') {\n      console.error('不支持 API canIUseWebp')\n      return false\n    }\n    const { platform } = taro.getSystemInfoSync()\n    const platformLower = platform.toLowerCase()\n    if (platformLower === 'android' || platformLower === 'devtools') {\n      return true\n    }\n    return false\n  }\n}\n\nfunction getNormalRequest (global) {\n  return function request (options) {\n    options = options || {}\n    if (typeof options === 'string') {\n      options = {\n        url: options\n      }\n    }\n    const originSuccess = options.success\n    const originFail = options.fail\n    const originComplete = options.complete\n    let requestTask\n    const p: any = new Promise((resolve, reject) => {\n      options.success = res => {\n        originSuccess && originSuccess(res)\n        resolve(res)\n      }\n      options.fail = res => {\n        originFail && originFail(res)\n        reject(res)\n      }\n\n      options.complete = res => {\n        originComplete && originComplete(res)\n      }\n\n      requestTask = global.request(options)\n    })\n    p.abort = (cb) => {\n      cb && cb()\n      if (requestTask) {\n        requestTask.abort()\n      }\n      return p\n    }\n    return p\n  }\n}\n\nfunction processApis (taro, global, config: IProcessApisIOptions = {}) {\n  const patchNeedPromiseApis = config.needPromiseApis || []\n  const _needPromiseApis = new Set<string>([...patchNeedPromiseApis, ...needPromiseApis])\n  const preserved = [\n    'getEnv',\n    'interceptors',\n    'Current',\n    'getCurrentInstance',\n    'options',\n    'nextTick',\n    'eventCenter',\n    'Events',\n    'preload',\n    'webpackJsonp'\n  ]\n\n  const apis = new Set(\n    !config.isOnlyPromisify\n      ? Object.keys(global).filter(api => preserved.indexOf(api) === -1)\n      : patchNeedPromiseApis\n  )\n\n  if (config.modifyApis) {\n    config.modifyApis(apis)\n  }\n\n  apis.forEach(key => {\n    if (_needPromiseApis.has(key)) {\n      const originKey = key\n      taro[originKey] = (options: Record<string, any> | string = {}, ...args) => {\n        let key = originKey\n\n        // 第一个参数 options 为字符串，单独处理\n        if (typeof options === 'string') {\n          if (args.length) {\n            return global[key](options, ...args)\n          }\n          return global[key](options)\n        }\n\n        // 改变 key 或 option 字段，如需要把支付宝标准的字段对齐微信标准的字段\n        if (config.transformMeta) {\n          const transformResult = config.transformMeta(key, options)\n          key = transformResult.key\n          ;(options as Record<string, any>) = transformResult.options\n          // 新 key 可能不存在\n          if (!global.hasOwnProperty(key)) {\n            return unsupport(key)()\n          }\n        }\n\n        let task: any = null\n        const obj: Record<string, any> = Object.assign({}, options)\n\n        // 为页面跳转相关的 API 设置一个随机数作为路由参数。为了给 runtime 区分页面。\n        setUniqueKeyToRoute(key, options)\n\n        // Promise 化\n        const p: any = new Promise((resolve, reject) => {\n          obj.success = res => {\n            config.modifyAsyncResult?.(key, res)\n            options.success?.(res)\n            if (key === 'connectSocket') {\n              resolve(\n                Promise.resolve().then(() => task ? Object.assign(task, res) : res)\n              )\n            } else {\n              resolve(res)\n            }\n          }\n          obj.fail = res => {\n            options.fail?.(res)\n            reject(res)\n          }\n          obj.complete = res => {\n            options.complete?.(res)\n          }\n          if (args.length) {\n            task = global[key](obj, ...args)\n          } else {\n            task = global[key](obj)\n          }\n        })\n\n        // 给 promise 对象挂载属性\n        if (key === 'uploadFile' || key === 'downloadFile') {\n          p.progress = cb => {\n            task?.onProgressUpdate(cb)\n            return p\n          }\n          p.abort = cb => {\n            cb?.()\n            task?.abort()\n            return p\n          }\n        }\n        return p\n      }\n    } else {\n      let platformKey = key\n\n      // 改变 key 或 option 字段，如需要把支付宝标准的字段对齐微信标准的字段\n      if (config.transformMeta) {\n        platformKey = config.transformMeta(key, {}).key\n      }\n\n      // API 不存在\n      if (!global.hasOwnProperty(platformKey)) {\n        taro[key] = unsupport(key)\n        return\n      }\n      if (typeof global[key] === 'function') {\n        taro[key] = (...args) => {\n          if (config.handleSyncApis) {\n            return config.handleSyncApis(key, global, args)\n          } else {\n            return global[platformKey].apply(global, args)\n          }\n        }\n      } else {\n        taro[key] = global[platformKey]\n      }\n    }\n  })\n\n  !config.isOnlyPromisify && equipCommonApis(taro, global, config)\n}\n\n/**\n * 挂载常用 API\n * @param taro Taro 对象\n * @param global 小程序全局对象，如微信的 wx，支付宝的 my\n */\nfunction equipCommonApis (taro, global, apis: Record<string, any> = {}) {\n  taro.canIUseWebp = getCanIUseWebp(taro)\n  taro.getCurrentPages = getCurrentPages || unsupport('getCurrentPages')\n  taro.getApp = getApp || unsupport('getApp')\n  taro.env = global.env || {}\n\n  try {\n    taro.requirePlugin = requirePlugin || unsupport('requirePlugin')\n  } catch (error) {\n    taro.requirePlugin = unsupport('requirePlugin')\n  }\n\n  // request & interceptors\n  const request = apis.request ? apis.request : getNormalRequest(global)\n  function taroInterceptor (chain) {\n    return request(chain.requestParams)\n  }\n  const link = new taro.Link(taroInterceptor)\n  taro.request = link.request.bind(link)\n  taro.addInterceptor = link.addInterceptor.bind(link)\n  taro.cleanInterceptors = link.cleanInterceptors.bind(link)\n  taro.miniGlobal = taro.options.miniGlobal = global\n}\n\nexport {\n  processApis\n}\n", "const { container, SERVICE_IDENTIFIER } = require('@tarojs/runtime')\nconst taro = require('@tarojs/api').default\n\nconst hooks = container.get(SERVICE_IDENTIFIER.Hooks)\nif (typeof hooks.initNativeApi === 'function') {\n  hooks.initNativeApi(taro)\n}\n\nmodule.exports = taro\nmodule.exports.default = module.exports\n", "if (typeof Object.assign !== 'function') {\n  // Must be writable: true, enumerable: false, configurable: true\n  Object.assign = function (target) { // .length of function is 2\n    if (target == null) { // TypeError if undefined or null\n      throw new TypeError('Cannot convert undefined or null to object')\n    }\n\n    const to = Object(target)\n\n    for (let index = 1; index < arguments.length; index++) {\n      const nextSource = arguments[index]\n\n      if (nextSource != null) { // Skip over if undefined or null\n        for (const nextKey in nextSource) {\n          // Avoid bugs when hasOwnProperty is shadowed\n          if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n            to[nextKey] = nextSource[nextKey]\n          }\n        }\n      }\n    }\n    return to\n  }\n}\n\nif (typeof Object.defineProperties !== 'function') {\n  Object.defineProperties = function (obj, properties) {\n    function convertToDescriptor (desc) {\n      function hasProperty (obj, prop) {\n        return Object.prototype.hasOwnProperty.call(obj, prop)\n      }\n\n      function isCallable (v) {\n        // NB: modify as necessary if other values than functions are callable.\n        return typeof v === 'function'\n      }\n\n      if (typeof desc !== 'object' || desc === null) { throw new TypeError('bad desc') }\n\n      const d = {}\n\n      if (hasProperty(desc, 'enumerable')) d.enumerable = !!desc.enumerable\n      if (hasProperty(desc, 'configurable')) { d.configurable = !!desc.configurable }\n      if (hasProperty(desc, 'value')) d.value = desc.value\n      if (hasProperty(desc, 'writable')) d.writable = !!desc.writable\n      if (hasProperty(desc, 'get')) {\n        const g = desc.get\n\n        if (!isCallable(g) && typeof g !== 'undefined') { throw new TypeError('bad get') }\n        d.get = g\n      }\n      if (hasProperty(desc, 'set')) {\n        const s = desc.set\n        if (!isCallable(s) && typeof s !== 'undefined') { throw new TypeError('bad set') }\n        d.set = s\n      }\n\n      if (('get' in d || 'set' in d) && ('value' in d || 'writable' in d)) { throw new TypeError('identity-confused descriptor') }\n\n      return d\n    }\n\n    if (typeof obj !== 'object' || obj === null) throw new TypeError('bad obj')\n\n    properties = Object(properties)\n\n    const keys = Object.keys(properties)\n    const descs = []\n\n    for (let i = 0; i < keys.length; i++) {\n      descs.push([keys[i], convertToDescriptor(properties[keys[i]])])\n    }\n\n    for (let i = 0; i < descs.length; i++) {\n      Object.defineProperty(obj, descs[i][0], descs[i][1])\n    }\n\n    return obj\n  }\n}\n", "export const ENV_TYPE = {\n  WEAPP: 'WEAPP',\n  WEB: 'WEB',\n  RN: 'RN',\n  SWAN: 'SWAN',\n  ALIPAY: 'ALIPAY',\n  TT: 'TT',\n  QQ: 'QQ',\n  JD: 'JD',\n  KWAI: 'KWAI'\n}\n\nlet _env = null\n\n// 一个taro项目肯定运行同样的环境\nexport function getEnv () {\n  if (_env) return _env\n  if (typeof jd !== 'undefined' && jd.getSystemInfo) {\n    _env = ENV_TYPE.JD\n    return ENV_TYPE.JD\n  }\n  if (typeof qq !== 'undefined' && qq.getSystemInfo) {\n    _env = ENV_TYPE.QQ\n    return ENV_TYPE.QQ\n  }\n  if (typeof tt !== 'undefined' && tt.getSystemInfo) {\n    _env = ENV_TYPE.TT\n    return ENV_TYPE.TT\n  }\n  if (typeof wx !== 'undefined' && wx.getSystemInfo) {\n    _env = ENV_TYPE.WEAPP\n    return ENV_TYPE.WEAPP\n  }\n  if (typeof swan !== 'undefined' && swan.getSystemInfo) {\n    _env = ENV_TYPE.SWAN\n    return ENV_TYPE.SWAN\n  }\n  if (typeof my !== 'undefined' && my.getSystemInfo) {\n    _env = ENV_TYPE.ALIPAY\n    return ENV_TYPE.ALIPAY\n  }\n  if (typeof ks !== 'undefined' && ks.getSystemInfo) {\n    _env = ENV_TYPE.KWAI\n    return ENV_TYPE.KWAI\n  }\n  if (typeof global !== 'undefined' && global.__fbGenNativeModule) {\n    _env = ENV_TYPE.RN\n    return ENV_TYPE.RN\n  }\n  if (typeof window !== 'undefined') {\n    _env = ENV_TYPE.WEB\n    return ENV_TYPE.WEB\n  }\n  return 'Unknown environment'\n}\n", "export default class Chain {\n  constructor (requestParams, interceptors = [], index = 0) {\n    this.index = index\n    this.requestParams = requestParams\n    this.interceptors = interceptors\n  }\n\n  proceed (requestParams) {\n    this.requestParams = requestParams\n    if (this.index >= this.interceptors.length) {\n      throw new Error('chain 参数错误, 请勿直接修改 request.chain')\n    }\n    const nextInterceptor = this._getNextInterceptor()\n    const nextChain = this._getNextChain()\n    const p = nextInterceptor(nextChain)\n    const res = p.catch(err => Promise.reject(err))\n    if (typeof p.abort === 'function') res.abort = p.abort\n    return res\n  }\n\n  _getNextInterceptor () {\n    return this.interceptors[this.index]\n  }\n\n  _getNextChain () {\n    return new Chain(this.requestParams, this.interceptors, this.index + 1)\n  }\n}\n", "import Chain from './chain'\n\nexport default class Link {\n  constructor (interceptor) {\n    this.taroInterceptor = interceptor\n    this.chain = new Chain()\n  }\n\n  request (requestParams) {\n    this.chain.interceptors = this.chain.interceptors.filter(interceptor => interceptor !== this.taroInterceptor)\n    this.chain.interceptors.push(this.taroInterceptor)\n    return this.chain.proceed({ ...requestParams })\n  }\n\n  addInterceptor (interceptor) {\n    this.chain.interceptors.push(interceptor)\n  }\n\n  cleanInterceptors () {\n    this.chain = new Chain()\n  }\n}\n", "export function timeoutInterceptor (chain) {\n  const requestParams = chain.requestParams\n  let p\n  const res = new Promise((resolve, reject) => {\n    let timeout = setTimeout(() => {\n      timeout = null\n      reject(new Error('网络链接超时,请稍后再试！'))\n    }, (requestParams && requestParams.timeout) || 60000)\n\n    p = chain.proceed(requestParams)\n    p.then(res => {\n      if (!timeout) return\n      clearTimeout(timeout)\n      resolve(res)\n    }).catch(err => {\n      timeout && clearTimeout(timeout)\n      reject(err)\n    })\n  })\n\n  if (p !== undefined && typeof p.abort === 'function') res.abort = p.abort\n  return res\n}\n\nexport function logInterceptor (chain) {\n  const requestParams = chain.requestParams\n  const { method, data, url } = requestParams\n  // eslint-disable-next-line no-console\n  console.log(`http ${method || 'GET'} --> ${url} data: `, data)\n  const p = chain.proceed(requestParams)\n  const res = p.then(res => {\n    // eslint-disable-next-line no-console\n    console.log(`http <-- ${url} result:`, res)\n    return res\n  })\n  if (typeof p.abort === 'function') res.abort = p.abort\n  return res\n}\n", "export function Behavior (options) {\n  return options\n}\n\nexport function getPreload (current) {\n  return function (key, val) {\n    if (typeof key === 'object') {\n      current.preloadData = key\n    } else if (key !== undefined && val !== undefined) {\n      current.preloadData = {\n        [key]: val\n      }\n    }\n  }\n}\n\nexport function getInitPxTransform (taro) {\n  return function (config) {\n    const {\n      designWidth = 750,\n      deviceRatio = {\n        640: 2.34 / 2,\n        750: 1,\n        828: 1.81 / 2\n      }\n    } = config\n    taro.config = taro.config || {}\n    taro.config.designWidth = designWidth\n    taro.config.deviceRatio = deviceRatio\n  }\n}\n\nexport function getPxTransform (taro) {\n  return function (size) {\n    const {\n      designWidth = 750,\n      deviceRatio = {\n        640: 2.34 / 2,\n        750: 1,\n        828: 1.81 / 2\n      }\n    } = taro.config || {}\n    if (!(designWidth in deviceRatio)) {\n      throw new Error(`deviceRatio 配置中不存在 ${designWidth} 的设置！`)\n    }\n    return (parseInt(size, 10) * deviceRatio[designWidth]) + 'rpx'\n  }\n}\n", "/* eslint-disable camelcase */\nimport './polyfill'\nimport { getEnv, ENV_TYPE } from './env'\nimport Link from './interceptor'\nimport * as interceptors from './interceptor/interceptors'\nimport {\n  Behavior,\n  getPreload,\n  getPxTransform,\n  getInitPxTransform\n} from './tools'\nimport {\n  Current,\n  getCurrentInstance,\n  options,\n  nextTick,\n  eventCenter,\n  Events,\n  useDidShow,\n  useDidHide,\n  usePullDownRefresh,\n  useReachBottom,\n  usePageScroll,\n  useResize,\n  useShareAppMessage,\n  useTabItemTap,\n  useTitleClick,\n  useOptionMenuClick,\n  usePullIntercept,\n  useShareTimeline,\n  useAddToFavorites,\n  useReady,\n  useRouter\n} from '@tarojs/runtime'\n\nconst Taro = {\n  Behavior,\n  getEnv,\n  ENV_TYPE,\n  Link,\n  interceptors,\n  Current,\n  getCurrentInstance,\n  options,\n  nextTick,\n  eventCenter,\n  Events,\n  useDidShow,\n  useDidHide,\n  usePullDownRefresh,\n  useReachBottom,\n  usePageScroll,\n  useResize,\n  useShareAppMessage,\n  useTabItemTap,\n  useTitleClick,\n  useOptionMenuClick,\n  usePullIntercept,\n  useShareTimeline,\n  useAddToFavorites,\n  useReady,\n  useRouter,\n  getInitPxTransform\n}\n\nTaro.initPxTransform = getInitPxTransform(Taro)\nTaro.preload = getPreload(Current)\nTaro.pxTransform = getPxTransform(Taro)\n\nexport default Taro\n", "const Taro = require('@tarojs/api').default\n\nlet api\n\n// bundler 可以自动移除不需要的 require\nif (process.env.TARO_ENV === 'alipay') {\n  api = require('./lib/alipay')\n} else if (process.env.TARO_ENV === 'jd') {\n  api = require('./lib/jd')\n} else if (process.env.TARO_ENV === 'qq') {\n  api = require('./lib/qq')\n} else if (process.env.TARO_ENV === 'swan') {\n  api = require('./lib/swan')\n} else if (process.env.TARO_ENV === 'tt') {\n  api = require('./lib/tt')\n} else if (process.env.TARO_ENV === 'weapp') {\n  api = require('./lib/wx')\n}\n\n// 兼容不同工具的 import 机制，如 Jest, rollup\nconst initNativeAPI = api && api.default ? api.default : api\n// 如果没有对应的 env type，那就啥也不干，例如 h5\nif (typeof initNativeAPI === 'function') {\n  initNativeAPI(Taro)\n}\n\nmodule.exports = Taro\nmodule.exports.default = module.exports\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = initNativeApi;\n\nvar _api = _interopRequireDefault(require(\"@tarojs/api\"));\n\nvar _dataCache = require(\"./data-cache\");\n\nvar _utils = require(\"./utils\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nvar noPromiseApis = _api[\"default\"].noPromiseApis,\n    onAndSyncApis = _api[\"default\"].onAndSyncApis,\n    otherApis = _api[\"default\"].otherApis,\n    initPxTransform = _api[\"default\"].initPxTransform,\n    Link = _api[\"default\"].Link;\nvar apiDiff = {\n  showActionSheet: {\n    options: {\n      change: [{\n        old: 'itemList',\n        \"new\": 'items'\n      }]\n    }\n  },\n  showToast: {\n    options: {\n      change: [{\n        old: 'title',\n        \"new\": 'content'\n      }, {\n        old: 'icon',\n        \"new\": 'type'\n      }]\n    }\n  },\n  showLoading: {\n    options: {\n      change: [{\n        old: 'title',\n        \"new\": 'content'\n      }]\n    }\n  },\n  setNavigationBarTitle: {\n    alias: 'setNavigationBar'\n  },\n  setNavigationBarColor: {\n    alias: 'setNavigationBar'\n  },\n  saveImageToPhotosAlbum: {\n    alias: 'saveImage',\n    options: {\n      change: [{\n        old: 'filePath',\n        \"new\": 'url'\n      }]\n    }\n  },\n  previewImage: {\n    options: {\n      set: [{\n        key: 'current',\n        value: function value(options) {\n          return options.urls.indexOf(options.current || options.urls[0]);\n        }\n      }]\n    }\n  },\n  getFileInfo: {\n    options: {\n      change: [{\n        old: 'filePath',\n        \"new\": 'apFilePath'\n      }]\n    }\n  },\n  getSavedFileInfo: {\n    options: {\n      change: [{\n        old: 'filePath',\n        \"new\": 'apFilePath'\n      }]\n    }\n  },\n  removeSavedFile: {\n    options: {\n      change: [{\n        old: 'filePath',\n        \"new\": 'apFilePath'\n      }]\n    }\n  },\n  saveFile: {\n    options: {\n      change: [{\n        old: 'tempFilePath',\n        \"new\": 'apFilePath'\n      }]\n    }\n  },\n  openLocation: {\n    options: {\n      set: [{\n        key: 'latitude',\n        value: function value(options) {\n          return String(options.latitude);\n        }\n      }, {\n        key: 'longitude',\n        value: function value(options) {\n          return String(options.longitude);\n        }\n      }]\n    }\n  },\n  uploadFile: {\n    options: {\n      change: [{\n        old: 'name',\n        \"new\": 'fileName'\n      }]\n    }\n  },\n  getClipboardData: {\n    alias: 'getClipboard'\n  },\n  setClipboardData: {\n    alias: 'setClipboard',\n    options: {\n      change: [{\n        old: 'data',\n        \"new\": 'text'\n      }]\n    }\n  },\n  makePhoneCall: {\n    options: {\n      change: [{\n        old: 'phoneNumber',\n        \"new\": 'number'\n      }]\n    }\n  },\n  scanCode: {\n    alias: 'scan',\n    options: {\n      change: [{\n        old: 'onlyFromCamera',\n        \"new\": 'hideAlbum'\n      }],\n      set: [{\n        key: 'type',\n        value: function value(options) {\n          return options.scanType && options.scanType[0].slice(0, -4) || 'qr';\n        }\n      }]\n    }\n  },\n  setScreenBrightness: {\n    options: {\n      change: [{\n        old: 'value',\n        \"new\": 'brightness'\n      }]\n    }\n  }\n};\nvar nativeRequest = my.canIUse('request') ? my.request : my.httpRequest;\nvar RequestQueue = {\n  MAX_REQUEST: 5,\n  queue: [],\n  request: function request(options) {\n    this.push(options); // 返回request task\n\n    return this.run();\n  },\n  push: function push(options) {\n    this.queue.push(options);\n  },\n  run: function run() {\n    var _arguments = arguments,\n        _this = this;\n\n    if (!this.queue.length) {\n      return;\n    }\n\n    if (this.queue.length <= this.MAX_REQUEST) {\n      var options = this.queue.shift();\n      var completeFn = options.complete;\n\n      options.complete = function () {\n        completeFn && completeFn.apply(options, _toConsumableArray(_arguments));\n\n        _this.run();\n      };\n\n      return nativeRequest(options);\n    }\n  }\n};\n\nfunction taroInterceptor(chain) {\n  return request(chain.requestParams);\n}\n\nvar link = new Link(taroInterceptor);\n\nfunction request(options) {\n  options = options || {};\n\n  if (typeof options === 'string') {\n    options = {\n      url: options\n    };\n  }\n\n  var defaultHeaders = {\n    'content-type': 'application/json'\n  };\n  options.headers = defaultHeaders;\n\n  if (options.header) {\n    for (var k in options.header) {\n      var lowerK = k.toLocaleLowerCase();\n      options.headers[lowerK] = options.header[k];\n    }\n\n    delete options.header;\n  }\n\n  var originSuccess = options.success;\n  var originFail = options.fail;\n  var originComplete = options.complete;\n  var requestTask;\n  var p = new Promise(function (resolve, reject) {\n    options.success = function (res) {\n      res.statusCode = res.status;\n      delete res.status;\n      res.header = res.headers;\n      delete res.headers;\n      originSuccess && originSuccess(res);\n      resolve(res);\n    };\n\n    options.fail = function (res) {\n      originFail && originFail(res);\n      reject(res);\n    };\n\n    options.complete = function (res) {\n      originComplete && originComplete(res);\n    };\n\n    requestTask = RequestQueue.request(options);\n  });\n\n  p.abort = function (cb) {\n    cb && cb();\n\n    if (requestTask) {\n      requestTask.abort();\n    }\n\n    return p;\n  };\n\n  return p;\n}\n\nfunction processApis(taro) {\n  var weApis = Object.assign({}, onAndSyncApis, noPromiseApis, otherApis);\n  var preloadPrivateKey = '__preload_';\n  var preloadInitedComponent = '$preloadComponent';\n  Object.keys(weApis).forEach(function (key) {\n    if (!onAndSyncApis[key] && !noPromiseApis[key]) {\n      taro[key] = function (options) {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n\n        var result = generateSpecialApis(key, options || {});\n        var newKey = result.api;\n        options = result.options;\n        var task = null;\n        var obj = Object.assign({}, options);\n\n        if (!(newKey in my)) {\n          console.warn(\"\\u652F\\u4ED8\\u5B9D\\u5C0F\\u7A0B\\u5E8F\\u6682\\u4E0D\\u652F\\u6301 \".concat(newKey));\n          return;\n        }\n\n        if (typeof options === 'string') {\n          if (args.length) {\n            var _my;\n\n            return (_my = my)[newKey].apply(_my, [options].concat(args));\n          }\n\n          return my[newKey](options);\n        }\n\n        if (key === 'navigateTo' || key === 'redirectTo' || key === 'switchTab') {\n          var url = obj.url ? obj.url.replace(/^\\//, '') : '';\n          if (url.indexOf('?') > -1) url = url.split('?')[0];\n          var Component = (0, _dataCache.cacheDataGet)(url);\n\n          if (Component) {\n            var component = new Component();\n\n            if (component.componentWillPreload) {\n              var cacheKey = (0, _utils.getUniqueKey)();\n              var MarkIndex = obj.url.indexOf('?');\n              var hasMark = MarkIndex > -1;\n              var urlQueryStr = hasMark ? obj.url.substring(MarkIndex + 1, obj.url.length) : '';\n              var params = (0, _utils.queryToJson)(urlQueryStr);\n              obj.url += (hasMark ? '&' : '?') + \"\".concat(preloadPrivateKey, \"=\").concat(cacheKey);\n              (0, _dataCache.cacheDataSet)(cacheKey, component.componentWillPreload(params));\n              (0, _dataCache.cacheDataSet)(preloadInitedComponent, component);\n            }\n          }\n        }\n\n        var p = new Promise(function (resolve, reject) {\n          ['fail', 'success', 'complete'].forEach(function (k) {\n            obj[k] = function (res) {\n              if (k === 'success') {\n                if (newKey === 'saveFile') {\n                  res.savedFilePath = res.apFilePath;\n                } else if (newKey === 'downloadFile') {\n                  res.tempFilePath = res.apFilePath;\n                } else if (newKey === 'chooseImage') {\n                  res.tempFilePaths = res.apFilePaths;\n                } else if (newKey === 'getClipboard') {\n                  res.data = res.text;\n                } else if (newKey === 'scan') {\n                  res.result = res.code;\n                } else if (newKey === 'getScreenBrightness') {\n                  res.value = res.brightness;\n                  delete res.brightness;\n                }\n              }\n\n              options[k] && options[k](res);\n\n              if (k === 'success') {\n                resolve(res);\n              } else if (k === 'fail') {\n                reject(res);\n              }\n            };\n          });\n\n          if (args.length) {\n            var _my2;\n\n            task = (_my2 = my)[newKey].apply(_my2, [obj].concat(args));\n          } else {\n            task = my[newKey](obj);\n          }\n        });\n\n        if (newKey === 'uploadFile' || newKey === 'downloadFile') {\n          p.progress = function (cb) {\n            if (task) {\n              task.onProgressUpdate(cb);\n            }\n\n            return p;\n          };\n\n          p.abort = function (cb) {\n            cb && cb();\n\n            if (task) {\n              task.abort();\n            }\n\n            return p;\n          };\n        }\n\n        return p;\n      };\n    } else {\n      taro[key] = function () {\n        if (!(key in my)) {\n          console.warn(\"\\u652F\\u4ED8\\u5B9D\\u5C0F\\u7A0B\\u5E8F\\u6682\\u4E0D\\u652F\\u6301 \".concat(key));\n          return;\n        }\n\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n\n        if (key === 'getStorageSync') {\n          var arg1 = args[0];\n\n          if (arg1 != null) {\n            return my[key]({\n              key: arg1\n            }).data || my[key]({\n              key: arg1\n            }).APDataStorage || '';\n          }\n\n          return console.log('getStorageSync 传入参数错误');\n        }\n\n        if (key === 'setStorageSync') {\n          var _arg = args[0];\n          var arg2 = args[1];\n\n          if (_arg != null) {\n            return my[key]({\n              key: _arg,\n              data: arg2\n            });\n          }\n\n          return console.log('setStorageSync 传入参数错误');\n        }\n\n        if (key === 'removeStorageSync') {\n          var _arg2 = args[0];\n\n          if (_arg2 != null) {\n            return my[key]({\n              key: _arg2\n            });\n          }\n\n          return console.log('removeStorageSync 传入参数错误');\n        }\n\n        if (key === 'createSelectorQuery') {\n          var query = my[key]();\n\n          query[\"in\"] = function () {\n            return query;\n          };\n\n          return query;\n        }\n\n        var argsLen = args.length;\n        var newArgs = args.concat();\n        var lastArg = newArgs[argsLen - 1];\n\n        if (lastArg && lastArg.isTaroComponent && lastArg.$scope) {\n          newArgs.splice(argsLen - 1, 1, lastArg.$scope);\n        }\n\n        return my[key].apply(my, newArgs);\n      };\n    }\n  });\n}\n\nfunction pxTransform(size) {\n  var _ref = this.config || {},\n      _ref$designWidth = _ref.designWidth,\n      designWidth = _ref$designWidth === void 0 ? 750 : _ref$designWidth,\n      _ref$deviceRatio = _ref.deviceRatio,\n      deviceRatio = _ref$deviceRatio === void 0 ? {\n    640: 2.34 / 2,\n    750: 1,\n    828: 1.81 / 2\n  } : _ref$deviceRatio;\n\n  if (!(designWidth in deviceRatio)) {\n    throw new Error(\"deviceRatio \\u914D\\u7F6E\\u4E2D\\u4E0D\\u5B58\\u5728 \".concat(designWidth, \" \\u7684\\u8BBE\\u7F6E\\uFF01\"));\n  }\n\n  return parseInt(size, 10) * deviceRatio[designWidth] + 'rpx';\n}\n\nfunction generateSpecialApis(api, options) {\n  var apiAlias = api;\n\n  if (api === 'showModal') {\n    options.cancelButtonText = options.cancelText;\n    options.confirmButtonText = options.confirmText || '确定';\n    apiAlias = 'confirm';\n\n    if (options.showCancel === false) {\n      options.buttonText = options.confirmText || '确定';\n      apiAlias = 'alert';\n    }\n  } else {\n    Object.keys(apiDiff).forEach(function (item) {\n      var apiItem = apiDiff[item];\n\n      if (api === item) {\n        if (apiItem.alias) {\n          apiAlias = apiItem.alias;\n        }\n\n        if (apiItem.options) {\n          var change = apiItem.options.change;\n          var set = apiItem.options.set;\n\n          if (change) {\n            change.forEach(function (changeItem) {\n              options[changeItem[\"new\"]] = options[changeItem.old];\n            });\n          }\n\n          if (set) {\n            set.forEach(function (setItem) {\n              options[setItem.key] = typeof setItem.value === 'function' ? setItem.value(options) : setItem.value;\n            });\n          }\n        }\n      }\n    });\n  }\n\n  return {\n    api: apiAlias,\n    options: options\n  };\n}\n\nfunction initNativeApi(taro) {\n  processApis(taro);\n  taro.request = link.request.bind(link);\n  taro.addInterceptor = link.addInterceptor.bind(link);\n  taro.cleanInterceptors = link.cleanInterceptors.bind(link);\n  taro.getCurrentPages = getCurrentPages;\n  taro.getApp = getApp;\n  taro.initPxTransform = initPxTransform.bind(taro);\n  taro.pxTransform = pxTransform.bind(taro);\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.cacheDataSet = cacheDataSet;\nexports.cacheDataGet = cacheDataGet;\nexports.cacheDataHas = cacheDataHas;\nvar data = {};\n\nfunction cacheDataSet(key, val) {\n  data[key] = val;\n}\n\nfunction cacheDataGet(key, delelteAfterGet) {\n  var temp = data[key];\n  delelteAfterGet && delete data[key];\n  return temp;\n}\n\nfunction cacheDataHas(key) {\n  return key in data;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.queryToJson = queryToJson;\nexports.getUniqueKey = getUniqueKey;\n\nfunction queryToJson(str) {\n  var dec = decodeURIComponent;\n  var qp = str.split('&');\n  var ret = {};\n  var name;\n  var val;\n\n  for (var i = 0, l = qp.length, item; i < l; ++i) {\n    item = qp[i];\n\n    if (item.length) {\n      var s = item.indexOf('=');\n\n      if (s < 0) {\n        name = dec(item);\n        val = '';\n      } else {\n        name = dec(item.slice(0, s));\n        val = dec(item.slice(s + 1));\n      }\n\n      if (typeof ret[name] === 'string') {\n        // inline'd type check\n        ret[name] = [ret[name]];\n      }\n\n      if (Array.isArray(ret[name])) {\n        ret[name].push(val);\n      } else {\n        ret[name] = val;\n      }\n    }\n  }\n\n  return ret; // Object\n}\n\nvar _i = 1;\n\nvar _loadTime = new Date().getTime().toString();\n\nfunction getUniqueKey() {\n  return _loadTime + _i++;\n}"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAEA;AAEA;AACA,WA8BA;AACA;;;;;;;;;;;;;;AClDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACxBA;AAAA;AAGA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;ACzBA;AAAA;AAGA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;AC5CA;AAEA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AC/DA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;AClCA;AACA;AACA;AACA;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACvCA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;ACLA;ACKA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;;AAGA;AACA;AACA;AACA;AACA;;AAGA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;AAEA;;AAEA;AACA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;;AAGA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChXA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;ACnHA;AASA;AAWA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AC7CA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAGA;AAEA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AAEA;AACA;AACA;;AAGA;AAMA;AAOA;;AACA;AAEA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;;;AAKA;AACA;AACA;AACA;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;;AAGA;AACA;ACpIA;AACA;AAUA;AACA;AACA;AAEA;AAkBA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;;AAEA;AAEA;AACA;AAIA;AACA;AACA;AACA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;ACpJA;AAAA;AAUA;AAAA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AAEA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;ACzCA;AACA;AAQA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AAEA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AASA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClnCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC3BA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;ACxBA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;AAKA;;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AC5DA;AAIA;AAAA;AACA;AAAA;AAJA;AAMA;;AACA;AAAA;AAAA;;AAGA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEA;;AAGA;AAEA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AAEA;AAEA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;;AACA;AAAA;AAAA;AAtEA;AACA;ACQA;;;;;;AAMA;;AACA;AAEA;AACA;;AAMA;AAIA;AACA;AAEA;AACA;AACA;AACA;;;AAIA;AACA;AACA;AACA;AACA;AAKA;;AAEA;AACA;;;AAIA;;AAGA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;AAGA;AACA;;AAGA;AACA;;AAGA;AAEA;AACA;ACnFA;ACDA;AAAA;AACA;AACA;AACA;AACA;AACA;ACeA;AAGA;AAAA;AASA;AAAA;AACA;AAEA;AAAA;AAEA;AAVA;AACA;AAgBA;AAAA;AAAA;AAAA;AAAA;AANA;AACA;AACA;AACA;AAAA;;;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;;AAGA;;AACA;AAAA;AAAA;AAGA;AAEA;AAEA;;AACA;AAAA;AAAA;AAGA;AAEA;;AAEA;AAAA;AAAA;AACA;AACA;AAEA;;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;;AAEA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;;;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAGA;AAEA;AACA;;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;AAEA;;AAEA;AACA;;;AAGA;;AACA;AAAA;AAAA;;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;;AACA;AAAA;AAAA;AAnNA;ACZA;AAAA;AAGA;AAAA;AACA;AAEA;AAAA;AAEA;AACA;AACA;AAAA;;AACA;AAAA;AAAA;AAWA;AACA;AAAA;AATA;AACA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAWA;AACA;AAAA;AALA;;AACA;AAAA;AAAA;AA3BA;AACA;ACZA;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACxLA;AACA;AACA;AACA;;AAGA;AAKA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAVA;AAAA;AAAA;AAYA;AACA;AAEA;AACA;AACA;AAAA;AASA;AAAA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;;AAGA;AACA;AACA;AAEA;AACA;;AAGA;AAEA;AACA;AACA;AACA;;;AAIA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;AAEA;;;AAEA;AAAA;AAAA;AAGA;;AAEA;AACA;AACA;;AAEA;AACA;;AAGA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;AAGA;AACA;AACA;;AAGA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;;AAGA;;AACA;AAAA;AAAA;AAGA;ACvJA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;;AAGA;;AAGA;AACA;AAEA;AACA;AAEA;AACA;;AAGA;AAEA;AACA;AACA;;AAGA;AAEA;AACA;;AAEA;AACA;AAEA;AACA;AAAA;AChDA;AAGA;AAAA;AAAA;AACA;AACA;AACA;AAAA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AAEA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AAEA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AC5BA;AAAA;AAOA;AAAA;AACA;AAGA;AAAA;AAEA;AAXA;AAEA;AAUA;AACA;AACA;AACA;AAAA;;AACA;AAAA;AAAA;;AAIA;;AAEA;AACA;AAEA;AACA;;AAGA;AACA;AACA;;;;AAGA;AAAA;AAAA;AAGA;AACA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;;AACA;AAAA;AAAA;AAGA;AACA;AAEA;AACA;;AAGA;AACA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;;;;AAIA;AACA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;;AAGA;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;AAEA;;AAEA;;AAGA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAEA;AAEA;AACA;;;AAGA;AACA;AACA;AAGA;AACA;;AAEA;AACA;AACA;AACA;;;;AAGA;AAAA;AAAA;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;;AAEA;;AAGA;AAEA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAGA;AAAA;AAAA;AAGA;AACA;;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAAA;AACA;;AACA;AAAA;AAAA;AAGA;AAEA;AAEA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAGA;AACA;;;AAIA;AACA;AACA;AACA;;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAGA;;AACA;AAAA;AAAA;AAGA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;AA1SA;AACA;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACxBA;AACA;AACA;ACAA;AACA;AACA;AACA;AACA;AACA;ACLA;AACA;AACA;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC5CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AC3BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACnBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC7CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACbA;AACA;AACA;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACnBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC9BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC3BA;AACA;AACA;ACDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACnBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACrEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACzBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC5BA;AACA;AACA;AACA;AAAA;ACHA;AAAA;AACA;;AAmBA;AAAA;AAAA;AAhBA;AACA;;AAEA;;AACA;AAAA;AAAA;AAGA;AACA;;AAEA;AACA;AACA;;AAEA;;AACA;AAAA;AAAA;AAGA;ACHA;AAGA;AAAA;AAaA;AAAA;AACA;AAIA;AAAA;AAEA;AAnBA;AAEA;AAEA;AAIA;AAEA;AAUA;AACA;AAAA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AAEA;AACA;;;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AACA;AACA;;AAEA;;AACA;AAGA;;AAEA;AACA;;AAEA;AAEA;AACA;AACA;;AAEA;AAZA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;AAnLA;AAoLA;ACnMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAGA;AACA;AACA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;;;;AAIA;;;AAIA;;AACA;AAAA;AAAA;AChCA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;ACeA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AAEA;AACA;AAEA;;AAGA;AACA;AACA;AACA;AACA;;;;AAKA;AACA;AACA;AACA;AACA;;;AAGA;AAAA;AASA;AAAA;AANA;AAEA;AAKA;;AACA;AAAA;AAAA;AAGA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAMA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AACA;AACA;AACA;;AAEA;AACA;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AAEA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA;AAAA;AAAA;AAGA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAGA;AACA;AACA;AACA;;AAEA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;;AAGA;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;;AAGA;AAAA;AAAA;AAAA;;;AAEA;AAAA;AAAA;AAGA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;;;AAGA;AAAA;AAAA;AC/VA;AAIA;AACA;AACA;AACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAGA;AACA;AAEA;AACA;AAEA;AACA;AC9BA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAqBA;AAAA;AACA;;AAoMA;AAAA;AAAA;AAlMA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AAEA;;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAxBA;AAAA;AAAA;;;AA0BA;AAAA;AAAA;AAGA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAGA;AACA;;AAGA;AACA;AACA;;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAKA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;;;AAKA;AACA;AACA;;AACA;AAAA;AAAA;;AAIA;;AAGA;;AAGA;AACA;AACA;AACA;AACA;AACA;;;;;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAMA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AACA;ACtQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;;AAGA;AACA;AAEA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AASA;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAGA;AACA;AAEA;AAEA;AACA;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AAAA;AACA;AACA;AAEA;;AAGA;AACA;AACA;AACA;AAEA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAEA;AAEA;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAIA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAIA;AACA;AC/QA;AACA;AACA;AAKA;AAIA;AACA;AAEA;AACA;AACA;;AAEA;AAEA;AACA;;AAEA;AACA;AChBA;;;;AAIA;;AAKA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AAEA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AACA;AAAA;AAAA;;AAGA;AACA;AC7DA;AAGA;AAAA;AACA;AAAA;AAEA;AAAA;AAAA;;AACA;AAAA;AAAA;AAGA;AAEA;AACA;AACA;AACA;;;AAGA;AACA;;;AAEA;AAAA;AAAA;AArBA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AClDA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AClBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAKA;AACA;;;AAEA;AAAA;AAAA;AAVA;AAaA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;ACTA;AAAA;AAGA;AAAA;AACA;AAIA;AAAA;AAEA;AACA;AACA;AACA;AAAA;;AACA;AAAA;AAAA;AAGA;AACA;;AAGA;AACA;;AAGA;;;;;;AAKA;AACA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;AACA;;AACA;AAAA;AAAA;;AAIA;AACA;;AAEA;;AACA;AAAA;AAAA;;AAIA;;;;;AAIA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AA7DA;ACQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AA+CA;AAAA;AAAA;;AACA;AAAA;AAAA;;AAMA;AAAA;AAAA;;AACA;AAAA;AAAA;;AAMA;AAAA;AAAA;;AACA;AAAA;AAAA;;AAMA;AAAA;AAAA;;AACA;AAAA;AAAA;AAnEA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAOA;AAOA;AAOA;AAjEA;AACA;AC9BA;;;;AAIA;ACEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AC3BA;AACA;AACA;AACA;;AAEA;AACA;AAEA;;AAEA;AAAA;AAAA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;ACZA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAGA;AACA;AACA;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AAAA;AAoBA;AAAA;AAZA;AAEA;AAEA;;;AAIA;AAKA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;;AAGA;AACA;AAGA;AAAA;;AAEA;AAAA;AAAA;;AAGA;AAEA;AACA;;AAGA;AAGA;AAAA;;AAEA;AAAA;AAAA;AAGA;AACA;;AAEA;AAAA;AAAA;AAAA;;AAGA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;;;AAIA;;AAEA;;AAGA;AACA;AAEA;AAEA;AACA;;AACA;AAEA;AACA;;AAGA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAMA;AACA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;;AAGA;AC1JA;AACA;AACA;ACWA;;;;;;;;;;;;;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AClDA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACrBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;;AAIA;AACA;AClCA;AACA;AACA;ACEA;AACA;AACA;AACA;AAEA;AACA;AAKA;AACA;AACA;AACA;;AAEA;AAEA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;ACzBA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;ACHA;AAAA;AACA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;AASA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAEA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;;AACA;AAAA;AAAA;AAYA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;;AACA;AAAA;AAAA;AAtGA;AA2GA;AACA;AACA;AACA;AChIA;AACA;AAmBA;AACA;AAEA;;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;AAGA;AAEA;AACA;AAAA;AAAA;AACA;;AAGA;AACA;;AAGA;AACA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AAEA;AACA;AACA;;AAGA;;AAEA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AAGA;AACA;AACA;;AAGA;AAEA;AACA;;AAGA;AACA;;AAGA;AACA;AAEA;;AACA;AACA;AAEA;AACA;AAAA;;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;ACpTA;AAEA;;AACA;AAGA;AAEA;AACA;AACA;AAEA;AAIA;AACA;;AAEA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AAEA;AACA;;AAGA;AAAA;AAAA;AAAA;AAAA;;AACA;AACA;AACA;AAAA;;AAoCA;AAAA;AAAA;;;AA3BA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AAEA;AAAA;AAKA;AACA;AAEA;AAAA;AAAA;;AAKA;AAEA;AAAA;;AAGA;AAAA;AAAA;AAhCA;AACA;AAAA;AAAA;;AACA;AAAA;AAAA;AAgCA;AACA;AAEA;AAIA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;;;AAKA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAgCA;AAEA;AAEA;;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;;;AAEA;AACA;AAAA;;AAuCA;AAAA;AAAA;AApCA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;;;AAIA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;;AAGA;AAEA;AACA;AAAA;AAAA;;AAGA;;AAKA;AAAA;AAAA;AAGA;AACA;AACA;;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;;AACA;AACA;AAAA;AAGA;;AAEA;;AAEA;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AAEA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;;;AAIA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;AAKA;AACA;AACA;AAEA;AAEA;AAAA;AAaA;AAAA;AAAA;AAAA;;AACA;AACA;AAAA;;AAoBA;AAAA;AAAA;AAjBA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AAIA;AACA;;AAIA;AAAA;AAAA;AArBA;AAwBA;AAAA;AAAA;AAAA;;AACA;AACA;AACA;AAAA;;AA2CA;AAAA;AAAA;AAxCA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AAAA;;AAEA;AAAA;AAAA;AAGA;AAEA;AAEA;AAIA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAIA;AACA;AC7fA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AAGA;AACA;AACA;;AAEA;AAEA;AAAA;AAAA;AAAA;;AAIA;AAEA;AACA;AACA;AAEA;AACA;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAiCA;AAEA;AAEA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;;AAEA;;AAEA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AAEA;AAEA;AACA;ACtMA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAGA;AAGA;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAgCA;AAEA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;;AACA;AACA;AAAA;AAGA;AACA;;;;AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;;AAEA;AAEA;AACA;AACA;AACA;;AACA;AACA;AAAA;AAGA;AACA;;AAEA;AAEA;AACA;AACA;AACA;;AACA;AACA;;;AAGA;AAEA;AAEA;AACA;AC1MA;AACA;AACA;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAGA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;AAMA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;AAEA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AC/EA;AACA;AACA;;AAEA;AACA;AAEA;;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;An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eA;A8GrgBA;AAEA;AAEA;AAEA;AAWA;;;;;;;AAOA;AAAA;AAAA;AAAA;AAAA;AAEA;;;;;;;AAOA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AAEA;AAEA;;;;;;AAMA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAA;AACA;;AAGA;AACA;AACA;AACA;;;;AAIA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;ACxLA;AA0HA;AACA;AACA;AACA;AACA;;AAEA;AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AAaA;AAEA;AAAA;AAIA;AACA;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;AAGA;AACA;AACA;;AAEA;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;;AAIA;AACA;;AAGA;;AAGA;AACA;;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;;AAEA;AACA;;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAGA;AACA;;;AAIA;AACA;AACA;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AAEA;AACA;AAEA;;;;;AAKA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC9VA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAEA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AAEA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AC/EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAYA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtDA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAEA;AACA;AACA;;;AAEA;AACA;AACA;;;;ACxBA;AACA;AAAA;AACA;AACA;AACA;;;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAEA;AACA;AACA;;;AAEA;AACA;AACA;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;ACrCA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAEA;AACA;AACA;AACA;AAHA;AAMA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AADA;AAEA;AACA;AACA;AACA;AAHA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AChDA;AACA;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA;AA8BA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAEA;AACA;AACA;AACA;AACA;AACA,SAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;AC3BA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACtiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AAEA;AACA;AACA;;;;A", "sourceRoot": ""}
import { Component, useEffect, useState } from 'react'
import Taro, { usePullDownRefresh } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import moment from 'moment';
const Timer = (props) => {
  const [init, setInit] = useState(['--','--','--']);
  const [showTxt, setShowTxt] = useState('--');
  const [showStatus, setShowStatus] = useState('m');
  let timer = null;
  useEffect(() => {
    const  {start, end} = props;
    const now = moment();
    const end_diff = moment(end).diff(now);
    const start_diff = moment(start).diff(now);
    const timer_status = (start_diff > 0) ? 'will' : 'buy';
    setShowStatus(timer_status);
    const operTimer = (timer_status === 'buy') ? {
      day: moment(end).diff(now, 'day'),
      ms: moment.duration(moment(end).diff(now), 'milliseconds')
    } : {
      day: moment(start).diff(now, 'day'),
      ms: moment.duration(moment(start).diff(now), 'milliseconds')
    }
    //if (timer_status === 'buy') {
      setShowTxt((timer_status === 'buy') ? '结束于' : '开始于');
      //if ( operTimer.day > 2) {
      if (timer_status === 'buy') {
        setInit(moment(end).format('MM-DD-HH').split('-'));
      } else {
        setInit(moment(start).format('MM-DD-HH').split('-'));
      }
      setShowStatus('m');
      //} else {
        // timer = setTimeout(() => {
        //   const timer_all = operTimer.ms;
        //   const timer_hh = (parseInt(timer_all.days(), 10) * 24) + timer_all.hours();
        //   setInit([timer_hh, 
        //         (parseInt(timer_all.minutes(), 10) < 10 ? `0${timer_all.minutes()}` : timer_all.minutes()), 
        //         (parseInt(timer_all.seconds(), 10) < 10 ? `0${timer_all.seconds()}` : timer_all.seconds())
        //         ]);
        // }, 1000);
          // const timer_all = operTimer.ms;
          // const timer_hh = (parseInt(timer_all.days(), 10) * 24) + timer_all.hours();
          // setInit([timer_hh, 
          //       (parseInt(timer_all.minutes(), 10) < 10 ? `0${timer_all.minutes()}` : timer_all.minutes()), 
          //       (parseInt(timer_all.seconds(), 10) < 10 ? `0${timer_all.seconds()}` : timer_all.seconds())
          //       ]);
          // setShowStatus('s');
      //}
     //}
  }, [])
  
    return (
        <View>
            <View className='at-row' >
                    <View className='at-col at-col-1'>
                      <AtIcon value='bell' size='16' color='#fa6f14'></AtIcon>   
                    </View>
                    <View className='at-col at-col-4' style={{textAlign: 'center'}}>
                    <Text style={{fontSize: '24rpx'}}>{showTxt}</Text>
                    </View>
                    {(showStatus === 's') ? <View className='at-col at-col-7'>
                      <View className='time_item time_month'>{init[0]}</View>
                      <Text style={{fontSize: '20rpx'}}>时</Text>
                      <View className='time_item time_month'>{init[1]}</View>
                      <Text style={{fontSize: '20rpx'}}>分</Text>
                      <View className='time_item time_month'>{init[2]}</View>
                      <Text style={{fontSize: '20rpx'}}>秒</Text>
                    </View> : <View className='at-col at-col-7'>
                      <View className='time_item time_month'>{init[0]}</View>
                      <Text style={{fontSize: '20rpx'}}>月</Text>
                      <View className='time_item time_month'>{init[1]}</View>
                      <Text style={{fontSize: '20rpx'}}>日</Text>
                      <View className='time_item time_month'>{init[2]}</View>
                      <Text style={{fontSize: '20rpx'}}>时</Text>
                    </View>}
                    
                  </View>
                  {/* <View className='at-row' >
                    <View className='at-col at-col-1'>
                      <AtIcon value='bell' size='16' color='#fa6f14'></AtIcon>   
                    </View>
                    <View className='at-col at-col-4' style={{textAlign: 'center'}}>
                      <Text style={{fontSize: '24rpx'}}>结束于</Text>
                    </View>
                    <View className='at-col at-col-7'>
                      <View className='time_item time_month'>11</View>
                      <Text style={{fontSize: '20rpx'}}>月</Text>
                      <View className='time_item time_month'>11</View>
                      <Text style={{fontSize: '20rpx'}}>日</Text>
                      <View className='time_item time_month'>20</View>
                      <Text style={{fontSize: '20rpx'}}>时</Text>
                    </View>
                  </View> */}
        </View>
    )
}
Timer.options = {
    addGlobalClass: true
  }
  Timer.defaultProps = {
    start: '2020-12-12',
    end: '2020-12-12'
  }
export default Timer;
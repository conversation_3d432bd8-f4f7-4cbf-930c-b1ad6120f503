/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-18 00:28:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/topic.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View, Image } from '@tarojs/components'
import { useSelector } from 'react-redux'
import Taro, { useReachBottom } from '@tarojs/taro'
import Memu from '@/components/memu'
import None from '@/components/tips/none'
import SreachTools from './bar'
import Item from '@/components/brand/item'
import tools from '@/utils/tools'
import "./index.scss"

const SreachIndex = (props) => {
    const query = tools.getQuery()
    const login = useSelector(state => state.login)
    const [init, setInit] = useState(null)
    const [page , setPage] = useState(1)
    const [brandList , setBrandList] = useState([])  
    const [loaded, setLoaded] = useState(false)
    const [title, setTitle] = useState(null)
    useEffect( async () => {
        setLoaded(false)
        Taro.showLoading({
          mask: true,
          title: '请稍等'
        })
        if (login && query.data.v) {
          const apiParams = {
            point : true,
            code : query.data.v,
          }
          const data = await tools.Api.topicInfo(apiParams)
          if (data.code === 200) {
            setBrandList(data.data.list)
            setTitle(data.data.topic)
            Taro.setNavigationBarTitle({
              title: `${data.data.topic.name}`
            })
          }
          setLoaded(true)
          Taro.hideLoading()
        }
    }, [login, page])

    useReachBottom(() => {
        const nextPage = init.current + 1
        if (nextPage <= init.totalPage) {
          setPage(nextPage)
        } else {
          Taro.showToast({
            title: '暂无更多内容',
            icon: 'none',
            duration: 2000
          })
        }
    })

    return (
        <View>
          {
            (title !== null) ? <View>
              <Image src={title.image} mode='widthFix' style={{width: '100%', marginBottom: '20px'}}/>
            </View> : null
          }
          
          <View>
            {
                (brandList.length > 0) ?  brandList.map(v => <Item className="list" data={v} cname="images_l"/>) : <None loaded={loaded}/>
            }
          </View>
          <Memu now={3} />
        </View>
    )
}
export default SreachIndex 
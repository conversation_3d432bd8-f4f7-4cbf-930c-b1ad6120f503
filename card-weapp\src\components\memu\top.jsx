/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-24 21:57:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/memu/top.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View, Text, Image, Swiper, SwiperItem } from '@tarojs/components'
import Taro from '@tarojs/taro'
import tools from '@/utils/tools'

const Top = (props) => {
    return (
        <View className="topNav" style={{display: "none", backgroundColor: (props.bg === 0) ? `#c5071100` : '#c50711'}}>
            <View style={{height:  `${tools.reTopH(6)}rpx`}}></View>
            <View className='at-row'>
                <View className='at-col at-col-1 left' onClick={() => {
                    Taro.navigateTo({
                        url: '/pages/sreach/index'
                    })
                }}>
                    <Image src={`${tools.picUrl}/memu/find.png`} mode='widthFix' className="topIcon"/>
                </View>
                <View className='at-col at-col-2' onClick={() => {
                    Taro.showToast({
                        title: '快速入园，敬请期待',
                        icon: 'none',
                        duration: 2000
                      })
                }}>
                    <Image src={`${tools.picUrl}/memu/scan.png`} mode='widthFix' className="topIcon" style={{marginLeft: '15px'}}/>
                </View>
                <View className='at-col at-col-8'></View>
            </View>
        </View>
    )
}
export default Top 
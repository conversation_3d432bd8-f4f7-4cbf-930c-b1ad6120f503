import { Component,useState, useEffect, Fragment } from 'react'
import Taro from '@tarojs/taro';
import { View, Text, Button, Image } from '@tarojs/components'
import tools from '@/utils/tools'
import moment from "moment"
import './index.scss'

const Item = props => {
    const { data } = props
    console.warn('--------------------', data)
    const [show, setShow] = useState(true)
    const csscolor = data.bound || data.refund_status ? {color: '#ccc'} : {}
    return (
        <Fragment>
        {show ? <View className='bookItem'>
        <View className='at-row cardNum'>
            <View className='at-col at-col-4' style={csscolor}>
            状态：{data.bound ? '已使用' : '未使用'} {data.refund_status ? '(已退)' : ''}
            </View>
            <View className='at-col at-col-7' style={{textAlign: 'right'}}>
                <Text style={csscolor}>购买日期：{data.create_time}</Text>
            </View>
        </View>
        <View>
        <View className='at-row' style={{ padding: '0 14px', width: '90%', marginTop: '14px'}}>
            <View className='at-col at-col-3' style={{textAlign: 'center'}}>
                <View className='day'  style={csscolor}>北京风景名胜区电子年票一张</View>
            </View>
        </View>
        {!data.refund_status ? <View className='at-row' style={{ padding: '0 14px',width: '90%', marginBottom: '0px', marginTop: '14px', color:'#8e8c8c'}}
            onClick={() => {
            Taro.setClipboardData({
              data: data.good_key,
              success: function (res) {
                Taro.showToast({
                   title: '激活码已复制',
                   icon: 'none'
                })
              }
            })
        }}>
            <View>
                <Text style={csscolor}>激活码：{data.good_key}</Text>
            </View>
            <View className='copy_btn'>点击复制</View>
        </View> : <View></View>}
        <View className='at-row' style={{ padding: '0 14px',width: '90%', marginBottom: '14px', marginTop: '6px', color:'#8e8c8c'}}>
            <View className='at-col at-col-12' style={{textAlign: 'left'}}>
                <Text style={csscolor}>凭此激活码可激活年票一张，重绑无效，请妥善保管</Text>
            </View>
        </View>
        {!data.bound && !data.refund_status ? <View className='at-row oper'>
            <View className='at-col at-col-6' style={{textAlign: 'center', borderRight: '1px solid #e2e2e2'}} onClick={ () => {
                Taro.navigateTo({
                    url : `/pages/bind/index?key=${data.good_key}`
                })
            }}>
                立即激活绑定
            </View>
            <View className='at-col at-col-6' style={{textAlign: 'center'}} onClick={() => {
                Taro.showModal({
                    title: `转赠激活码`,
                    content: `请将该激活码发送给您的家人或朋友，凭此码可激活北京风景名胜区协会年票一张`,
                    success:  async (res) => {
                    }
                  })
            }}>
                转赠家人或朋友
            </View>
        </View> : <View><View style={{height: '10px'}}></View></View>}
        
        </View>
        </View> : null}
        </Fragment>
        
    )
}
export default Item;
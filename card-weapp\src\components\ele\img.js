/*
 * @Author: your name
 * @Date: 2020-12-04 11:06:26
 * @LastEditTime: 2021-11-15 17:55:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /dfx-umi/Users/<USER>/dfx-video/src/components/ele/img.js
 */
import { View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
const Img = (props) => {
    const {km, css, img, type} = props;
    return (
        <View style={{position: 'relative'}}>
            {(km) ? <View style={{position:'absolute', padding: '4rpx 8rpx', fontSize: '20rpx', color: '#fff', background: 'rgba(0, 0, 0, 0.5)', borderRadius: '18rpx', bottom: '15rpx', left: '10rpx', zIndex: 5}}>
                {(type === 'km' || type === 'brand') ? 
                <View>
                    <View style={{display: 'inline-block', position: 'absolute', bottom: '7rpx'}}><AtIcon value='map-pin' size='12' color='#fff'></AtIcon></View>
                    <View style={{display: 'inline-block', paddingLeft: '27rpx'}}>{(type === 'km') ? '' : ''}{km}km</View>
                </View>
                :(type === 'id') ?
                <View>
                    <View style={{display: 'inline-block', position: 'absolute', bottom: '7rpx'}}><AtIcon value='map-pin' size='12' color='#fff'></AtIcon></View>
                    <View style={{display: 'inline-block', paddingLeft: '27rpx'}}>{km}</View>
                </View>
                :(type === 'a.ordernum') ?
                <View>
                    <View style={{display: 'inline-block', position: 'absolute', bottom: '7rpx'}}><AtIcon value='tags' size='12' color='#fff'></AtIcon></View>
                    <View style={{display: 'inline-block', paddingLeft: '27rpx'}}>{(parseInt(km, 10) === 0) ? '新品上架' : `热销${km}单`}</View>
                </View>
                :(type === 'all') ?
                <View>
                    <View style={{display: 'inline-block', position: 'absolute', bottom: '7rpx'}}><AtIcon value='map-pin' size='12' color='#fff'></AtIcon></View>
                    <View style={{display: 'inline-block', paddingLeft: '27rpx'}}>{props.province}{props.city} {parseInt(km, 10)}km</View>
                </View>
                : <View>
                <View style={{display: 'inline-block', position: 'absolute', bottom: '7rpx'}}><AtIcon value='map-pin' size='12' color='#fff'></AtIcon></View>
                <View style={{display: 'inline-block', paddingLeft: '27rpx'}}>距离我{parseInt(km, 10)}km</View>
            </View>}
                
            </View> : null}
            <Image src={img} mode="aspectFill" lazyLoad={true} className={css} />
        </View>
    )
}
Img.options = {
    addGlobalClass: true
  }
  Img.defaultProps = {
    km : '--',
    css: '',
    img: '',
    type: 'km'
}
export default Img;
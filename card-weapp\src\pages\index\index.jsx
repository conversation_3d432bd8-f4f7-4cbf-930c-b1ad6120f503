import { useState, useEffect } from 'react'
import Taro, { usePageScroll, useShareAppMessage } from '@tarojs/taro';
import { useSelector } from 'react-redux'
import { View, Text, Image, Swiper, SwiperItem } from '@tarojs/components'
import BrandIndex from '@/components/brand'
import Memu from '@/components/memu'
import Top from '@/components/memu/top'
import { AtGrid, AtToast } from "taro-ui"
import tools from '@/utils/tools'
import './index.scss'


function Index (props) { 
  const [isOpened, setIsOpened] = useState(false)
  const [blurVal, setBlurVal] = useState(0)
  const login = useSelector(state => state.login)
  const [init, setInit] = useState(null)
  const [loading, setLoading] = useState(true)
  const [btnStatus, setBtnStatus] = useState('LOAD')
  const [buyList , setBuyList] = useState({
    list: [],
    title: "年票福利",
    code: 0,
    type: 'buy'
  })  
  const loadData = async () => {
    // Taro.showLoading({
    //   mask: true,
    //   title: '读取中'
    // })
    const data = await tools.Api.index({
      line_id : tools.line_code,
      order : "km",
      point : true,
      page : {
          "current": 1,
          "pageSize" : 5
      }
    })
    //Taro.hideLoading()
    return data
  }

  useEffect( async () => {
    if (login) {
      const userInfo = tools.getDataSafe('userInfo');
      const point = tools.getDataSafe('mypoint')
      const buyItem = {"admin":"buyer","showlist":1,"from":"fe","page":0,"enter_user_id":0,"agent_id":1,"pageSize":8,"currentPage":1,"buyer":userInfo.mem_id,"order":"a.ordernum","ordertype":"desc","range":{"latitude":point.api.latitude,"longitude":point.api.longitude,"val":5000}}
      // const buyData = await tools.Api.brandBuyList(buyItem)
      // if (buyData.code === 0) {
      //   setBuyList({
      //     ...buyList,
      //     list: buyData.list
      //   })
      // }
      const data = await loadData()
      const cardStatus = await tools.getCardList()
      setBtnStatus(cardStatus.status)
      if (data.code === 200) {
        setInit(data.data)
        setLoading(false)
      }
    }
  }, [login])

  useShareAppMessage(() => {
    return {
      title: '北京风景名胜年票',
      path: '/pages/index/index',
      imageUrl: 'https://cardall.oss-cn-beijing.aliyuncs.com/card/2021/12/1640067294_4f8NtU3L.jpg'
    }
  })

  usePageScroll(res => {
    if (res.scrollTop >= 100) {
      setBlurVal(25)
    }
    if (res.scrollTop < 100) {
      setBlurVal(0)
    }
  })

  return (
    <View>
        {(init === null) ?  <AtToast isOpened text="请稍等" status="loading" duration={0}></AtToast> :
        <View>
        <Top bg={blurVal}/>
        <View className="top_pic" style={{filter: `blur(${blurVal}px)`}}>
          <Swiper
            className='index_swiper'
            indicatorColor='#999'
            indicatorActiveColor='#333'
            circular
            indicatorDots
            autoplay>
            {
              init.imgList.trunList.map(val => <SwiperItem onClick={() => {
                Taro.navigateTo({
                  url : `/pages/brand/index?v=${val.link_code}`
                })
              }}>
                <Image src={val.image} mode="aspectFill" className="images"/>
              </SwiperItem>)
            }
          </Swiper>
          {/*<Image src={`${tools.picUrl}/index/bg4.png`}  className="images_bottom" mode="widthfix"/>*/}
        </View>
        
        <View className="index_center">
          <View className="nav">
            <AtGrid hasBorder={false} columnNum={4} data={
                [
                  {
                    image: `${tools.picUrl}/index/card.png`,
                    value: '购买年票',
                    page: 'buy/index'
                  },
                  {
                    image: `${tools.picUrl}/index/plan.png`,
                    value: '入园记录',
                    page: 'my/use'
                  },
                  {
                    image: `${tools.picUrl}/index/book.png`,
                    value: '景区预约',
                    page: 'sreach/book'
                  },
                  {
                    image: `${tools.picUrl}/index/news.png`,
                    value: '最新通知',
                    page: 'news/index'
                  }
                ]
              }  onClick={ async (item) => {
                if (item.page === 'tel') {
                  Taro.makePhoneCall({
                    phoneNumber: tools.phone
                  })
                } else if (item.page === 'card') {
                  // await tools.appConfig('buy')
                } else {
                  Taro.navigateTo({
                    url: `/pages/${item.page}`
                  })
                }
              }}/>
          </View>
          {<View className='btn' style={{fontWeight: 'bold'}} onClick={() => {
            Taro.navigateTo({
              url: (btnStatus === 'BOOK') ? '/pages/qr/index' : '/pages/bind/index'
            })
          }}>{(btnStatus === 'BOOK') ? '我的入园码' : '激活年票'}</View>}
          <Swiper
            className='swiper'
            indicatorColor='#999'
            indicatorActiveColor='#333'
            circular
            interval= {8000}
            autoplay>
            {
              init.imgList.topicList.map(val => <SwiperItem>
                <Image src={val.image} mode="aspectFill" className="images" onClick={() => {
                  Taro.navigateTo({
                    url : `/pages/sreach/topic?v=${val.link_code}`
                  })
                }}/>
              </SwiperItem>)
            }
          </Swiper>
        </View>
        <View className="content_index">
        {
              init.brandList.map(val =>   {
                val.type = "book";
                return (val.list.length > 1) ? <BrandIndex data={val}/> : null
              })
          }
         
        </View>
        <Memu now={0}/>
        </View>
      }
    </View>
  )
}
export default Index;

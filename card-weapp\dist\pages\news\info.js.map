{"version": 3, "file": "pages/news/info.js", "sources": ["webpack:///./src/pages/news/info.jsx", "webpack:///./src/pages/news/info.jsx?ba48"], "sourcesContent": ["/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2021-12-27 17:24:13\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/news/info.jsx\r\n */\r\nimport {  useState, useEffect } from 'react'\r\nimport { View, RichText } from '@tarojs/components'\r\nimport Taro from '@tarojs/taro'\r\nimport tools from '@/utils/tools'\r\nimport \"./index.scss\"\r\n\r\nconst SreachIndex = () => {\r\n    const query = tools.getQuery()\r\n    const [list, setList] = useState(null)\r\n    useEffect( async () => {\r\n      Taro.showLoading({\r\n        mask: true,\r\n        title: '读取中'\r\n      })\r\n      const data = await tools.Api.noteInfo({\r\n        code : query.data.v\r\n      })\r\n      if (data.code === 200) {\r\n        setList(data.data)\r\n        Taro.setNavigationBarTitle({title:data.data.title})\r\n      }\r\n      Taro.hideLoading()\r\n    }, [])\r\n    \r\n    return (\r\n      <View>\r\n        {\r\n          list !== null ? \r\n          <View className='at-article'>\r\n          <View className='at-article__h1' style={{fontSize: '22px', lineHeight: '28px'}}>\r\n            {list.title}\r\n          </View>\r\n          <View className='at-article__info' style={{color: '#968e8e', marginTop: '8px'}}>\r\n          {list.add_time}\r\n          </View>\r\n          <View className='at-article__content'>\r\n            <View className=\"brand_html\">\r\n              <RichText className='htmlformat' style={{lineHeight: '30px'}} nodes={tools.removeCss(list.content)}/>\r\n            </View>\r\n          </View>\r\n        </View>\r\n          : null\r\n        }\r\n             \r\n      </View>\r\n\r\n    )\r\n}\r\nexport default SreachIndex ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./info.jsx\"\nvar config = {\"navigationBarTitleText\":\"loading\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/news/info', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAFA;AAGA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAMA;AACA;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
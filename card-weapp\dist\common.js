(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["common"],{

/***/ "./node_modules/moment/dist/locale sync recursive ^\\.\\/.*$":
/*!*******************************************************!*\
  !*** ./node_modules/moment/dist/locale sync ^\.\/.*$ ***!
  \*******************************************************/
/*! no static exports found */
/*! all exports used */
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./af": "./node_modules/moment/dist/locale/af.js",
	"./af.js": "./node_modules/moment/dist/locale/af.js",
	"./ar": "./node_modules/moment/dist/locale/ar.js",
	"./ar-dz": "./node_modules/moment/dist/locale/ar-dz.js",
	"./ar-dz.js": "./node_modules/moment/dist/locale/ar-dz.js",
	"./ar-kw": "./node_modules/moment/dist/locale/ar-kw.js",
	"./ar-kw.js": "./node_modules/moment/dist/locale/ar-kw.js",
	"./ar-ly": "./node_modules/moment/dist/locale/ar-ly.js",
	"./ar-ly.js": "./node_modules/moment/dist/locale/ar-ly.js",
	"./ar-ma": "./node_modules/moment/dist/locale/ar-ma.js",
	"./ar-ma.js": "./node_modules/moment/dist/locale/ar-ma.js",
	"./ar-ps": "./node_modules/moment/dist/locale/ar-ps.js",
	"./ar-ps.js": "./node_modules/moment/dist/locale/ar-ps.js",
	"./ar-sa": "./node_modules/moment/dist/locale/ar-sa.js",
	"./ar-sa.js": "./node_modules/moment/dist/locale/ar-sa.js",
	"./ar-tn": "./node_modules/moment/dist/locale/ar-tn.js",
	"./ar-tn.js": "./node_modules/moment/dist/locale/ar-tn.js",
	"./ar.js": "./node_modules/moment/dist/locale/ar.js",
	"./az": "./node_modules/moment/dist/locale/az.js",
	"./az.js": "./node_modules/moment/dist/locale/az.js",
	"./be": "./node_modules/moment/dist/locale/be.js",
	"./be.js": "./node_modules/moment/dist/locale/be.js",
	"./bg": "./node_modules/moment/dist/locale/bg.js",
	"./bg.js": "./node_modules/moment/dist/locale/bg.js",
	"./bm": "./node_modules/moment/dist/locale/bm.js",
	"./bm.js": "./node_modules/moment/dist/locale/bm.js",
	"./bn": "./node_modules/moment/dist/locale/bn.js",
	"./bn-bd": "./node_modules/moment/dist/locale/bn-bd.js",
	"./bn-bd.js": "./node_modules/moment/dist/locale/bn-bd.js",
	"./bn.js": "./node_modules/moment/dist/locale/bn.js",
	"./bo": "./node_modules/moment/dist/locale/bo.js",
	"./bo.js": "./node_modules/moment/dist/locale/bo.js",
	"./br": "./node_modules/moment/dist/locale/br.js",
	"./br.js": "./node_modules/moment/dist/locale/br.js",
	"./bs": "./node_modules/moment/dist/locale/bs.js",
	"./bs.js": "./node_modules/moment/dist/locale/bs.js",
	"./ca": "./node_modules/moment/dist/locale/ca.js",
	"./ca.js": "./node_modules/moment/dist/locale/ca.js",
	"./cs": "./node_modules/moment/dist/locale/cs.js",
	"./cs.js": "./node_modules/moment/dist/locale/cs.js",
	"./cv": "./node_modules/moment/dist/locale/cv.js",
	"./cv.js": "./node_modules/moment/dist/locale/cv.js",
	"./cy": "./node_modules/moment/dist/locale/cy.js",
	"./cy.js": "./node_modules/moment/dist/locale/cy.js",
	"./da": "./node_modules/moment/dist/locale/da.js",
	"./da.js": "./node_modules/moment/dist/locale/da.js",
	"./de": "./node_modules/moment/dist/locale/de.js",
	"./de-at": "./node_modules/moment/dist/locale/de-at.js",
	"./de-at.js": "./node_modules/moment/dist/locale/de-at.js",
	"./de-ch": "./node_modules/moment/dist/locale/de-ch.js",
	"./de-ch.js": "./node_modules/moment/dist/locale/de-ch.js",
	"./de.js": "./node_modules/moment/dist/locale/de.js",
	"./dv": "./node_modules/moment/dist/locale/dv.js",
	"./dv.js": "./node_modules/moment/dist/locale/dv.js",
	"./el": "./node_modules/moment/dist/locale/el.js",
	"./el.js": "./node_modules/moment/dist/locale/el.js",
	"./en-au": "./node_modules/moment/dist/locale/en-au.js",
	"./en-au.js": "./node_modules/moment/dist/locale/en-au.js",
	"./en-ca": "./node_modules/moment/dist/locale/en-ca.js",
	"./en-ca.js": "./node_modules/moment/dist/locale/en-ca.js",
	"./en-gb": "./node_modules/moment/dist/locale/en-gb.js",
	"./en-gb.js": "./node_modules/moment/dist/locale/en-gb.js",
	"./en-ie": "./node_modules/moment/dist/locale/en-ie.js",
	"./en-ie.js": "./node_modules/moment/dist/locale/en-ie.js",
	"./en-il": "./node_modules/moment/dist/locale/en-il.js",
	"./en-il.js": "./node_modules/moment/dist/locale/en-il.js",
	"./en-in": "./node_modules/moment/dist/locale/en-in.js",
	"./en-in.js": "./node_modules/moment/dist/locale/en-in.js",
	"./en-nz": "./node_modules/moment/dist/locale/en-nz.js",
	"./en-nz.js": "./node_modules/moment/dist/locale/en-nz.js",
	"./en-sg": "./node_modules/moment/dist/locale/en-sg.js",
	"./en-sg.js": "./node_modules/moment/dist/locale/en-sg.js",
	"./eo": "./node_modules/moment/dist/locale/eo.js",
	"./eo.js": "./node_modules/moment/dist/locale/eo.js",
	"./es": "./node_modules/moment/dist/locale/es.js",
	"./es-do": "./node_modules/moment/dist/locale/es-do.js",
	"./es-do.js": "./node_modules/moment/dist/locale/es-do.js",
	"./es-mx": "./node_modules/moment/dist/locale/es-mx.js",
	"./es-mx.js": "./node_modules/moment/dist/locale/es-mx.js",
	"./es-us": "./node_modules/moment/dist/locale/es-us.js",
	"./es-us.js": "./node_modules/moment/dist/locale/es-us.js",
	"./es.js": "./node_modules/moment/dist/locale/es.js",
	"./et": "./node_modules/moment/dist/locale/et.js",
	"./et.js": "./node_modules/moment/dist/locale/et.js",
	"./eu": "./node_modules/moment/dist/locale/eu.js",
	"./eu.js": "./node_modules/moment/dist/locale/eu.js",
	"./fa": "./node_modules/moment/dist/locale/fa.js",
	"./fa.js": "./node_modules/moment/dist/locale/fa.js",
	"./fi": "./node_modules/moment/dist/locale/fi.js",
	"./fi.js": "./node_modules/moment/dist/locale/fi.js",
	"./fil": "./node_modules/moment/dist/locale/fil.js",
	"./fil.js": "./node_modules/moment/dist/locale/fil.js",
	"./fo": "./node_modules/moment/dist/locale/fo.js",
	"./fo.js": "./node_modules/moment/dist/locale/fo.js",
	"./fr": "./node_modules/moment/dist/locale/fr.js",
	"./fr-ca": "./node_modules/moment/dist/locale/fr-ca.js",
	"./fr-ca.js": "./node_modules/moment/dist/locale/fr-ca.js",
	"./fr-ch": "./node_modules/moment/dist/locale/fr-ch.js",
	"./fr-ch.js": "./node_modules/moment/dist/locale/fr-ch.js",
	"./fr.js": "./node_modules/moment/dist/locale/fr.js",
	"./fy": "./node_modules/moment/dist/locale/fy.js",
	"./fy.js": "./node_modules/moment/dist/locale/fy.js",
	"./ga": "./node_modules/moment/dist/locale/ga.js",
	"./ga.js": "./node_modules/moment/dist/locale/ga.js",
	"./gd": "./node_modules/moment/dist/locale/gd.js",
	"./gd.js": "./node_modules/moment/dist/locale/gd.js",
	"./gl": "./node_modules/moment/dist/locale/gl.js",
	"./gl.js": "./node_modules/moment/dist/locale/gl.js",
	"./gom-deva": "./node_modules/moment/dist/locale/gom-deva.js",
	"./gom-deva.js": "./node_modules/moment/dist/locale/gom-deva.js",
	"./gom-latn": "./node_modules/moment/dist/locale/gom-latn.js",
	"./gom-latn.js": "./node_modules/moment/dist/locale/gom-latn.js",
	"./gu": "./node_modules/moment/dist/locale/gu.js",
	"./gu.js": "./node_modules/moment/dist/locale/gu.js",
	"./he": "./node_modules/moment/dist/locale/he.js",
	"./he.js": "./node_modules/moment/dist/locale/he.js",
	"./hi": "./node_modules/moment/dist/locale/hi.js",
	"./hi.js": "./node_modules/moment/dist/locale/hi.js",
	"./hr": "./node_modules/moment/dist/locale/hr.js",
	"./hr.js": "./node_modules/moment/dist/locale/hr.js",
	"./hu": "./node_modules/moment/dist/locale/hu.js",
	"./hu.js": "./node_modules/moment/dist/locale/hu.js",
	"./hy-am": "./node_modules/moment/dist/locale/hy-am.js",
	"./hy-am.js": "./node_modules/moment/dist/locale/hy-am.js",
	"./id": "./node_modules/moment/dist/locale/id.js",
	"./id.js": "./node_modules/moment/dist/locale/id.js",
	"./is": "./node_modules/moment/dist/locale/is.js",
	"./is.js": "./node_modules/moment/dist/locale/is.js",
	"./it": "./node_modules/moment/dist/locale/it.js",
	"./it-ch": "./node_modules/moment/dist/locale/it-ch.js",
	"./it-ch.js": "./node_modules/moment/dist/locale/it-ch.js",
	"./it.js": "./node_modules/moment/dist/locale/it.js",
	"./ja": "./node_modules/moment/dist/locale/ja.js",
	"./ja.js": "./node_modules/moment/dist/locale/ja.js",
	"./jv": "./node_modules/moment/dist/locale/jv.js",
	"./jv.js": "./node_modules/moment/dist/locale/jv.js",
	"./ka": "./node_modules/moment/dist/locale/ka.js",
	"./ka.js": "./node_modules/moment/dist/locale/ka.js",
	"./kk": "./node_modules/moment/dist/locale/kk.js",
	"./kk.js": "./node_modules/moment/dist/locale/kk.js",
	"./km": "./node_modules/moment/dist/locale/km.js",
	"./km.js": "./node_modules/moment/dist/locale/km.js",
	"./kn": "./node_modules/moment/dist/locale/kn.js",
	"./kn.js": "./node_modules/moment/dist/locale/kn.js",
	"./ko": "./node_modules/moment/dist/locale/ko.js",
	"./ko.js": "./node_modules/moment/dist/locale/ko.js",
	"./ku": "./node_modules/moment/dist/locale/ku.js",
	"./ku-kmr": "./node_modules/moment/dist/locale/ku-kmr.js",
	"./ku-kmr.js": "./node_modules/moment/dist/locale/ku-kmr.js",
	"./ku.js": "./node_modules/moment/dist/locale/ku.js",
	"./ky": "./node_modules/moment/dist/locale/ky.js",
	"./ky.js": "./node_modules/moment/dist/locale/ky.js",
	"./lb": "./node_modules/moment/dist/locale/lb.js",
	"./lb.js": "./node_modules/moment/dist/locale/lb.js",
	"./lo": "./node_modules/moment/dist/locale/lo.js",
	"./lo.js": "./node_modules/moment/dist/locale/lo.js",
	"./lt": "./node_modules/moment/dist/locale/lt.js",
	"./lt.js": "./node_modules/moment/dist/locale/lt.js",
	"./lv": "./node_modules/moment/dist/locale/lv.js",
	"./lv.js": "./node_modules/moment/dist/locale/lv.js",
	"./me": "./node_modules/moment/dist/locale/me.js",
	"./me.js": "./node_modules/moment/dist/locale/me.js",
	"./mi": "./node_modules/moment/dist/locale/mi.js",
	"./mi.js": "./node_modules/moment/dist/locale/mi.js",
	"./mk": "./node_modules/moment/dist/locale/mk.js",
	"./mk.js": "./node_modules/moment/dist/locale/mk.js",
	"./ml": "./node_modules/moment/dist/locale/ml.js",
	"./ml.js": "./node_modules/moment/dist/locale/ml.js",
	"./mn": "./node_modules/moment/dist/locale/mn.js",
	"./mn.js": "./node_modules/moment/dist/locale/mn.js",
	"./mr": "./node_modules/moment/dist/locale/mr.js",
	"./mr.js": "./node_modules/moment/dist/locale/mr.js",
	"./ms": "./node_modules/moment/dist/locale/ms.js",
	"./ms-my": "./node_modules/moment/dist/locale/ms-my.js",
	"./ms-my.js": "./node_modules/moment/dist/locale/ms-my.js",
	"./ms.js": "./node_modules/moment/dist/locale/ms.js",
	"./mt": "./node_modules/moment/dist/locale/mt.js",
	"./mt.js": "./node_modules/moment/dist/locale/mt.js",
	"./my": "./node_modules/moment/dist/locale/my.js",
	"./my.js": "./node_modules/moment/dist/locale/my.js",
	"./nb": "./node_modules/moment/dist/locale/nb.js",
	"./nb.js": "./node_modules/moment/dist/locale/nb.js",
	"./ne": "./node_modules/moment/dist/locale/ne.js",
	"./ne.js": "./node_modules/moment/dist/locale/ne.js",
	"./nl": "./node_modules/moment/dist/locale/nl.js",
	"./nl-be": "./node_modules/moment/dist/locale/nl-be.js",
	"./nl-be.js": "./node_modules/moment/dist/locale/nl-be.js",
	"./nl.js": "./node_modules/moment/dist/locale/nl.js",
	"./nn": "./node_modules/moment/dist/locale/nn.js",
	"./nn.js": "./node_modules/moment/dist/locale/nn.js",
	"./oc-lnc": "./node_modules/moment/dist/locale/oc-lnc.js",
	"./oc-lnc.js": "./node_modules/moment/dist/locale/oc-lnc.js",
	"./pa-in": "./node_modules/moment/dist/locale/pa-in.js",
	"./pa-in.js": "./node_modules/moment/dist/locale/pa-in.js",
	"./pl": "./node_modules/moment/dist/locale/pl.js",
	"./pl.js": "./node_modules/moment/dist/locale/pl.js",
	"./pt": "./node_modules/moment/dist/locale/pt.js",
	"./pt-br": "./node_modules/moment/dist/locale/pt-br.js",
	"./pt-br.js": "./node_modules/moment/dist/locale/pt-br.js",
	"./pt.js": "./node_modules/moment/dist/locale/pt.js",
	"./ro": "./node_modules/moment/dist/locale/ro.js",
	"./ro.js": "./node_modules/moment/dist/locale/ro.js",
	"./ru": "./node_modules/moment/dist/locale/ru.js",
	"./ru.js": "./node_modules/moment/dist/locale/ru.js",
	"./sd": "./node_modules/moment/dist/locale/sd.js",
	"./sd.js": "./node_modules/moment/dist/locale/sd.js",
	"./se": "./node_modules/moment/dist/locale/se.js",
	"./se.js": "./node_modules/moment/dist/locale/se.js",
	"./si": "./node_modules/moment/dist/locale/si.js",
	"./si.js": "./node_modules/moment/dist/locale/si.js",
	"./sk": "./node_modules/moment/dist/locale/sk.js",
	"./sk.js": "./node_modules/moment/dist/locale/sk.js",
	"./sl": "./node_modules/moment/dist/locale/sl.js",
	"./sl.js": "./node_modules/moment/dist/locale/sl.js",
	"./sq": "./node_modules/moment/dist/locale/sq.js",
	"./sq.js": "./node_modules/moment/dist/locale/sq.js",
	"./sr": "./node_modules/moment/dist/locale/sr.js",
	"./sr-cyrl": "./node_modules/moment/dist/locale/sr-cyrl.js",
	"./sr-cyrl.js": "./node_modules/moment/dist/locale/sr-cyrl.js",
	"./sr.js": "./node_modules/moment/dist/locale/sr.js",
	"./ss": "./node_modules/moment/dist/locale/ss.js",
	"./ss.js": "./node_modules/moment/dist/locale/ss.js",
	"./sv": "./node_modules/moment/dist/locale/sv.js",
	"./sv.js": "./node_modules/moment/dist/locale/sv.js",
	"./sw": "./node_modules/moment/dist/locale/sw.js",
	"./sw.js": "./node_modules/moment/dist/locale/sw.js",
	"./ta": "./node_modules/moment/dist/locale/ta.js",
	"./ta.js": "./node_modules/moment/dist/locale/ta.js",
	"./te": "./node_modules/moment/dist/locale/te.js",
	"./te.js": "./node_modules/moment/dist/locale/te.js",
	"./tet": "./node_modules/moment/dist/locale/tet.js",
	"./tet.js": "./node_modules/moment/dist/locale/tet.js",
	"./tg": "./node_modules/moment/dist/locale/tg.js",
	"./tg.js": "./node_modules/moment/dist/locale/tg.js",
	"./th": "./node_modules/moment/dist/locale/th.js",
	"./th.js": "./node_modules/moment/dist/locale/th.js",
	"./tk": "./node_modules/moment/dist/locale/tk.js",
	"./tk.js": "./node_modules/moment/dist/locale/tk.js",
	"./tl-ph": "./node_modules/moment/dist/locale/tl-ph.js",
	"./tl-ph.js": "./node_modules/moment/dist/locale/tl-ph.js",
	"./tlh": "./node_modules/moment/dist/locale/tlh.js",
	"./tlh.js": "./node_modules/moment/dist/locale/tlh.js",
	"./tr": "./node_modules/moment/dist/locale/tr.js",
	"./tr.js": "./node_modules/moment/dist/locale/tr.js",
	"./tzl": "./node_modules/moment/dist/locale/tzl.js",
	"./tzl.js": "./node_modules/moment/dist/locale/tzl.js",
	"./tzm": "./node_modules/moment/dist/locale/tzm.js",
	"./tzm-latn": "./node_modules/moment/dist/locale/tzm-latn.js",
	"./tzm-latn.js": "./node_modules/moment/dist/locale/tzm-latn.js",
	"./tzm.js": "./node_modules/moment/dist/locale/tzm.js",
	"./ug-cn": "./node_modules/moment/dist/locale/ug-cn.js",
	"./ug-cn.js": "./node_modules/moment/dist/locale/ug-cn.js",
	"./uk": "./node_modules/moment/dist/locale/uk.js",
	"./uk.js": "./node_modules/moment/dist/locale/uk.js",
	"./ur": "./node_modules/moment/dist/locale/ur.js",
	"./ur.js": "./node_modules/moment/dist/locale/ur.js",
	"./uz": "./node_modules/moment/dist/locale/uz.js",
	"./uz-latn": "./node_modules/moment/dist/locale/uz-latn.js",
	"./uz-latn.js": "./node_modules/moment/dist/locale/uz-latn.js",
	"./uz.js": "./node_modules/moment/dist/locale/uz.js",
	"./vi": "./node_modules/moment/dist/locale/vi.js",
	"./vi.js": "./node_modules/moment/dist/locale/vi.js",
	"./x-pseudo": "./node_modules/moment/dist/locale/x-pseudo.js",
	"./x-pseudo.js": "./node_modules/moment/dist/locale/x-pseudo.js",
	"./yo": "./node_modules/moment/dist/locale/yo.js",
	"./yo.js": "./node_modules/moment/dist/locale/yo.js",
	"./zh-cn": "./node_modules/moment/dist/locale/zh-cn.js",
	"./zh-cn.js": "./node_modules/moment/dist/locale/zh-cn.js",
	"./zh-hk": "./node_modules/moment/dist/locale/zh-hk.js",
	"./zh-hk.js": "./node_modules/moment/dist/locale/zh-hk.js",
	"./zh-mo": "./node_modules/moment/dist/locale/zh-mo.js",
	"./zh-mo.js": "./node_modules/moment/dist/locale/zh-mo.js",
	"./zh-tw": "./node_modules/moment/dist/locale/zh-tw.js",
	"./zh-tw.js": "./node_modules/moment/dist/locale/zh-tw.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "./node_modules/moment/dist/locale sync recursive ^\\.\\/.*$";

/***/ }),

/***/ "./src/components/brand/index.scss":
/*!*****************************************!*\
  !*** ./src/components/brand/index.scss ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/components/brand/item.jsx":
/*!***************************************!*\
  !*** ./src/components/brand/item.jsx ***!
  \***************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.scss */ "./src/components/brand/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-04-27 12:19:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/brand/item.jsx
 */





var BrandItem = function BrandItem(props) {
  var data = props.data;
  var css = props.className ? props.className : 'noraml';
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
    className: "brandIndex ".concat(css),
    onClick: function onClick() {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.navigateTo({
        url: "/pages/brand/index?v=".concat(data.code)
      });
    },
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "top_tags",
      style: {
        backgroundColor: "".concat(data.is_open_config.is_open ? '#ED8502' : '#bdbdbd')
      },
      children: data.is_open_config.is_open_tips.replace('|', '')
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "status open",
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Text"], {
          children: "\u8DDD\u6211"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
          className: "num",
          children: data.km
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Text"], {
          children: "\u516C\u91CC"
        })]
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      style: {
        position: 'relative'
      },
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Image"], {
        src: data.image,
        className: props.cname,
        mode: "widthFix"
      }), data.brand_note !== 'none' && data.brand_note.length > 4 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        style: {
          position: 'absolute',
          bottom: '5px',
          background: '#0000007a',
          padding: '5px 0',
          color: '#fff',
          fontSize: '14px',
          width: '100%',
          zIndex: 1,
          textIndent: '5px'
        },
        children: data.brand_note
      }) : null]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "title",
      children: data.brand_name
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "info",
      children: [data.tips, data.address]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        className: "tips_one",
        style: {
          backgroundColor: data.is_book === 0 ? '#FF5722' : '#8BC34A'
        },
        children: data.is_book_str
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        className: "tips_two",
        children: data.user_tips
      }), data.today_num > -1 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        className: "tips_one",
        style: {
          backgroundColor: "#ff5722",
          width: '70px',
          padding: '4px 0'
        },
        children: data.today_num_str
      }) : null]
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (BrandItem);

/***/ }),

/***/ "./src/components/brand/itembuy.jsx":
/*!******************************************!*\
  !*** ./src/components/brand/itembuy.jsx ***!
  \******************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.scss */ "./src/components/brand/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-07-23 00:02:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/brand/itembuy.jsx
 */






var BrandBuyItem = function BrandBuyItem(props) {
  var data = props.data;
  var css = props.className ? props.className : 'noraml';
  return data.good_info !== null && data.id !== "817" ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
    className: "brandIndex ".concat(css),
    onClick: function onClick() {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.navigateTo({
        url: "/pages/brand/buy?v=".concat(data.id)
      });
    },
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "top_tags",
      style: {
        backgroundColor: '#ED8502'
      },
      children: ["\u6301\u5E74\u7968\u7ACB\u51CF", /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Text"], {
        style: {
          fontWeight: 'bold',
          fontSize: '14px',
          color: '#FFEB3B'
        },
        children: [" ", parseInt(parseFloat(data.good_info.show_price) - parseFloat(data.good_info.share_price), 10), " "]
      }), "\u5143"]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "status open",
      style: {
        backgroundColor: '#FF9800'
      },
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Text"], {
          children: "\u4EC5\u552E"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
          className: "num",
          children: _utils_tools__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].getPrice(data.good_info.share_price)
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Text"], {
          children: "\u5143"
        })]
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      style: {
        position: 'relative',
        background: "url(".concat(data.logo, "?x-oss-process=image/blur,r_30,s_30)"),
        textAlign: 'center',
        backgroundSize: '100% 100%',
        borderTopLeftRadius: '15px',
        borderTopRightRadius: '8px'
      },
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Image"], {
        src: "".concat(data.logo),
        className: props.cname,
        mode: "heightFix"
      }), data.website_url !== '' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        style: {
          position: 'absolute',
          bottom: '0px',
          background: '#0000007a',
          padding: '5px 0',
          color: '#fff',
          fontSize: '12px',
          width: '100%',
          zIndex: 1,
          textIndent: '5px',
          textAlign: 'left'
        },
        children: data.website_url
      }) : null]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "title",
      children: data.name
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "info",
      children: [data.province, data.city, data.address, " \u8DDD", data.km, "\u516C\u91CC"]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      style: {
        paddingLeft: '5px'
      },
      children: data.good_info.day.map(function (v, index) {
        return index <= 3 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
          className: "tips_three",
          children: v
        }) : null;
      })
    })]
  }) : null;
};
/* harmony default export */ __webpack_exports__["a"] = (BrandBuyItem);

/***/ }),

/***/ "./src/components/card/index.jsx":
/*!***************************************!*\
  !*** ./src/components/card/index.jsx ***!
  \***************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ "./node_modules/moment/dist/moment.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.scss */ "./src/components/card/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-17 17:41:14
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/card/index.jsx
 */






var Card = function Card(props) {
  var data = props.data,
    oper = props.oper;
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
    className: "cardlayout",
    style: {
      position: 'relative'
    },
    onClick: function onClick() {
      if (data.enable) {
        if (Boolean(oper)) {
          oper(data.good_id, data.real_name);
        } else {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.navigateTo({
            url: "/pages/qr/index?code=".concat(data.good_id)
          });
        }
      }
    },
    children: [data.enable === false ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      style: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        backgroundColor: '#777272de',
        top: 0,
        left: 0,
        borderRadius: '7px',
        textAlign: 'center',
        lineHeight: '100px',
        fontWeight: 'bold'
      },
      children: data.enable_str
    }) : null, /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "at-row",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        className: "at-col at-col-4 number",
        style: {
          textAlign: 'left'
        }
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        className: "at-col at-col-8 number",
        style: {
          textAlign: 'right'
        },
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Text"], {
          style: {
            paddingRight: '5px'
          },
          children: data.real_name
        }), " No.", data.good_id]
      })]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "at-row",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        className: "at-col at-col-2",
        style: {
          textAlign: 'right'
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Image"], {
          src: data.user_img,
          className: "logo",
          mode: "aspectFill"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        className: "at-col at-col-9",
        style: {
          marginLeft: "10px"
        },
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
          className: "title",
          children: data.card_name
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
          className: "date",
          children: ["\u6709\u6548\u671F: ", Object(moment__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(data.enable_start_time_unix * 1000).format("YYYY年MM月DD日"), " \u81F3 ", Object(moment__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(data.enable_end_time_unix * 1000).format("YYYY年MM月DD日")]
        })]
      })]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      className: "bottom"
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (Card);

/***/ }),

/***/ "./src/components/card/index.scss":
/*!****************************************!*\
  !*** ./src/components/card/index.scss ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/components/card/list.jsx":
/*!**************************************!*\
  !*** ./src/components/card/list.jsx ***!
  \**************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var taro_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! taro-skeleton */ "./node_modules/taro-skeleton/dist/index.umd.js");
/* harmony import */ var taro_skeleton__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(taro_skeleton__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./index */ "./src/components/card/index.jsx");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./index.scss */ "./src/components/card/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__);



/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-24 11:16:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/card/list.jsx
 */










var CardList = function CardList(props) {
  var max = Boolean(props.max) ? props.max : 0;
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])([]),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    init = _useState2[0],
    setInit = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var myCard;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showLoading({
            mask: true,
            title: '读取中'
          });
          _context.next = 3;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getCardList();
        case 3:
          myCard = _context.sent;
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
          setInit(myCard.card_list);
          setLoading(false);
        case 7:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), []);
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
    children: [init.map(function (val, index) {
      return index < max || max === 0 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__["jsx"])(_index__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], {
        data: val
      }) : null;
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
      className: "bindBtn",
      onClick: function onClick() {
        if (_tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getEnv() === _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.ENV_TYPE.ALIPAY) {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.navigateTo({
            url: '/pages/bind/index'
          });
        } else {
          _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getUserInfo(function () {
            if (props.callback) props.callback();
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.navigateTo({
              url: '/pages/bind/index'
            });
          });
        }
      },
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_6__[/* AtIcon */ "f"], {
        value: "add-circle",
        size: "20",
        color: "#fff"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["Text"], {
        style: {
          paddingLeft: '5px',
          paddingTop: '5px'
        },
        children: "\u6FC0\u6D3B\u5E74\u7968"
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (CardList);

/***/ }),

/***/ "./src/components/memu/index.jsx":
/*!***************************************!*\
  !*** ./src/components/memu/index.jsx ***!
  \***************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__);


/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-08-03 18:00:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/memu/index.jsx
 */







var Memu = function Memu(props) {
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
      style: {
        height: '100px'
      }
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
      className: "memu",
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_5__[/* AtTabBar */ "r"], {
        tabList: [{
          title: '首页',
          image: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].picUrl, "/memu/index.png"),
          selectedImage: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].picUrl, "/memu/index_s.png")
        }, {
          title: '地图',
          image: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].picUrl, "/memu/map.png"),
          selectedImage: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].picUrl, "/memu/map_s.png")
        }, {
          title: '景区',
          image: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].picUrl, "/memu/brand.png"),
          selectedImage: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].picUrl, "/memu/brand_s.png")
        }, {
          title: '我的',
          image: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].picUrl, "/memu/mem.png"),
          selectedImage: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].picUrl, "/memu/mem_s.png")
        }],
        onClick: ( /*#__PURE__*/function () {
          var _ref = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee(value) {
            return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.t0 = value;
                  _context.next = _context.t0 === 0 ? 3 : _context.t0 === 1 ? 5 : _context.t0 === 2 ? 7 : _context.t0 === 3 ? 9 : 11;
                  break;
                case 3:
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.redirectTo({
                    url: '/pages/index/index'
                  });
                  return _context.abrupt("break", 12);
                case 5:
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.redirectTo({
                    url: '/pages/map/index'
                  });
                  return _context.abrupt("break", 12);
                case 7:
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.redirectTo({
                    url: '/pages/sreach/list'
                  });
                  return _context.abrupt("break", 12);
                case 9:
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.redirectTo({
                    url: '/pages/my/index'
                  });
                  return _context.abrupt("break", 12);
                case 11:
                  return _context.abrupt("break", 12);
                case 12:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }));
          return function (_x) {
            return _ref.apply(this, arguments);
          };
        }()),
        current: props.now
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (Memu);

/***/ }),

/***/ "./src/components/my/index.scss":
/*!**************************************!*\
  !*** ./src/components/my/index.scss ***!
  \**************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/components/tips/index.scss":
/*!****************************************!*\
  !*** ./src/components/tips/index.scss ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/components/tips/none.jsx":
/*!**************************************!*\
  !*** ./src/components/tips/none.jsx ***!
  \**************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.scss */ "./src/components/tips/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);




var None = function None(props) {
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
    children: props.loaded ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        className: "clear_v",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Image"], {
          src: "https://test.qqyhmmwg.com/res/wbg/user/clear.png",
          className: "clear",
          mode: "widthFix"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
        style: {
          textAlign: 'center',
          color: '#c1c1c1'
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Text"], {
          children: "\u54C7\uFF5E\u8FD9\u91CC\u7A7A\u7A7A\u5982\u4E5F\uFF5E"
        })
      })]
    }) : null
  });
};
/* harmony default export */ __webpack_exports__["a"] = (None);

/***/ }),

/***/ "./src/components/tool/bg.jsx":
/*!************************************!*\
  !*** ./src/components/tool/bg.jsx ***!
  \************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__);


var Bg = function Bg(props) {
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["View"], {
    style: {
      height: '100%',
      width: '100%',
      position: 'fixed',
      top: 0,
      backgroundColor: props.color ? props.color : '#fff',
      zIndex: -1
    }
  });
};
/* harmony default export */ __webpack_exports__["a"] = (Bg);

/***/ }),

/***/ "./src/pages/bind/index.scss":
/*!***********************************!*\
  !*** ./src/pages/bind/index.scss ***!
  \***********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/pages/brand/index.scss":
/*!************************************!*\
  !*** ./src/pages/brand/index.scss ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/pages/buy/index.scss":
/*!**********************************!*\
  !*** ./src/pages/buy/index.scss ***!
  \**********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/pages/my/index.scss":
/*!*********************************!*\
  !*** ./src/pages/my/index.scss ***!
  \*********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/pages/news/index.scss":
/*!***********************************!*\
  !*** ./src/pages/news/index.scss ***!
  \***********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/pages/sreach/bar.jsx":
/*!**********************************!*\
  !*** ./src/pages/sreach/bar.jsx ***!
  \**********************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _tags__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./tags */ "./src/pages/sreach/tags/index.jsx");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./index.scss */ "./src/pages/sreach/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__);












var SreachTools = function SreachTools(props) {
  var config = props.config;
  var query = _utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].getQuery();
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(null),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    toolVal = _useState2[0],
    setToolVal = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(props.init ? props.init : undefined),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState3, 2),
    word = _useState4[0],
    setWord = _useState4[1];
  var callBack = function callBack() {
    var result = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({}, toolVal), {}, {
      show: false,
      active: null
    });
    setToolVal(result);
    var setData = function setData() {
      var data = {};
      Object.keys(result.data).forEach(function (v) {
        if (result.data[v].length > 0) {
          data[v] = result.data[v].length === 1 && v === 'order' ? result.data[v][0] : result.data[v];
        }
      });
      return data;
    };
    props.callBack(setData());
  };
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])(function () {
    var init = {
      show: false,
      active: null,
      data: {}
    };
    if (Boolean(config)) {
      config.forEach(function (val) {
        init.data[val.name] = Boolean(val === null || val === void 0 ? void 0 : val.default) ? val === null || val === void 0 ? void 0 : val.default : [];
      });
    }
    setToolVal(init);
  }, []);
  var setData = function setData(name, val, type, data, multiple) {
    if (Array.isArray(data.data[name])) {
      if (val === null) {
        data.data[name] = [];
      } else {
        if (multiple) {
          if (type && !data.data[name].includes(val)) {
            data.data[name].push(val);
          } else {
            var index = data.data[name].indexOf(val);
            if (index > -1) data.data[name].splice(index, 1);
          }
        } else {
          data.data[name] = [val];
        }
      }
      setToolVal(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({}, data));
    }
  };
  if (toolVal === null) {
    return null;
  }
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
    className: "sreach_tools ".concat(config.length === 0 ? 'h_only' : 'h_all'),
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
      className: "sreach_content",
      children: [props.top !== 'hide' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_5__[/* AtSearchBar */ "q"], {
        placeholder: '请输入景区名称/地区/特色',
        value: word,
        onChange: function onChange(v) {
          setWord(v);
        },
        onClear: function onClear() {
          if (Boolean(props.setkey)) {
            props.setkey(undefined);
            setWord(undefined);
          }
        },
        onActionClick: function onActionClick() {
          var nowKey = _utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].getDataSafe('keyWords');
          if (nowKey === null) {
            _utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].setData('keyWords', [word]);
          } else {
            if (nowKey.length <= 10) {
              nowKey.unshift(word);
              _utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].setData('keyWords', Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(new Set(nowKey)));
            }
          }
          if (Boolean(props.setkey)) {
            props.setkey(word);
          } else {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_6___default.a.navigateTo({
              url: "/pages/sreach/list?v=".concat(word)
            });
          }
        }
      }) : null, /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
        className: "at-row sreach_sel",
        style: {
          display: config.length === 0 ? 'none' : 'flex'
        },
        children: config.map(function (val) {
          return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
            className: "at-col md ".concat((toolVal.active === val.name || toolVal.data[val.name].length > 0) && val.type !== 'type' ? 'selected' : 'init'),
            onClick: function onClick() {
              var active = toolVal.active;
              if (val.name === active) {
                setToolVal(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({}, toolVal), {}, {
                  active: null,
                  show: false
                }));
              } else {
                setToolVal(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({}, toolVal), {}, {
                  show: true,
                  active: val.name
                }));
              }
            },
            children: ["".concat(val.title).concat(toolVal.data[val.name].length > 0 && val.type !== 'type' && val.multiple ? " [".concat(toolVal.data[val.name].length, "]") : ''), " ", /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_5__[/* AtIcon */ "f"], {
              value: "chevron-".concat(toolVal.active === val.name ? 'down' : 'up'),
              size: "15",
              color: toolVal.active === val.name || toolVal.data[val.name].length > 0 ? "#6190E8" : '#666'
            })]
          });
        })
      }), config.map(function (val) {
        switch (val.type) {
          case 'tags':
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
              className: "sel_area ".concat(toolVal.active === val.name ? 'show' : 'hide'),
              children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                className: "sel_item",
                style: {
                  paddingTop: '15px'
                },
                children: val.list.map(function (item) {
                  if (Boolean(item.chlidren)) {
                    return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                      style: {
                        marginBottom: '20px'
                      },
                      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                        style: {
                          paddingLeft: '15px',
                          color: '#8c8686',
                          fontWeight: 'bold'
                        },
                        children: item.title
                      }), item.chlidren.map(function (cc) {
                        return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tags__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], {
                          val: {
                            name: val.name,
                            val: cc.val,
                            multiple: val.multiple
                          },
                          data: toolVal,
                          onClick: function onClick(v) {
                            setData(val.name, cc.val, v, toolVal, val.multiple);
                            if (val.multiple === false) {
                              callBack();
                            }
                          },
                          children: cc.title
                        });
                      })]
                    });
                  } else {
                    return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tags__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], {
                      val: {
                        name: val.name,
                        val: item.val,
                        multiple: val.multiple
                      },
                      data: toolVal,
                      onClick: function onClick(v) {
                        setData(val.name, item.val, v, toolVal, val.multiple);
                        if (val.multiple === false) {
                          callBack();
                        }
                      },
                      children: item.title
                    });
                  }
                })
              }), val.multiple ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                className: "at-row sel_btn",
                children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                  className: "at-col at-col-1"
                }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                  className: "at-col at-col-4",
                  children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_5__[/* AtButton */ "a"], {
                    onClick: function onClick() {
                      setData(val.name, null, false, toolVal);
                      callBack();
                    },
                    children: "\u4E0D\u9650\u6761\u4EF6"
                  })
                }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                  className: "at-col at-col-1"
                }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                  className: "at-col at-col-5",
                  children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_5__[/* AtButton */ "a"], {
                    type: "primary",
                    onClick: function onClick() {
                      return callBack();
                    },
                    children: "\u786E \u8BA4"
                  })
                }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                  className: "at-col at-col-1"
                })]
              }) : null]
            });
          case 'list':
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
              className: "sel_list ".concat(toolVal.active === val.name ? 'show' : 'hide'),
              children: val.list.map(function (item, index) {
                return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
                  className: "sel_list_item ".concat(toolVal.data[val.name].includes(item.val) || toolVal.data[val.name].length === 0 && index === 0 ? 'active' : ''),
                  onClick: function onClick() {
                    var data = toolVal;
                    data.data[val.name] = [item.val];
                    setToolVal(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({}, data));
                    callBack();
                  },
                  children: item.title
                });
              })
            });
          default:
            return null;
        }
      })]
    }), toolVal.show ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
      className: "mask",
      onClick: function onClick() {
        setToolVal(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({}, toolVal), {}, {
          show: false,
          active: ''
        }));
      }
    }) : null]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (SreachTools);

/***/ }),

/***/ "./src/pages/sreach/index.scss":
/*!*************************************!*\
  !*** ./src/pages/sreach/index.scss ***!
  \*************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/pages/sreach/tags/index.jsx":
/*!*****************************************!*\
  !*** ./src/pages/sreach/tags/index.jsx ***!
  \*****************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.scss */ "./src/pages/sreach/tags/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);

/*
 * @Author: 高超
 * @Date: 2021-11-27 23:20:42
 * @LastEditTime: 2021-11-28 02:23:16
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/tags/index.jsx
 * love jiajia
 */





var Tags = function Tags(props) {
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])(),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState, 2),
    active = _useState2[0],
    setActive = _useState2[1];
  Object(react__WEBPACK_IMPORTED_MODULE_1__["useEffect"])(function () {
    setActive(props.data.data[props.val.name].includes(props.val.val));
  }, [props.data]);
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["Text"], {
    className: "tags ".concat(active ? 'active' : ''),
    onClick: function onClick() {
      props.onClick(active ? false : true);
      setActive(!active);
      //  if (props.val.multiple){
      //   props.callBack()
      // }
    },
    children: props.children
  }, _utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].createId(5));
};
/* harmony default export */ __webpack_exports__["a"] = (Tags);

/***/ }),

/***/ "./src/pages/sreach/tags/index.scss":
/*!******************************************!*\
  !*** ./src/pages/sreach/tags/index.scss ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/utils/api.config.json":
/*!***********************************!*\
  !*** ./src/utils/api.config.json ***!
  \***********************************/
/*! exports provided: baseUrl, zhbaseUrl, zhnodeUrl, default */
/*! exports used: default */
/***/ (function(module) {

module.exports = JSON.parse("{\"baseUrl\":\"https://test.qqyhmmwg.com/weapp/\",\"zhbaseUrl\":\"https://zhlx.dfxpw.cn/dfx/index.php/api/\",\"zhnodeUrl\":\"https://vlog.dfxpw.cn/nick/\"}");

/***/ }),

/***/ "./src/utils/api.js":
/*!**************************!*\
  !*** ./src/utils/api.js ***!
  \**************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _utils_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/request */ "./src/utils/request.js");
/* harmony import */ var _api_config_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./api.config.json */ "./src/utils/api.config.json");
var _api_config_json__WEBPACK_IMPORTED_MODULE_3___namespace = /*#__PURE__*/__webpack_require__.t(/*! ./api.config.json */ "./src/utils/api.config.json", 1);


/*
 * @Author: 高超
 * @Date: 2021-10-30 11:39:41
 * @LastEditTime: 2022-07-22 10:02:24
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/utils/api.js
 * love jiajia
 */


/* harmony default export */ __webpack_exports__["a"] = ({
  baseUrl: _api_config_json__WEBPACK_IMPORTED_MODULE_3__.baseUrl,
  zhbaseUrl: _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhbaseUrl,
  zhnodeUrl: _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhnodeUrl,
  getUserCode: function () {
    var _getUserCode = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'codeToInfo',
              method: 'GET',
              data: data
            });
          case 2:
            return _context.abrupt("return", _context.sent);
          case 3:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    function getUserCode(_x) {
      return _getUserCode.apply(this, arguments);
    }
    return getUserCode;
  }(),
  getAlipayUserCode: function () {
    var _getAlipayUserCode = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee2(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'alipayCodeToInfo',
              method: 'GET',
              data: data
            });
          case 2:
            return _context2.abrupt("return", _context2.sent);
          case 3:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    function getAlipayUserCode(_x2) {
      return _getAlipayUserCode.apply(this, arguments);
    }
    return getAlipayUserCode;
  }(),
  regZhMem: function () {
    var _regZhMem = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee3(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'regFromOther',
              method: 'POST',
              data: data
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhnodeUrl);
          case 2:
            return _context3.abrupt("return", _context3.sent);
          case 3:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    function regZhMem(_x3) {
      return _regZhMem.apply(this, arguments);
    }
    return regZhMem;
  }(),
  myCardList: function () {
    var _myCardList = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee4(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee4$(_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            _context4.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'userCardList',
              method: 'POST',
              data: data
            });
          case 2:
            return _context4.abrupt("return", _context4.sent);
          case 3:
          case "end":
            return _context4.stop();
        }
      }, _callee4);
    }));
    function myCardList(_x4) {
      return _myCardList.apply(this, arguments);
    }
    return myCardList;
  }(),
  myBookList: function () {
    var _myBookList = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee5(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee5$(_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            _context5.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'myBookList',
              method: 'POST',
              data: data
            });
          case 2:
            return _context5.abrupt("return", _context5.sent);
          case 3:
          case "end":
            return _context5.stop();
        }
      }, _callee5);
    }));
    function myBookList(_x5) {
      return _myBookList.apply(this, arguments);
    }
    return myBookList;
  }(),
  index: function () {
    var _index = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee6(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee6$(_context6) {
        while (1) switch (_context6.prev = _context6.next) {
          case 0:
            _context6.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'brandListIndex',
              method: 'POST',
              data: data
            });
          case 2:
            return _context6.abrupt("return", _context6.sent);
          case 3:
          case "end":
            return _context6.stop();
        }
      }, _callee6);
    }));
    function index(_x6) {
      return _index.apply(this, arguments);
    }
    return index;
  }(),
  userReg: function () {
    var _userReg = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee7(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee7$(_context7) {
        while (1) switch (_context7.prev = _context7.next) {
          case 0:
            _context7.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'userUpdate',
              method: 'POST',
              data: data
            });
          case 2:
            return _context7.abrupt("return", _context7.sent);
          case 3:
          case "end":
            return _context7.stop();
        }
      }, _callee7);
    }));
    function userReg(_x7) {
      return _userReg.apply(this, arguments);
    }
    return userReg;
  }(),
  alipayUserReg: function () {
    var _alipayUserReg = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee8(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee8$(_context8) {
        while (1) switch (_context8.prev = _context8.next) {
          case 0:
            _context8.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'alipayUserUpdate',
              method: 'POST',
              data: data
            });
          case 2:
            return _context8.abrupt("return", _context8.sent);
          case 3:
          case "end":
            return _context8.stop();
        }
      }, _callee8);
    }));
    function alipayUserReg(_x8) {
      return _alipayUserReg.apply(this, arguments);
    }
    return alipayUserReg;
  }(),
  bindCard: function () {
    var _bindCard = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee9(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee9$(_context9) {
        while (1) switch (_context9.prev = _context9.next) {
          case 0:
            _context9.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'bindCard',
              method: 'POST',
              data: data
            });
          case 2:
            return _context9.abrupt("return", _context9.sent);
          case 3:
          case "end":
            return _context9.stop();
        }
      }, _callee9);
    }));
    function bindCard(_x9) {
      return _bindCard.apply(this, arguments);
    }
    return bindCard;
  }(),
  getPhone: function () {
    var _getPhone = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee10(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee10$(_context10) {
        while (1) switch (_context10.prev = _context10.next) {
          case 0:
            _context10.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'getPhone',
              method: 'POST',
              data: data
            });
          case 2:
            return _context10.abrupt("return", _context10.sent);
          case 3:
          case "end":
            return _context10.stop();
        }
      }, _callee10);
    }));
    function getPhone(_x10) {
      return _getPhone.apply(this, arguments);
    }
    return getPhone;
  }(),
  brandCity: function () {
    var _brandCity = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee11(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee11$(_context11) {
        while (1) switch (_context11.prev = _context11.next) {
          case 0:
            _context11.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'brandCity',
              method: 'POST',
              data: data
            });
          case 2:
            return _context11.abrupt("return", _context11.sent);
          case 3:
          case "end":
            return _context11.stop();
        }
      }, _callee11);
    }));
    function brandCity(_x11) {
      return _brandCity.apply(this, arguments);
    }
    return brandCity;
  }(),
  brandBuy: function () {
    var _brandBuy = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee12(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee12$(_context12) {
        while (1) switch (_context12.prev = _context12.next) {
          case 0:
            _context12.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'brandBuy',
              method: 'POST',
              data: data
            });
          case 2:
            return _context12.abrupt("return", _context12.sent);
          case 3:
          case "end":
            return _context12.stop();
        }
      }, _callee12);
    }));
    function brandBuy(_x12) {
      return _brandBuy.apply(this, arguments);
    }
    return brandBuy;
  }(),
  brandList: function () {
    var _brandList = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee13(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee13$(_context13) {
        while (1) switch (_context13.prev = _context13.next) {
          case 0:
            _context13.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'brandList',
              method: 'POST',
              data: data
            });
          case 2:
            return _context13.abrupt("return", _context13.sent);
          case 3:
          case "end":
            return _context13.stop();
        }
      }, _callee13);
    }));
    function brandList(_x13) {
      return _brandList.apply(this, arguments);
    }
    return brandList;
  }(),
  brandBuyList: function () {
    var _brandBuyList = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee14(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee14$(_context14) {
        while (1) switch (_context14.prev = _context14.next) {
          case 0:
            _context14.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'agent/allFindBrand',
              method: 'POST',
              data: data
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhbaseUrl);
          case 2:
            return _context14.abrupt("return", _context14.sent);
          case 3:
          case "end":
            return _context14.stop();
        }
      }, _callee14);
    }));
    function brandBuyList(_x14) {
      return _brandBuyList.apply(this, arguments);
    }
    return brandBuyList;
  }(),
  myCollect: function () {
    var _myCollect = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee15(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee15$(_context15) {
        while (1) switch (_context15.prev = _context15.next) {
          case 0:
            _context15.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'myCollect',
              method: 'POST',
              data: data
            });
          case 2:
            return _context15.abrupt("return", _context15.sent);
          case 3:
          case "end":
            return _context15.stop();
        }
      }, _callee15);
    }));
    function myCollect(_x15) {
      return _myCollect.apply(this, arguments);
    }
    return myCollect;
  }(),
  needBookList: function () {
    var _needBookList = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee16(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee16$(_context16) {
        while (1) switch (_context16.prev = _context16.next) {
          case 0:
            _context16.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'needBookList',
              method: 'POST',
              data: data
            });
          case 2:
            return _context16.abrupt("return", _context16.sent);
          case 3:
          case "end":
            return _context16.stop();
        }
      }, _callee16);
    }));
    function needBookList(_x16) {
      return _needBookList.apply(this, arguments);
    }
    return needBookList;
  }(),
  topicInfo: function () {
    var _topicInfo = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee17(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee17$(_context17) {
        while (1) switch (_context17.prev = _context17.next) {
          case 0:
            _context17.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'topicInfo',
              method: 'POST',
              data: data
            });
          case 2:
            return _context17.abrupt("return", _context17.sent);
          case 3:
          case "end":
            return _context17.stop();
        }
      }, _callee17);
    }));
    function topicInfo(_x17) {
      return _topicInfo.apply(this, arguments);
    }
    return topicInfo;
  }(),
  brandInfo: function () {
    var _brandInfo = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee18(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee18$(_context18) {
        while (1) switch (_context18.prev = _context18.next) {
          case 0:
            _context18.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'brandInfo',
              method: 'POST',
              data: data
            });
          case 2:
            return _context18.abrupt("return", _context18.sent);
          case 3:
          case "end":
            return _context18.stop();
        }
      }, _callee18);
    }));
    function brandInfo(_x18) {
      return _brandInfo.apply(this, arguments);
    }
    return brandInfo;
  }(),
  brandInfoZh: function () {
    var _brandInfoZh = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee19(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee19$(_context19) {
        while (1) switch (_context19.prev = _context19.next) {
          case 0:
            _context19.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: "getBrand/".concat(data),
              method: 'GET'
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhnodeUrl);
          case 2:
            return _context19.abrupt("return", _context19.sent);
          case 3:
          case "end":
            return _context19.stop();
        }
      }, _callee19);
    }));
    function brandInfoZh(_x19) {
      return _brandInfoZh.apply(this, arguments);
    }
    return brandInfoZh;
  }(),
  getBrandDatePrice: function () {
    var _getBrandDatePrice = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee20(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee20$(_context20) {
        while (1) switch (_context20.prev = _context20.next) {
          case 0:
            _context20.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: "fe/brandDatePrice",
              method: 'GET',
              data: data
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhbaseUrl);
          case 2:
            return _context20.abrupt("return", _context20.sent);
          case 3:
          case "end":
            return _context20.stop();
        }
      }, _callee20);
    }));
    function getBrandDatePrice(_x20) {
      return _getBrandDatePrice.apply(this, arguments);
    }
    return getBrandDatePrice;
  }(),
  brandGoodZh: function () {
    var _brandGoodZh = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee21(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee21$(_context21) {
        while (1) switch (_context21.prev = _context21.next) {
          case 0:
            _context21.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: "fe/brandVlog?id=".concat(data, "&shopid=1"),
              method: 'GET'
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhbaseUrl);
          case 2:
            return _context21.abrupt("return", _context21.sent);
          case 3:
          case "end":
            return _context21.stop();
        }
      }, _callee21);
    }));
    function brandGoodZh(_x21) {
      return _brandGoodZh.apply(this, arguments);
    }
    return brandGoodZh;
  }(),
  brandCollect: function () {
    var _brandCollect = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee22(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee22$(_context22) {
        while (1) switch (_context22.prev = _context22.next) {
          case 0:
            _context22.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'brandCollect',
              method: 'POST',
              data: data
            });
          case 2:
            return _context22.abrupt("return", _context22.sent);
          case 3:
          case "end":
            return _context22.stop();
        }
      }, _callee22);
    }));
    function brandCollect(_x22) {
      return _brandCollect.apply(this, arguments);
    }
    return brandCollect;
  }(),
  brandBookList: function () {
    var _brandBookList = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee23(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee23$(_context23) {
        while (1) switch (_context23.prev = _context23.next) {
          case 0:
            _context23.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'brandBookList',
              method: 'POST',
              data: data
            });
          case 2:
            return _context23.abrupt("return", _context23.sent);
          case 3:
          case "end":
            return _context23.stop();
        }
      }, _callee23);
    }));
    function brandBookList(_x23) {
      return _brandBookList.apply(this, arguments);
    }
    return brandBookList;
  }(),
  addBrandBook: function () {
    var _addBrandBook = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee24(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee24$(_context24) {
        while (1) switch (_context24.prev = _context24.next) {
          case 0:
            _context24.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'addBrandBook',
              method: 'POST',
              data: data
            });
          case 2:
            return _context24.abrupt("return", _context24.sent);
          case 3:
          case "end":
            return _context24.stop();
        }
      }, _callee24);
    }));
    function addBrandBook(_x24) {
      return _addBrandBook.apply(this, arguments);
    }
    return addBrandBook;
  }(),
  myCard: function () {
    var _myCard = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee25() {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee25$(_context25) {
        while (1) switch (_context25.prev = _context25.next) {
          case 0:
            _context25.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'myCard',
              method: 'GET'
            });
          case 2:
            return _context25.abrupt("return", _context25.sent);
          case 3:
          case "end":
            return _context25.stop();
        }
      }, _callee25);
    }));
    function myCard() {
      return _myCard.apply(this, arguments);
    }
    return myCard;
  }(),
  appConfig: function () {
    var _appConfig = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee26() {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee26$(_context26) {
        while (1) switch (_context26.prev = _context26.next) {
          case 0:
            _context26.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'appConfig',
              method: 'GET'
            });
          case 2:
            return _context26.abrupt("return", _context26.sent);
          case 3:
          case "end":
            return _context26.stop();
        }
      }, _callee26);
    }));
    function appConfig() {
      return _appConfig.apply(this, arguments);
    }
    return appConfig;
  }(),
  cardInfo: function () {
    var _cardInfo = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee27(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee27$(_context27) {
        while (1) switch (_context27.prev = _context27.next) {
          case 0:
            _context27.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'getCradUser',
              method: 'GET',
              data: data
            });
          case 2:
            return _context27.abrupt("return", _context27.sent);
          case 3:
          case "end":
            return _context27.stop();
        }
      }, _callee27);
    }));
    function cardInfo(_x25) {
      return _cardInfo.apply(this, arguments);
    }
    return cardInfo;
  }(),
  brandMap: function () {
    var _brandMap = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee28(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee28$(_context28) {
        while (1) switch (_context28.prev = _context28.next) {
          case 0:
            _context28.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'brandMap',
              method: 'GET'
            });
          case 2:
            return _context28.abrupt("return", _context28.sent);
          case 3:
          case "end":
            return _context28.stop();
        }
      }, _callee28);
    }));
    function brandMap(_x26) {
      return _brandMap.apply(this, arguments);
    }
    return brandMap;
  }(),
  removeBook: function () {
    var _removeBook = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee29(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee29$(_context29) {
        while (1) switch (_context29.prev = _context29.next) {
          case 0:
            _context29.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'removeBook',
              method: 'POST',
              data: data
            });
          case 2:
            return _context29.abrupt("return", _context29.sent);
          case 3:
          case "end":
            return _context29.stop();
        }
      }, _callee29);
    }));
    function removeBook(_x27) {
      return _removeBook.apply(this, arguments);
    }
    return removeBook;
  }(),
  noteList: function () {
    var _noteList = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee30(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee30$(_context30) {
        while (1) switch (_context30.prev = _context30.next) {
          case 0:
            _context30.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'noteList',
              method: 'GET'
            });
          case 2:
            return _context30.abrupt("return", _context30.sent);
          case 3:
          case "end":
            return _context30.stop();
        }
      }, _callee30);
    }));
    function noteList(_x28) {
      return _noteList.apply(this, arguments);
    }
    return noteList;
  }(),
  noteInfo: function () {
    var _noteInfo = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee31(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee31$(_context31) {
        while (1) switch (_context31.prev = _context31.next) {
          case 0:
            _context31.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'noteInfo',
              method: 'POST',
              data: data
            });
          case 2:
            return _context31.abrupt("return", _context31.sent);
          case 3:
          case "end":
            return _context31.stop();
        }
      }, _callee31);
    }));
    function noteInfo(_x29) {
      return _noteInfo.apply(this, arguments);
    }
    return noteInfo;
  }(),
  myUseList: function () {
    var _myUseList = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee32(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee32$(_context32) {
        while (1) switch (_context32.prev = _context32.next) {
          case 0:
            _context32.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'myUseList',
              method: 'POST',
              data: data
            });
          case 2:
            return _context32.abrupt("return", _context32.sent);
          case 3:
          case "end":
            return _context32.stop();
        }
      }, _callee32);
    }));
    function myUseList(_x30) {
      return _myUseList.apply(this, arguments);
    }
    return myUseList;
  }(),
  getGoodWxshopLive: function () {
    var _getGoodWxshopLive = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee33(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee33$(_context33) {
        while (1) switch (_context33.prev = _context33.next) {
          case 0:
            _context33.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: "fe/liveBuy?id=".concat(data),
              method: 'GET'
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhbaseUrl);
          case 2:
            return _context33.abrupt("return", _context33.sent);
          case 3:
          case "end":
            return _context33.stop();
        }
      }, _callee33);
    }));
    function getGoodWxshopLive(_x31) {
      return _getGoodWxshopLive.apply(this, arguments);
    }
    return getGoodWxshopLive;
  }(),
  getShopGood: function () {
    var _getShopGood = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee34(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee34$(_context34) {
        while (1) switch (_context34.prev = _context34.next) {
          case 0:
            _context34.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'goods/detail',
              method: 'GET',
              data: data
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhbaseUrl);
          case 2:
            return _context34.abrupt("return", _context34.sent);
          case 3:
          case "end":
            return _context34.stop();
        }
      }, _callee34);
    }));
    function getShopGood(_x32) {
      return _getShopGood.apply(this, arguments);
    }
    return getShopGood;
  }(),
  getShopGoodRule: function () {
    var _getShopGoodRule = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee35(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee35$(_context35) {
        while (1) switch (_context35.prev = _context35.next) {
          case 0:
            _context35.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: "fe/brandDatePrice?id=".concat(data),
              method: 'GET'
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhbaseUrl);
          case 2:
            return _context35.abrupt("return", _context35.sent);
          case 3:
          case "end":
            return _context35.stop();
        }
      }, _callee35);
    }));
    function getShopGoodRule(_x33) {
      return _getShopGoodRule.apply(this, arguments);
    }
    return getShopGoodRule;
  }(),
  addShopOrder: function () {
    var _addShopOrder = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee36(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee36$(_context36) {
        while (1) switch (_context36.prev = _context36.next) {
          case 0:
            _context36.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'buy/wxShopAdd',
              method: 'POST',
              data: data
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhbaseUrl);
          case 2:
            return _context36.abrupt("return", _context36.sent);
          case 3:
          case "end":
            return _context36.stop();
        }
      }, _callee36);
    }));
    function addShopOrder(_x34) {
      return _addShopOrder.apply(this, arguments);
    }
    return addShopOrder;
  }(),
  pay: function () {
    var _pay = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee37(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee37$(_context37) {
        while (1) switch (_context37.prev = _context37.next) {
          case 0:
            _context37.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'addOrderAll',
              method: 'POST',
              data: data
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.zhnodeUrl);
          case 2:
            return _context37.abrupt("return", _context37.sent);
          case 3:
          case "end":
            return _context37.stop();
        }
      }, _callee37);
    }));
    function pay(_x35) {
      return _pay.apply(this, arguments);
    }
    return pay;
  }(),
  buy: function () {
    var _buy = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee38() {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee38$(_context38) {
        while (1) switch (_context38.prev = _context38.next) {
          case 0:
            _context38.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'placeOrder',
              method: 'GET'
            }, _api_config_json__WEBPACK_IMPORTED_MODULE_3__.baseUrl);
          case 2:
            return _context38.abrupt("return", _context38.sent);
          case 3:
          case "end":
            return _context38.stop();
        }
      }, _callee38);
    }));
    function buy() {
      return _buy.apply(this, arguments);
    }
    return buy;
  }(),
  myOrders: function () {
    var _myOrders = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee39(data) {
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee39$(_context39) {
        while (1) switch (_context39.prev = _context39.next) {
          case 0:
            _context39.next = 2;
            return Object(_utils_request__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])({
              url: 'myOrders',
              method: 'GET'
            });
          case 2:
            return _context39.abrupt("return", _context39.sent);
          case 3:
          case "end":
            return _context39.stop();
        }
      }, _callee39);
    }));
    function myOrders(_x36) {
      return _myOrders.apply(this, arguments);
    }
    return myOrders;
  }()
});

/***/ }),

/***/ "./src/utils/md5.js":
/*!**************************!*\
  !*** ./src/utils/md5.js ***!
  \**************************/
/*! exports provided: hex_md5 */
/*! exports used: hex_md5 */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return hex_md5; });
var hexcase = 0;
var chrsz = 8;
function core_md5(x, len) {
  x[len >> 5] |= 0x80 << len % 32;
  x[(len + 64 >>> 9 << 4) + 14] = len;
  var a = 1732584193;
  var b = -271733879;
  var c = -1732584194;
  var d = 271733878;
  for (var i = 0; i < x.length; i += 16) {
    var olda = a;
    var oldb = b;
    var oldc = c;
    var oldd = d;
    a = md5_ff(a, b, c, d, x[i + 0], 7, -680876936);
    d = md5_ff(d, a, b, c, x[i + 1], 12, -389564586);
    c = md5_ff(c, d, a, b, x[i + 2], 17, 606105819);
    b = md5_ff(b, c, d, a, x[i + 3], 22, -1044525330);
    a = md5_ff(a, b, c, d, x[i + 4], 7, -176418897);
    d = md5_ff(d, a, b, c, x[i + 5], 12, 1200080426);
    c = md5_ff(c, d, a, b, x[i + 6], 17, -1473231341);
    b = md5_ff(b, c, d, a, x[i + 7], 22, -45705983);
    a = md5_ff(a, b, c, d, x[i + 8], 7, 1770035416);
    d = md5_ff(d, a, b, c, x[i + 9], 12, -1958414417);
    c = md5_ff(c, d, a, b, x[i + 10], 17, -42063);
    b = md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);
    a = md5_ff(a, b, c, d, x[i + 12], 7, 1804603682);
    d = md5_ff(d, a, b, c, x[i + 13], 12, -40341101);
    c = md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);
    b = md5_ff(b, c, d, a, x[i + 15], 22, 1236535329);
    a = md5_gg(a, b, c, d, x[i + 1], 5, -165796510);
    d = md5_gg(d, a, b, c, x[i + 6], 9, -1069501632);
    c = md5_gg(c, d, a, b, x[i + 11], 14, 643717713);
    b = md5_gg(b, c, d, a, x[i + 0], 20, -373897302);
    a = md5_gg(a, b, c, d, x[i + 5], 5, -701558691);
    d = md5_gg(d, a, b, c, x[i + 10], 9, 38016083);
    c = md5_gg(c, d, a, b, x[i + 15], 14, -660478335);
    b = md5_gg(b, c, d, a, x[i + 4], 20, -405537848);
    a = md5_gg(a, b, c, d, x[i + 9], 5, 568446438);
    d = md5_gg(d, a, b, c, x[i + 14], 9, -1019803690);
    c = md5_gg(c, d, a, b, x[i + 3], 14, -187363961);
    b = md5_gg(b, c, d, a, x[i + 8], 20, 1163531501);
    a = md5_gg(a, b, c, d, x[i + 13], 5, -1444681467);
    d = md5_gg(d, a, b, c, x[i + 2], 9, -51403784);
    c = md5_gg(c, d, a, b, x[i + 7], 14, 1735328473);
    b = md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);
    a = md5_hh(a, b, c, d, x[i + 5], 4, -378558);
    d = md5_hh(d, a, b, c, x[i + 8], 11, -2022574463);
    c = md5_hh(c, d, a, b, x[i + 11], 16, 1839030562);
    b = md5_hh(b, c, d, a, x[i + 14], 23, -35309556);
    a = md5_hh(a, b, c, d, x[i + 1], 4, -1530992060);
    d = md5_hh(d, a, b, c, x[i + 4], 11, 1272893353);
    c = md5_hh(c, d, a, b, x[i + 7], 16, -155497632);
    b = md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);
    a = md5_hh(a, b, c, d, x[i + 13], 4, 681279174);
    d = md5_hh(d, a, b, c, x[i + 0], 11, -358537222);
    c = md5_hh(c, d, a, b, x[i + 3], 16, -722521979);
    b = md5_hh(b, c, d, a, x[i + 6], 23, 76029189);
    a = md5_hh(a, b, c, d, x[i + 9], 4, -640364487);
    d = md5_hh(d, a, b, c, x[i + 12], 11, -421815835);
    c = md5_hh(c, d, a, b, x[i + 15], 16, 530742520);
    b = md5_hh(b, c, d, a, x[i + 2], 23, -995338651);
    a = md5_ii(a, b, c, d, x[i + 0], 6, -198630844);
    d = md5_ii(d, a, b, c, x[i + 7], 10, 1126891415);
    c = md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);
    b = md5_ii(b, c, d, a, x[i + 5], 21, -57434055);
    a = md5_ii(a, b, c, d, x[i + 12], 6, 1700485571);
    d = md5_ii(d, a, b, c, x[i + 3], 10, -1894986606);
    c = md5_ii(c, d, a, b, x[i + 10], 15, -1051523);
    b = md5_ii(b, c, d, a, x[i + 1], 21, -2054922799);
    a = md5_ii(a, b, c, d, x[i + 8], 6, 1873313359);
    d = md5_ii(d, a, b, c, x[i + 15], 10, -30611744);
    c = md5_ii(c, d, a, b, x[i + 6], 15, -1560198380);
    b = md5_ii(b, c, d, a, x[i + 13], 21, 1309151649);
    a = md5_ii(a, b, c, d, x[i + 4], 6, -145523070);
    d = md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);
    c = md5_ii(c, d, a, b, x[i + 2], 15, 718787259);
    b = md5_ii(b, c, d, a, x[i + 9], 21, -343485551);
    a = safe_add(a, olda);
    b = safe_add(b, oldb);
    c = safe_add(c, oldc);
    d = safe_add(d, oldd);
  }
  return Array(a, b, c, d);
}
function md5_cmn(q, a, b, x, s, t) {
  return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b);
}
function md5_ff(a, b, c, d, x, s, t) {
  return md5_cmn(b & c | ~b & d, a, b, x, s, t);
}
function md5_gg(a, b, c, d, x, s, t) {
  return md5_cmn(b & d | c & ~d, a, b, x, s, t);
}
function md5_hh(a, b, c, d, x, s, t) {
  return md5_cmn(b ^ c ^ d, a, b, x, s, t);
}
function md5_ii(a, b, c, d, x, s, t) {
  return md5_cmn(c ^ (b | ~d), a, b, x, s, t);
}
function safe_add(x, y) {
  var lsw = (x & 0xFFFF) + (y & 0xFFFF);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return msw << 16 | lsw & 0xFFFF;
}
function bit_rol(num, cnt) {
  return num << cnt | num >>> 32 - cnt;
}
function str2binl(str) {
  var bin = Array();
  var mask = (1 << chrsz) - 1;
  for (var i = 0; i < str.length * chrsz; i += chrsz) bin[i >> 5] |= (str.charCodeAt(i / chrsz) & mask) << i % 32;
  return bin;
}
function binl2hex(binarray) {
  var hex_tab = hexcase ? "0123456789ABCDEF" : "0123456789abcdef";
  var str = "";
  for (var i = 0; i < binarray.length * 4; i++) {
    str += hex_tab.charAt(binarray[i >> 2] >> i % 4 * 8 + 4 & 0xF) + hex_tab.charAt(binarray[i >> 2] >> i % 4 * 8 & 0xF);
  }
  return str;
}
var hex_md5 = function hex_md5(s) {
  return binl2hex(core_md5(str2binl(s), s.length * chrsz));
};


/***/ }),

/***/ "./src/utils/request.js":
/*!******************************!*\
  !*** ./src/utils/request.js ***!
  \******************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ "./src/utils/api.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");

/*
 * @Author: 高超
 * @Date: 2021-10-30 11:39:41
 * @LastEditTime: 2022-07-21 10:14:38
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/utils/request.js
 * love jiajia
 */



/* harmony default export */ __webpack_exports__["a"] = (function () {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {
    method: 'GET',
    data: {}
  };
  var apiUrl = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'init';
  var token = _utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].getDataSafe('token') === null ? 'unlogin' : _utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].getData('token');
  var sendData = options.data || {};
  if (Boolean(sendData.point)) {
    var point = _utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].getDataSafe('mypoint');
    sendData.point = "".concat(point.api.latitude, ",").concat(point.api.longitude);
  }
  var getApiUrl = apiUrl === 'init' ? _utils_api__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].baseUrl : apiUrl;
  return _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.request({
    url: getApiUrl + options.url,
    data: Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])({}, sendData),
    header: {
      'Content-Type': 'application/json',
      'authorization': "Bearer ".concat(token),
      'customize-authorize': _utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].creatSign("0tTu8etSrB971nwaktfT91gOzIHD", options.method, Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])({}, sendData), "WehMeafRaFjtc2iZMpYEgglCcI3ZfoPh")
    },
    credentials: 'include',
    method: options.method.toUpperCase()
  }).then(function (res) {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.hideLoading();
    var statusCode = res.statusCode,
      data = res.data;
    if (statusCode >= 200 && statusCode < 300) {
      if (data.code !== 200 && data.code !== 0) {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.showToast({
          title: "".concat(res.data.message || res.data.msg, "~") || false,
          icon: 'none',
          mask: true
        });
      }
      if (data.code === 404) {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.navigateTo({
          url: '/pages/index/index?auth=none',
          success: function success(res) {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.removeStorageSync('token');
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.removeStorageSync('userInfo');
          }
        });
      }
      return data;
    } else {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default.a.redirectTo({
        url: '/pages/tips/index?type=102'
      });
      throw new Error("\u7F51\u7EDC\u8BF7\u6C42\u9519\u8BEF\uFF0C\u72B6\u6001\u7801".concat(statusCode));
    }
  });
});

/***/ }),

/***/ "./src/utils/tools.js":
/*!****************************!*\
  !*** ./src/utils/tools.js ***!
  \****************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/api */ "./src/utils/api.js");
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ "./node_modules/moment/dist/moment.js");
/* harmony import */ var _md5__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./md5 */ "./src/utils/md5.js");









/* harmony default export */ __webpack_exports__["a"] = (Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])({
  vin: 'v1.0.1',
  Api: _utils_api__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"],
  pageSize: 20,
  videoCoverH: 730,
  uploadUrl: 'https://zhonghuivideo.oss-accelerate.aliyuncs.com',
  picUrl: 'https://test.qqyhmmwg.com/res/card',
  webUrl: 'https://zhlx.dfxpw.cn',
  showNum: 20,
  phone: "4006091798",
  line_code: "hBFjF",
  ip: "https://test.qqyhmmwg.com/image",
  OSSAccessKeyId: 'LTAI4GCmXdLYnme6Qh6LuGjP',
  relation: [{
    label: '自己',
    value: 0
  }, {
    label: '爷爷',
    value: 1
  }, {
    label: '奶奶',
    value: 2
  }, {
    label: '父亲',
    value: 3
  }, {
    label: '母亲',
    value: 4
  }, {
    label: '配偶',
    value: 5
  }, {
    label: '子女',
    value: 6
  }, {
    label: '亲朋',
    value: 7
  }],
  getQuery: function getQuery() {
    var paramOper = _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.useRouter();
    return {
      path: paramOper.path,
      data: Boolean(paramOper.params.scene) ? this.urlToObj(decodeURIComponent(paramOper.params.scene)) : paramOper.params
    };
  },
  getTrim: function getTrim(str) {
    var s1 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : " ";
    var s2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : "";
    return str.replace(new RegExp(s1, "gm"), s2);
  },
  checkData: function checkData(str) {
    var len = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    var title = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : "";
    var type = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'null';
    var data = this.getTrim(str.toString());
    var partten_tel = /^[1][3456789]\d{9}$/;
    var partten_card = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
    var partten_tai = /^\d{8}|^[a-zA-Z0-9]{10}|^\d{18}$/;
    var partten_gh = /^([A-Z]\d{6,10}(\(\w{1}\))?)$/;
    var partten_hz = /^([a-zA-z]|[0-9]){5,17}$/;
    var partten_jun = /^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/;
    var other = {};
    var getBirthBySfzh = function getBirthBySfzh(sfzh) {
      var year = sfzh.substr(6, 4); // 身份证年
      var month = sfzh.substr(10, 2); // 身份证月
      var date = sfzh.substr(12, 2); // 身份证日
      var birth = "".concat(year, "-").concat(month, "-").concat(date);
      return birth;
    };
    var getSexBySfzh = function getSexBySfzh(sfzh) {
      var num = sfzh.substring(16, 17);
      return num % 2 === 0 ? 0 : 1;
    };
    var result = '';
    if (data.length >= len) {
      switch (type) {
        case "null":
          result = true;
          break;
        case "tel":
          result = partten_tel.test(data) ? true : "".concat(title, "\u53F7\u7801\u683C\u5F0F\u9519\u8BEF");
          break;
        case "tai":
          result = partten_tai.test(data) ? true : "".concat(title, "\u53F7\u7801\u683C\u5F0F\u9519\u8BEF");
          break;
        case "gh":
          result = partten_gh.test(data) ? true : "".concat(title, "\u53F7\u7801\u683C\u5F0F\u9519\u8BEF");
          break;
        case "hz":
          result = partten_hz.test(data) ? true : "".concat(title, "\u53F7\u7801\u683C\u5F0F\u9519\u8BEF");
          break;
        case "jun":
          result = partten_jun.test(data) ? true : "".concat(title, "\u53F7\u7801\u683C\u5F0F\u9519\u8BEF");
          break;
        case "card":
          var cardYn = partten_card.test(data);
          result = cardYn ? true : "".concat(title, "\u53F7\u7801\u683C\u5F0F\u9519\u8BEF");
          if (cardYn) {
            other.year = getBirthBySfzh(data);
            other.sex = getSexBySfzh(data);
          }
          break;
      }
    } else {
      result = "\u8BF7\u586B\u5199\u6B63\u786E\u7684".concat(title, "\u53F7\u7801");
    }
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])({
      check: result,
      data: data
    }, other);
  },
  // getLocation() {
  //   Taro.getLocation({
  //    type: 'wgs84',
  //    isHighAccuracy: true,
  //      success:  (res) => {
  //        const baiduMap = this.txMap_to_bdMap(res.latitude, res.longitude);
  //        this.setData('location', {
  //          latitude: baiduMap.lat,
  //          longitude: baiduMap.lng
  //        });
  //      }
  //    });
  // },
  setData: function setData(key, value) {
    try {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.setStorageSync(key, value);
      return true;
    } catch (e) {
      return false;
    }
  },
  goLoginSession: function goLoginSession() {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showModal({
      title: '您的登录状态已过期',
      content: '请重新登录后进行操作',
      confirmText: '立即登录',
      cancelText: '暂不登录',
      success: function success(res) {
        if (res.confirm) {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.navigateTo({
            url: '/pages/auth/index'
          });
        }
      }
    });
  },
  updateUserInfo: function updateUserInfo(data) {
    var _this = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee() {
      var userLogin;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _utils_api__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].userReg(data);
          case 2:
            userLogin = _context.sent;
            _this.setData('userInfo', userLogin.data);
            _this.setData('token', userLogin.data.token);
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  alipayUpdateUserInfo: function alipayUpdateUserInfo(data) {
    var _this2 = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee2() {
      var userLogin;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return _utils_api__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].alipayUserReg(data);
          case 2:
            userLogin = _context2.sent;
            _this2.setData('userInfo', userLogin.data);
            _this2.setData('token', userLogin.data.token);
          case 5:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }))();
  },
  getLogin: function getLogin() {
    var check = false;
    var loginStatus = this.getDataSafe('userInfo');
    var session_key = this.getDataSafe('session_key');
    if (loginStatus === null || session_key === null) {
      check = false;
    } else {
      // check = (Boolean(loginStatus.union_id) === false || Boolean(loginStatus.mem_id) === false) ? false : true
      check = Boolean(loginStatus.union_id) === false ? false : true;
    }
    return check;
  },
  userLoginHide: function userLoginHide() {
    var _this3 = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee5() {
      var check, aliPromise, promise;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee5$(_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            check = false;
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showLoading({
              mask: true,
              title: 'Loading'
            });
            if (!(_tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getEnv() === _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.ENV_TYPE.ALIPAY)) {
              _context5.next = 9;
              break;
            }
            aliPromise = new Promise(function (resolve, reject) {
              my.getAuthCode({
                scopes: 'auth_base',
                success: function () {
                  var _success = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee3(res) {
                    var openid;
                    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee3$(_context3) {
                      while (1) switch (_context3.prev = _context3.next) {
                        case 0:
                          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
                          _context3.prev = 1;
                          _context3.next = 4;
                          return _utils_api__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].getAlipayUserCode({
                            code: res.authCode,
                            from: "alipay"
                          });
                        case 4:
                          openid = _context3.sent;
                          _this3.setData('session_key', {
                            session_key: openid.data.session_key,
                            endtime: openid.data.endtime
                          });
                          _context3.next = 8;
                          return _this3.alipayUpdateUserInfo({
                            openid: openid.data.openid,
                            unionid: openid.data.unionid,
                            mem_id: null
                          });
                        case 8:
                          resolve(true);
                          _context3.next = 15;
                          break;
                        case 11:
                          _context3.prev = 11;
                          _context3.t0 = _context3["catch"](1);
                          console.log(_context3.t0);
                          reject(false);
                        case 15:
                        case "end":
                          return _context3.stop();
                      }
                    }, _callee3, null, [[1, 11]]);
                  }));
                  function success(_x) {
                    return _success.apply(this, arguments);
                  }
                  return success;
                }()
              });
            });
            _context5.next = 6;
            return aliPromise.then(function (result) {
              return result;
            }).catch(function (err) {
              return false;
            });
          case 6:
            return _context5.abrupt("return", _context5.sent);
          case 9:
            promise = new Promise(function (resolve, reject) {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.login({
                success: function () {
                  var _success2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee4(res) {
                    var openid;
                    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee4$(_context4) {
                      while (1) switch (_context4.prev = _context4.next) {
                        case 0:
                          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
                          _context4.prev = 1;
                          _context4.next = 4;
                          return _utils_api__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].getUserCode({
                            code: res.code,
                            from: "weixin"
                          });
                        case 4:
                          openid = _context4.sent;
                          _this3.setData('session_key', {
                            session_key: openid.data.session_key,
                            endtime: openid.data.endtime
                          });
                          // const zhMem = await Api.regZhMem({ unionid: openid.data.unionid, origin: 'NK'})
                          // await this.updateUserInfo({openid : openid.data.openid, unionid: openid.data.unionid, mem_id: zhMem.data})
                          _context4.next = 8;
                          return _this3.updateUserInfo({
                            openid: openid.data.openid,
                            unionid: openid.data.unionid,
                            mem_id: null
                          });
                        case 8:
                          resolve(true);
                          _context4.next = 15;
                          break;
                        case 11:
                          _context4.prev = 11;
                          _context4.t0 = _context4["catch"](1);
                          console.log(_context4.t0);
                          reject(false);
                        case 15:
                        case "end":
                          return _context4.stop();
                      }
                    }, _callee4, null, [[1, 11]]);
                  }));
                  function success(_x2) {
                    return _success2.apply(this, arguments);
                  }
                  return success;
                }()
              });
            });
            _context5.next = 12;
            return promise.then(function (result) {
              return result;
            }).catch(function (err) {
              return false;
            });
          case 12:
            return _context5.abrupt("return", _context5.sent);
          case 13:
          case "end":
            return _context5.stop();
        }
      }, _callee5);
    }))();
  },
  relogin: function relogin() {
    var _this4 = this;
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showModal({
      title: '您的登录状态已过期',
      content: '请重新登录后进行操作',
      confirmText: '立即登录',
      cancelText: '暂不登录',
      success: function () {
        var _success3 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee6(res) {
          return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee6$(_context6) {
            while (1) switch (_context6.prev = _context6.next) {
              case 0:
                if (!res.confirm) {
                  _context6.next = 3;
                  break;
                }
                _context6.next = 3;
                return _this4.userLoginHide();
              case 3:
              case "end":
                return _context6.stop();
            }
          }, _callee6);
        }));
        function success(_x3) {
          return _success3.apply(this, arguments);
        }
        return success;
      }()
    });
  },
  getDva: function getDva(namespace) {
    var mapStateToProps = function mapStateToProps(state) {
      var auth = state[namespace];
      return {
        auth: auth
      };
    };
    return mapStateToProps;
  },
  getPhoneNumber: function getPhoneNumber(data) {
    var _this5 = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee8() {
      var session_key, userInfo, _self;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee8$(_context8) {
        while (1) switch (_context8.prev = _context8.next) {
          case 0:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showLoading({
              mask: true,
              title: 'Loading'
            });
            session_key = _this5.getDataSafe("session_key");
            userInfo = _this5.getDataSafe("userInfo");
            _self = _this5;
            if (session_key === null) {
              _this5.relogin();
            }
            if (data.detail.errMsg === 'getPhoneNumber:ok') {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.checkSession({
                success: function () {
                  var _success4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee7() {
                    var phone;
                    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee7$(_context7) {
                      while (1) switch (_context7.prev = _context7.next) {
                        case 0:
                          _context7.next = 2;
                          return _utils_api__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].getPhone(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])({}, data.detail), {}, {
                            session_key: session_key.session_key
                          }));
                        case 2:
                          phone = _context7.sent;
                          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
                          if (phone.data.check) {
                            userInfo.user_tel = phone.data.purePhoneNumber;
                            _this5.setData("userInfo", userInfo);
                            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.navigateTo({
                              url: '/pages/bind/rel'
                            });
                          }
                        case 5:
                        case "end":
                          return _context7.stop();
                      }
                    }, _callee7);
                  }));
                  function success() {
                    return _success4.apply(this, arguments);
                  }
                  return success;
                }(),
                fail: function fail() {
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
                  _self.relogin();
                }
              });
            } else {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
            }
          case 6:
          case "end":
            return _context8.stop();
        }
      }, _callee8);
    }))();
  },
  getUserInfo: function getUserInfo(callback) {
    var _this6 = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee10() {
      var userInfo, session_key, nickname, avatar;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee10$(_context10) {
        while (1) switch (_context10.prev = _context10.next) {
          case 0:
            userInfo = _this6.getDataSafe('userInfo');
            session_key = _this6.getDataSafe('session_key');
            nickname = userInfo.nickname, avatar = userInfo.avatar;
            if (!(nickname !== null && avatar !== null)) {
              _context10.next = 6;
              break;
            }
            callback();
            return _context10.abrupt("return", false);
          case 6:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getUserProfile({
              desc: '用于完善会员资料',
              // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
              success: function () {
                var _success5 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee9(res) {
                  var data;
                  return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee9$(_context9) {
                    while (1) switch (_context9.prev = _context9.next) {
                      case 0:
                        if (!(userInfo !== null && session_key !== null)) {
                          _context9.next = 5;
                          break;
                        }
                        data = {
                          openid: userInfo.code,
                          all: {
                            session_key: session_key.session_key,
                            encryptedData: res.encryptedData,
                            iv: res.iv
                          }
                        };
                        _context9.next = 4;
                        return _this6.updateUserInfo(data);
                      case 4:
                        callback(true);
                      case 5:
                      case "end":
                        return _context9.stop();
                    }
                  }, _callee9);
                }));
                function success(_x4) {
                  return _success5.apply(this, arguments);
                }
                return success;
              }(),
              fail: function fail(res) {
                callback(false);
              }
            });
          case 7:
          case "end":
            return _context10.stop();
        }
      }, _callee10);
    }))();
  },
  goLogin: function goLogin() {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showModal({
      title: '您还未登录',
      content: '请登录后进行操作',
      confirmText: '立即登录',
      cancelText: '暂不登录',
      success: function success(res) {
        if (res.confirm) {
          //todo
        }
      }
    });
  },
  getDataSafe: function getDataSafe(key) {
    var data = _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getStorageSync(key);
    if (Boolean(data)) {
      return data;
    }
    return null;
  },
  getData: function getData(key) {
    var data = _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getStorageSync(key);
    if (Boolean(data)) {
      return data;
    }
    return {
      id: 0
    };
  },
  getSamll: function getSamll(str, num) {
    if (str === null) {
      return '';
    }
    try {
      return str.length > num ? str.slice(0, num) + '...' : str;
    } catch (e) {
      return 'error';
    }
  },
  getImageSize: function () {
    var _getImageSize = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee11(url) {
      var sizeData;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee11$(_context11) {
        while (1) switch (_context11.prev = _context11.next) {
          case 0:
            sizeData = null;
            _context11.next = 3;
            return _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getImageInfo({
              src: url,
              success: function success(res) {
                sizeData = res;
              },
              fail: function fail() {
                sizeData = null;
              }
            });
          case 3:
            return _context11.abrupt("return", sizeData);
          case 4:
          case "end":
            return _context11.stop();
        }
      }, _callee11);
    }));
    function getImageSize(_x5) {
      return _getImageSize.apply(this, arguments);
    }
    return getImageSize;
  }(),
  createId: function createId() {
    var l = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;
    var x = '1234567890poiuytrewqasdfghjklmnbvcxz';
    var tmp = '';
    for (var i = 0; i < l; i++) {
      tmp += x.charAt(Math.ceil(Math.random() * 100000000) % x.length);
    }
    return tmp;
  },
  textBr: function textBr(txt) {
    var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    var per = type === 0 ? '<br/>' : ' ';
    return txt.replace(/\r\n/g, per).replace(/\n/g, per).replace(/\s/g, ' '); //转换格式
  },
  saveToAlbum: function saveToAlbum(img) {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.saveImageToPhotosAlbum({
      filePath: img,
      success: function success() {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showToast({
          title: '保存图片成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function fail() {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showToast({
          title: '请在小程序设置中打开保存相册权限',
          icon: 'fail',
          duration: 2000
        });
      }
    });
  },
  delTmpFile: function delTmpFile() {
    console.log(_tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.env.TEMP_DATA_PATH);
    var fsm = _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getFileSystemManager();
    fsm.readdir({
      dirPath: _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.env.USER_DATA_PATH,
      success: function success(res) {
        console.log(res);
        res.files.forEach(function (el) {
          // 遍历文件列表里的数据
          // 删除存储的垃圾数据
          if (el.includes('.png')) {
            fsm.unlink({
              filePath: "".concat(wx.env.USER_DATA_PATH, "/").concat(el),
              // 这里注意。文件夹也要加上，如果直接文件名的话会无法找到这个文件
              fail: function fail(e) {
                console.log('readdir文件删除失败：', e);
              }
            });
          }
        });
      }
    });
  },
  findKeyword: function findKeyword(arr, data) {
    return arr.reduce(function (prev, cur) {
      if (data.includes(cur.id)) {
        prev.push(cur.title);
      }
      return prev;
    }, []);
  },
  findMap: function findMap(data, keyword) {
    return data.reduce(function (prev, cur) {
      if (cur.title.includes(keyword)) {
        prev.push(cur);
      }
      return prev;
    }, []);
  },
  arrSwich: function arrSwich(arr, val) {
    if (!arr.includes(val)) {
      return [].concat(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(arr), [val]);
    } else {
      var num = arr.indexOf(val);
      arr.splice(num, 1);
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(arr);
    }
  },
  setHttps: function setHttps(url) {
    if (Boolean(url)) {
      return url.includes('http://') ? url.replace('http://', 'https://') : url;
    } else {
      return '';
    }
  },
  reSetH: function reSetH(top) {
    var res = _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getSystemInfoSync();
    var bet = top;
    var clientHeight = res.windowHeight;
    var height = clientHeight;
    // const clientWidth = res.windowWidth;
    // const changeHeight = 750 / clientWidth;
    // const height = (clientHeight * changeHeight) - bet;
    return height + 210;
  },
  reTopH: function reTopH() {
    var top = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 13;
    var res = _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getSystemInfoSync();
    var clientHeight = res.statusBarHeight;
    var clientWidth = res.windowWidth;
    var changeHeight = 750 / clientWidth;
    var height = clientHeight * changeHeight;
    return height + top;
  },
  reVideoH: function reVideoH(h, w) {
    var videoFix = w / h;
    var res = _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getSystemInfoSync();
    var clientWidth = res.windowWidth;
    var height = parseInt(clientWidth / videoFix, 10);
    return height;
  },
  getSize: function getSize() {
    var res = _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getSystemInfoSync();
    if (res.windowWidth < 375) {
      return '320';
    }
    return '';
  },
  getArea: function getArea(mode) {
    if (mode === 0) {
      return 'width: 100%;height:1200rpx';
    } else if (mode === 1) {
      return 'width: 100%;height:600rpx';
    } else if (mode === 2) {
      return 'width: 100%;height:700rpx';
    }
  },
  getExf: function getExf(name) {
    var exf = name.split('.');
    return exf.length === 0 ? '.mp4111' : ".".concat(exf[exf.length - 1]);
  },
  clearS: function clearS(string) {
    try {
      return string.replace(/\n/g, ' ');
    } catch (e) {
      return string;
    }
  },
  getDateDiff: function getDateDiff(time) {
    try {
      var nowTime = new Date();
      var day = nowTime.getDate();
      var hours = parseInt(nowTime.getHours(), 10);
      var minutes = nowTime.getMinutes();
      var timeyear = time.substring(0, 4);
      var timemonth = time.substring(5, 7);
      var timeday = time.substring(8, 10);
      var timehours = parseInt(time.substring(11, 13), 10);
      var timeminutes = time.substring(14, 16);
      var d_day = Math.abs(day - timeday);
      var d_hours = Math.abs(hours - timehours);
      var d_minutes = Math.abs(minutes - timeminutes);
      if (d_day > 1) {
        return timemonth + '-' + timeday;
      } else if (d_day === 0 && d_hours > 0 && d_hours <= 24) {
        return d_hours + '小时前';
      } else if (d_day === 1) {
        return '昨天';
      } else if (d_minutes > 0 && d_minutes <= 60) {
        return '刚刚';
      }
      return '刚刚';
    } catch (e) {
      return '';
    }
  },
  getPrice: function getPrice(price) {
    var num = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;
    if (isNaN(price)) {
      return price;
    }
    price = parseFloat(price);
    var _re = Number.isInteger(price) ? parseInt(price, 10) : parseFloat(price).toFixed(num);
    _re = _re.toString();
    if (_re.charAt(_re.length - 1) === '0' && _re.includes('.')) {
      _re = _re.substring(0, _re.length - 1);
      if (_re.charAt(_re.length - 1) === '.') {
        _re = _re.substring(0, _re.length - 1);
      }
    }
    return _re;
  },
  urlToObj: function urlToObj(str) {
    var obj = {};
    var arr2 = str.split("&");
    for (var i = 0; i < arr2.length; i++) {
      var res = arr2[i].split("=");
      obj[res[0]] = res[1];
    }
    return obj;
  },
  uploadImages: function uploadImages(data, callback) {
    var _this7 = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee13() {
      var uploadList;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee13$(_context13) {
        while (1) switch (_context13.prev = _context13.next) {
          case 0:
            uploadList = [];
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showLoading({
              mask: true,
              title: '上传中'
            });
            _context13.next = 4;
            return data.forEach( /*#__PURE__*/function () {
              var _ref = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee12(val) {
                return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee12$(_context12) {
                  while (1) switch (_context12.prev = _context12.next) {
                    case 0:
                      _context12.next = 2;
                      return _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.uploadFile({
                        url: _this7.Api.baseUrl + 'uploadFiles',
                        filePath: val,
                        name: 'media',
                        header: {
                          'authorization': _this7.getData('token'),
                          'customize-authorize': _this7.creatSign("0tTu8etSrB971nwaktfT91gOzIHD", 'post', {}, "WehMeafRaFjtc2iZMpYEgglCcI3ZfoPh")
                        },
                        success: function success(res) {
                          var result = JSON.parse(res.data);
                          if (result.code === 200) {
                            var resData = JSON.parse(res.data);
                            uploadList.push(resData.data[0]);
                            if (data.length === uploadList.length) {
                              _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
                              callback(uploadList);
                            }
                          } else {
                            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
                            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showToast({
                              title: '上传失败：图片含有敏感信息',
                              icon: 'none',
                              duration: 2000
                            });
                          }
                        }
                      });
                    case 2:
                    case "end":
                      return _context12.stop();
                  }
                }, _callee12);
              }));
              return function (_x6) {
                return _ref.apply(this, arguments);
              };
            }());
          case 4:
          case "end":
            return _context13.stop();
        }
      }, _callee13);
    }))();
  },
  removeCss: function removeCss(content) {
    var reg = /(style|class)="[^"]+"/gi;
    var img = /<img[^>]+>/gi;
    var regClass = new RegExp('class="', "g");
    var regstyle = new RegExp('style="', "g");
    var res;
    if (img.test(content)) {
      res = content.match(img);
      for (var i = 0; i < res.length; i++) {
        content = content.replace(res[i], res[i].replace(reg, ""));
      }
    }
    var removeHtml = content.replace(regstyle, 'data-style="').replace(regClass, 'data-none="').replace(/\<img/gi, '<img class="richImg" ');
    return "<div style=\"line-height: 25px\">".concat(removeHtml, "</div>");
  },
  getWxNote: function getWxNote(callback) {
    var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'neworder';
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showLoading({
      title: '读取中'
    });
    var tid = ['3Q1GMvMyGNVxbRA2Qk-42MXHRBDiSEfcq_hlRfNxjm0'];
    if (_tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.canIUse('requestSubscribeMessage')) {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.getSetting({
        withSubscriptions: true,
        success: function success(res) {
          if (!res.subscriptionsSetting.mainSwitch) {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
            callback();
          } else {
            try {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.requestSubscribeMessage({
                tmplIds: tid,
                success: function success(res) {
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
                  callback();
                }
              });
            } catch (e) {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
              callback();
            }
          }
        }
      });
    }
  },
  txMap_to_bdMap: function txMap_to_bdMap(lat, lng) {
    var pi = 3.14159265358979324 * 3000.0 / 180.0;
    var x = lng;
    var y = lat;
    var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * pi);
    var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * pi);
    lng = z * Math.cos(theta) + 0.0065;
    lat = z * Math.sin(theta) + 0.006;
    return {
      lat: lat,
      lng: lng
    };
  },
  bdMap_to_txMap: function bdMap_to_txMap(lat, lng) {
    var pi = 3.14159265358979324 * 3000.0 / 180.0;
    var x = lng - 0.0065;
    var y = lat - 0.006;
    var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * pi);
    var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * pi);
    lng = z * Math.cos(theta);
    lat = z * Math.sin(theta);
    return {
      lat: lat,
      lng: lng
    };
  },
  dateInfo: function dateInfo(stime, etime, enable_week) {
    var weekStr = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    if (stime === '0' && etime === '0') {
      var out = [];
      var week_arr = enable_week.split(',');
      week_arr.map(function (val) {
        return out.push(weekStr[parseInt(val, 10)]);
      });
      return "".concat(out.join('，'), "\u53EF\u7528");
    }
    return "".concat(Object(moment__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(parseInt(stime, 10) * 1000).format('MM-DD'), " \u81F3 ").concat(Object(moment__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(parseInt(etime, 10) * 1000).format('MM-DD'), "\u53EF\u7528");
  },
  addNoteList: function addNoteList(data) {
    var _arguments = arguments,
      _this8 = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee15() {
      var callback, can, mlist;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee15$(_context15) {
        while (1) switch (_context15.prev = _context15.next) {
          case 0:
            callback = _arguments.length > 1 && _arguments[1] !== undefined ? _arguments[1] : function () {};
            can = data.reduce(function (prev, cur) {
              if (cur.had === false) {
                prev.push({
                  coupon_id: cur.id,
                  cash: cur.cash,
                  user_id: _this8.getDataSafe('userInfo').id || 0
                });
                cur.had = true;
              }
              return prev;
            }, []);
            mlist = ['G1DCV-HjVlxGsCAPcb6N-ek7ASl_QWrbIAXYoJkUJ5s', 'eaLH6aQHL72rl0eROCPLi99PNUX-rV0w2nh8QPFvx4M'];
            wx.requestSubscribeMessage({
              tmplIds: mlist,
              success: function () {
                var _success6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee14(res) {
                  return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee14$(_context14) {
                    while (1) switch (_context14.prev = _context14.next) {
                      case 0:
                      case "end":
                        return _context14.stop();
                    }
                  }, _callee14);
                }));
                function success(_x7) {
                  return _success6.apply(this, arguments);
                }
                return success;
              }()
            });
            return _context15.abrupt("return", true);
          case 5:
          case "end":
            return _context15.stop();
        }
      }, _callee15);
    }))();
  },
  creatSign: function creatSign(accessKeyId, method) {
    var data = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    var accessKey = arguments.length > 3 ? arguments[3] : undefined;
    var timestamp = function () {
      return parseInt(Date.parse(new Date()) / 1000, 10);
    }();
    var nonce = "".concat(this.createId(12));
    var stringToSignArray = ["accessKeyId=".concat(accessKeyId), "nonce=".concat(nonce), "timestamp=".concat(timestamp), "method=".concat(method.toUpperCase()), "key=".concat(accessKey)];
    stringToSignArray.push("content=".concat(Object(_md5__WEBPACK_IMPORTED_MODULE_8__[/* hex_md5 */ "a"])(encodeURI(JSON.stringify(data))).toUpperCase()));
    var stringToSign = stringToSignArray.sort();
    // console.log(encodeURI(JSON.stringify(data)))
    var signStr = Object(_md5__WEBPACK_IMPORTED_MODULE_8__[/* hex_md5 */ "a"])(stringToSign.join('&_*')).toString().toUpperCase();
    return ["accessKeyId=".concat(accessKeyId), "nonce=".concat(nonce), "timestamp=".concat(timestamp), "signature=".concat(signStr)].join(';');
  },
  getLocation: function getLocation() {
    return new Promise(function (resolve, reject) {
      var _locationChangeFn = function _locationChangeFn(res) {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.offLocationChange(_locationChangeFn);
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.stopLocationUpdate();
        resolve(res);
      };
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.startLocationUpdate({
        success: function success(res) {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.onLocationChange(_locationChangeFn);
        },
        fail: function fail(err) {
          // Taro.openSetting({
          //     success(res) {
          //         res.authSetting = {
          //             "scope.userLocation": true
          //         }
          //     }
          // })
          reject(err);
        }
      });
    });
  },
  brandRank: function brandRank(num) {
    var str = '优质景区';
    var temp = [];
    if (num >= 3) {
      for (var i = 0; i < num; i++) {
        temp.push('A');
      }
      str = "".concat(temp.join(''), "\u7EA7\u666F\u533A");
    }
    return str;
  },
  brandSpace: function brandSpace(num) {
    return num === 'IN' ? "室内" : num === 'OUT' ? "室外" : "室内+室外";
  },
  getCardList: function getCardList() {
    var _arguments2 = arguments,
      _this9 = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee16() {
      var params, data;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee16$(_context16) {
        while (1) switch (_context16.prev = _context16.next) {
          case 0:
            params = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : [];
            _context16.next = 3;
            return _this9.Api.myCardList(params);
          case 3:
            data = _context16.sent;
            if (!(data.code === 200)) {
              _context16.next = 6;
              break;
            }
            return _context16.abrupt("return", data.data);
          case 6:
          case "end":
            return _context16.stop();
        }
      }, _callee16);
    }))();
  },
  cardCheck: function cardCheck(cardList) {
    var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'all';
    return type === 'all' ? cardList.filter(function (v) {
      return v.enable === true;
    }) : cardList.filter(function (v) {
      return v.enable_now === true;
    });
  },
  appConfig: function appConfig(type) {
    var _this10 = this;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee17() {
      var data;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee17$(_context17) {
        while (1) switch (_context17.prev = _context17.next) {
          case 0:
            _context17.next = 2;
            return _this10.Api.appConfig();
          case 2:
            data = _context17.sent;
            if (!(data.code === 200)) {
              _context17.next = 11;
              break;
            }
            _context17.t0 = type;
            _context17.next = _context17.t0 === 'buy' ? 7 : _context17.t0 === 'menu' ? 8 : _context17.t0 === 'order' ? 9 : 10;
            break;
          case 7:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.navigateToMiniProgram(data.data.buyCard);
          case 8:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.navigateToMiniProgram(data.data.menu);
          case 9:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.navigateToMiniProgram(data.data.order);
          case 10:
            return _context17.abrupt("return", data.data);
          case 11:
            return _context17.abrupt("return", null);
          case 12:
          case "end":
            return _context17.stop();
        }
      }, _callee17);
    }))();
  },
  getlimt: function getlimt(type, num) {
    if (type === '1') {
      return "\u63D0\u524D".concat(num, "\u5C0F\u65F6");
    } else if (type === '2') {
      return "\u63D0\u524D".concat(num, "\u5929");
    } else if (type === '3') {
      return num === '0' ? '当日发货' : num === '1' ? '次日发货' : "".concat(num, "\u65E5\u540E\u53D1\u8D27");
    }
    if (type === '0' && num === '-1') {
      return '预售抢购';
    }
    if (type === '0' && num === '-2') {
      return '预约使用';
    }
    return '随买随用';
  },
  showUseDate: function showUseDate(is_book, open_time, close_time) {
    if (parseInt(is_book, 10) === 1) {
      return 0;
    } else {
      return parseInt(Object(moment__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(close_time).diff(Object(moment__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(open_time), 'day'), 10) >= 180 ? 1 : -1;
    }
  }
}, "dateInfo", function dateInfo(stime, etime, enable_week) {
  var weekStr = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  if (stime === '0' && etime === '0') {
    var out = [];
    var week_arr = enable_week.split(',');
    week_arr.map(function (val) {
      return out.push(weekStr[parseInt(val, 10)]);
    });
    return "".concat(out.join('，'), "\u53EF\u7528");
  }
  return "".concat(Object(moment__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(parseInt(stime, 10) * 1000).format('MM-DD'), " \u81F3 ").concat(Object(moment__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(parseInt(etime, 10) * 1000).format('MM-DD'), "\u53EF\u7528");
}), "IdentityCodeValid", function IdentityCodeValid(idCard) {
  var regIdCard = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
  return regIdCard.test(idCard);
}));

/***/ })

}]);
//# sourceMappingURL=common.js.map
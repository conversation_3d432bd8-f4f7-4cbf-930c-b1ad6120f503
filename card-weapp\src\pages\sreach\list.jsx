/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-08-03 18:05:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/list.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import { useSelector } from 'react-redux'
import Taro, { useReachBottom } from '@tarojs/taro'
import Memu from '@/components/memu'
import None from '@/components/tips/none'
import SreachTools from './bar'
import Item from '@/components/brand/item'
import tools from '@/utils/tools'
import "./index.scss"

const SreachIndex = (props) => {
    const query = tools.getQuery()
    const login = useSelector(state => state.login)
    const [init, setInit] = useState(null)
    const [config, setConfig] = useState(false)
    const [page , setPage] = useState(1)
    const [params , setParams] = useState({})
    const [brandList , setBrandList] = useState([])  
    const [word, setWord] = useState(query.data.v)
    const [loaded, setLoaded] = useState(false)

    useEffect( async () => {
        if (login) {
          const selData = await tools.Api.brandCity({
            line_id: tools.line_code
          })
          if (selData.code === 200) {
            setConfig(selData.data)
          }
        }
    }, [login])

    useEffect( async () => {
        setLoaded(false)
        Taro.showLoading({
          mask: true,
          title: '请稍等'
        })
        if (login && config) {
          const apiParams = {
            line_id: tools.line_code,
            order : "km",
            point : true,
            page : {
                current: page,
                pageSize : 20
            },
            ...params
          }
          if (Boolean(word)) {
            apiParams.keywords = word
          }
          const data = await tools.Api.brandList(apiParams)
          if (data.code === 200) {
            setInit(data.data.page)
            setBrandList([
                ...brandList,
                ...data.data.list
            ])
          }
          setLoaded(true)
          Taro.hideLoading()
        }
    }, [login, page, params, word, config])

    useReachBottom(() => {
        const nextPage = init.current + 1
        if (nextPage <= init.totalPage) {
          setPage(nextPage)
        } else {
          Taro.showToast({
            title: '暂无更多内容',
            icon: 'none',
            duration: 2000
          })
        }
    })

    return (
        <View>
         {config ? <SreachTools config={config} callBack={data => {
            setBrandList([])
            setParams(data)
            setPage(1)
          }} setkey = {(v) => {
            setBrandList([])
            setWord(v)
            setPage(1)
          }} init={word}/> : null}
          <View>
            {
                (brandList.length > 0) ?  brandList.map(v => <Item className="list" data={v} cname="images_l"/>) : <None loaded={loaded}/>
            }
          </View>
          <Memu now={2} />
        </View>
    )
}
export default SreachIndex 
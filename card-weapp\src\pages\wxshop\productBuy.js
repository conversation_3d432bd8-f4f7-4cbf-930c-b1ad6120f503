import { useState, useEffect } from 'react'
import Taro from '@tarojs/taro';
import { AtInput, AtIcon, AtInputNumber, AtFloatLayout, AtList, AtListItem  } from 'taro-ui';
import { View, Swiper, SwiperItem, Text, Image } from '@tarojs/components';
import moment from 'moment';
import Skeleton from 'taro-skeleton'
import tools from '@/utils/tools';
import './index.scss';

function ProductBuy(props) {
    const paramOper = Taro.useRouter();
    let params = {};
    if (paramOper.params.scene) {
      params = tools.urlToObj(decodeURIComponent(paramOper.params.scene));
    } else {
      params = paramOper.params;
    }
    const weekStr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const { productId, rulescode }  = params;
    const [initData, setInitData] =useState({code: -1, data:{
      buy_price: 0,
      show_price: 0,
      tips: []
    }, content_web: {
      brand: { memo : "", tagsname: []},
      hotelList: {
        facilities: []
      },
    }});
    const [dateShow, setDateShow] = useState(false)
    const [buyNum, setBuyNum] = useState(1)
    const [showPrice, setShowPrice] = useState(0)
    const [userInfo, setUserInfo] = useState({
      receive_name: '',
      receive_tel: '',
      link_credit_no: -1,
      user_note: '',
      bookdate:'',
      bookweek:''
    })
    const [goodConfig, setGoodConfig] = useState(
      {
        tel: 1,
        max: 10,
        limitnum: 1,
        limittype: 2,
        open_time_i: 0,
        close_time_i: 0,
        is_book: false,
        any_book: false,
        needAddress: false,
        user_no_en: false
      }
    )
    const [ruleData , setRuleData] = useState({
      good: { name: ''},
      rules: []
    })

    const [ selRule , setSelRule] = useState(null)

    useEffect( async () => {
      ableDate()
      if(productId) {
        const ruleList = await tools.Api.getShopGoodRule(productId);
        const data = await tools.Api.getGoodWxshopLive(productId);
        const goodData = await tools.Api.getShopGood({
          goods_id: productId,
          shopid: 1
        })
        setInitData(data)
        setRuleData(ruleList.data)
        if (rulescode === 'none') {
          setShowPrice(tools.getPrice(data.data.share_price))
        } else {
          setSelRule(ruleList.data.rules.find( v => v.title_code === rulescode))
        }
        setGoodConfig({
            category_id: goodData.data.goods.category_id,
            path: (goodData.data.goods.category_id === '900') ? `/pages/brand/good?id=${productId}` : `/pages/brand/index?id=${goodData.data.brand.id}`,
            tel: goodData.data.goods.good_tel,
            max: goodData.data.goods.buyerlimit_num,
            limitnum: goodData.data.goods.limitnum,
            limittype: goodData.data.goods.limittype,
            open_time_i: goodData.data.goods.open_time_i,
            close_time_i: goodData.data.goods.close_time_i,
            is_book: (goodData.data.goods.is_book === '1'),
            any_book: (goodData.data.goods.is_book === '0'),
            needAddress: (goodData.data.goods.category_id === '900'),
            user_no_en: (goodData.data.goods.zwyneed.includes('link_credit_no') || goodData.data.goods.buyerlimit === '2')
        })
        if (moment().unix() > parseInt(goodData.data.goods.close_time_i, 10)) {
          Taro.redirectTo({
            url: '/pages/tips/index?type=w401'
          })
          return false
        }
      }
    }, [])

    useEffect(() => {
      if (selRule !== null) {
        setShowPrice(tools.getPrice(selRule.share_price))
        setShowPrice(tools.getPrice((buyNum * parseFloat(selRule.share_price)).toString()))
      }
    }, [selRule])
    
    const addOrder = async () => {
      const partten = /^[1][3456789]\d{9}$/;
      const userLoginInfo = tools.getData('userInfo');     
      if (userLoginInfo === null || Boolean(userLoginInfo.mem_id) === false ) {
        Taro.showToast({ icon: 'none', title: '登录信息错误' });
        return false;
      }
      if (userInfo.receive_name === '') {
        Taro.showToast({ icon: 'none', title: '请填写游客姓名' });
        return false;
      }
      if (!partten.test(userInfo.receive_tel)) {
        Taro.showToast({ icon: 'none', title: '请填写正确的手机号' });
        return false;
      }
      if (goodConfig.user_no_en && !tools.IdentityCodeValid(userInfo.link_credit_no)) {
        Taro.showToast({ icon: 'none', title: '请填写正确的身份证号' });
        return false;
      }
      if (goodConfig.is_book && userInfo.bookdate === '') {
        Taro.showToast({ icon: 'none', title: '请选择游玩时间' });
        return false;
      }
      const obj = {
        goods_id: parseInt(productId, 10),
        stock: buyNum,
        spec: [],
        payment_id: 1,
        in_time: 0,
        out_time: 0,
        re_userid: 0,
        vlog_id: 0,
        from_agent_id: 0,
        shop_id: 1,
        pay_for: 1,
        origin: 'NK',
        day: 1,
        category_id: goodConfig.category_id,
        order_name: initData.data.title,
        user: {
          id: userLoginInfo.mem_id,
          brand_id: 0
        },
        ...userInfo
      }
      if (Boolean(obj.bookdate)) {
        obj.bookdate = obj.bookdateval
      }
      if (selRule !== null) {
        obj.rules_id = selRule.title_code;
        obj.rules_name = selRule.title;
      }
      Taro.showLoading({
        title: '提交中',
        mask: true
      })
      const orderAdd = await tools.Api.addShopOrder(obj)
      Taro.hideLoading();
      if (orderAdd.code !== 0) {
        Taro.showToast({ icon: 'none', title: orderAdd.msg });
        return false
      } else {
        Taro.redirectTo({
          url: `/pages/pay/index?id=${orderAdd.data.orderId}`,
        });
      }
    }

    const ableDate = () => {
      const date = []
      const hour = moment().hour()
      for (let i= (hour > 17) ? 1 : 0; i <=8 ; i++) {
        date.push({
          day : moment().add(i, 'days').format('MM月DD日'),
          week : weekStr[moment().add(i, 'days').weekday()],
          val: moment().add(i, 'days').format('YYYY-MM-DD')
        })
      }
      return date
    }

    return (initData.code === -1) ? ( <View><Skeleton title="" row={30}></Skeleton></View>) :
    (<View>
        <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>
          <View className='at-row'>
            <View className='at-col at-col-3'>
              <Image mode="widthFix" src={initData.data.logo} style={{width: '100px', borderRadius: '8px'}}/>
            </View>
            <View className='at-col at-col-1 at-col--auto'></View>
            <View className='at-col at-col-8 at-col--wrap' style={{fontSize: '16px',paddingLeft: '7px', marginTop: '0px'}}>
              <View>{initData.data.name}{ruleData.good.name}</View>
              <View style={{marginTop: '12px'}}>
                <View className='at-row'>
                  <View className='at-col at-col-4' style={{paddingTop: '4px', color: '#756e6e', fontSize:'14px'}}>购买数量:</View>
                  <View className='at-col at-col-1 at-col--auto'></View>
                  <View className='at-col at-col-7'>
                    <AtInputNumber
                    min={1}
                    max={parseInt(goodConfig.max, 10)}
                    step={1}
                    value={buyNum}
                    onChange={(value)=>{
                      setBuyNum(value)
                      const onePirce = (selRule === null) ? parseFloat(initData.data.share_price) : parseFloat(selRule.share_price)
                      setShowPrice(tools.getPrice((value * onePirce).toString()))
                    }}
                  />
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>
        {(selRule !== null) ? <View>
          <View style={{borderLeft: '4px solid #2196F3', margin: '20px 10px', textIndent: '10px'}}>使用时间 <Text style={{paddingLeft: '5px', fontSize: '14px', color: '#585757'}}>(有效期:{moment(ruleData.good.open_time_i * 1000).format('YYYY-MM-DD')}至{moment(ruleData.good.close_time_i * 1000).format('YYYY-MM-DD')})</Text></View>
          <View style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>
            {ruleData.rules.map( (v, index) => <View className="at-row" style={(ruleData.rules.length === index + 1) ? {} :{borderBottom: '1px solid #ece8e8', marginBottom: '7px'}} onClick={() => setSelRule(v)}>
              <View className='at-col at-col-10'>
                <View style={{fontSize: '14px', fontWeight: 'bold'}}>{v.title}</View>
                <View style={{padding: '8px 0', color: '#F44336', fontSize: '13px'}}>有效期内{tools.dateInfo(v.stime, v.etime, v.enable_week)}</View>
              </View>
              {
                (selRule.title_code === v.title_code) ? <View className='at-col at-col-2' style={{textAlign: 'center'}}>
                <View style={{background: '#eae9e9', width: '20px', height: '20px', borderRadius: '30px', textAlign: 'center', padding: '5px', margin: '5px 15px'}}>
                  <AtIcon value='check' size='22' color='#4CAF50'></AtIcon>
                </View>
              </View>   : null
              }
            </View>)}
          </View>
        </View> : null}
        
        <View style={{borderLeft: '4px solid #2196F3', margin: '20px 10px', textIndent: '10px'}}>预定人信息</View>
        <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>
          <View className="at-row">
            <View className='at-col at-col-2' style={{fontSize: '14px'}}>
              游客姓名
            </View>
            <View className='at-col at-col-1'></View>    
            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#8a8787'}}>
              <AtInput placeholder="必填, 便于商家为您提供服务" name="receive_name" value={userInfo.receive_name} maxLength={6} onChange={(value) => {
                setUserInfo({
                  ...userInfo,
                  receive_name: value
                })
              }}/>
            </View> 
            <View className='at-col at-col-1' style={{position:'relative'}}>
              <View style={{position: 'absolute', right: 0, top: '-1px'}}>
                <AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>
              </View>
            </View>    
          </View>
        </View>
        <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>
          <View className="at-row">
            <View className='at-col at-col-2' style={{fontSize: '14px'}}>
              手机号码
            </View>
            <View className='at-col at-col-1'></View>    
            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#8a8787'}}>
              <AtInput placeholder="必填, 用于接收使用验证码" name="receive_tel" type="number" value={userInfo.receive_tel} maxLength={15} onChange={(value) => {
                // console.log(value)
                setUserInfo({
                  ...userInfo,
                  receive_tel: value
                })
              }}/>
            </View> 
            <View className='at-col at-col-1' style={{position:'relative'}}>
              <View style={{position: 'absolute', right: 0, top: '-1px'}}>
                <AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>
              </View>
            </View>    
          </View>
        </View>
        {(goodConfig.user_no_en) ? <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>
          <View className="at-row">
            <View className='at-col at-col-2' style={{fontSize: '14px'}}>
              身份证号
            </View>
            <View className='at-col at-col-1'></View>    
            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#8a8787'}}>
              <AtInput placeholder="必填, 此产品需实名使用" type="idcard" name="link_credit_no" value={userInfo.link_credit_no} maxLength={20} onChange={(value) => {
                setUserInfo({
                  ...userInfo,
                  link_credit_no: value
                })
              }}/>
            </View> 
            <View className='at-col at-col-1' style={{position:'relative'}}>
              <View style={{position: 'absolute', right: 0, top: '-1px'}}>
                <AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>
              </View>
            </View>    
          </View>
        </View> : null}
        {(goodConfig.is_book) ?    <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}} onClick={() => setDateShow(true)}>
          <View className="at-row">
            <View className='at-col at-col-2' style={{fontSize: '14px'}}>
              游玩时间
            </View>
            <View className='at-col at-col-1'></View>    
            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#d0caca'}}>
              {
                (userInfo.bookdate === '') ? '必选, 请选择出游时间' : <Text style={{color: '#000'}}>{userInfo.bookweek} {userInfo.bookdate}</Text>
              }
            </View> 
            <View className='at-col at-col-1' style={{position:'relative'}}>
              <View style={{position: 'absolute', right: 0, top: '-3px'}}>
                <Text style={{color: '#2196F3', fontSize: '14px'}}>选择</Text><AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>
              </View>
            </View> 
          </View>
        </View> : null}
    
        <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>
          <View className="at-row">
            <View className='at-col at-col-2' style={{fontSize: '14px'}}>
            备注信息
            </View>
            <View className='at-col at-col-1'></View>    
            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#8a8787'}}>
              <AtInput placeholder="选填, 可填写需求和留言" name="user_note" value={userInfo.user_note} maxLength={40} onChange={(value) => {
                console.log(value)
                setUserInfo({
                  ...userInfo,
                  user_note: value
                })
              }}/>
            </View> 
            <View className='at-col at-col-1' style={{position:'relative'}}>
              <View style={{position: 'absolute', right: 0, top: '-1px'}}>
                <AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>
              </View>
            </View>    
          </View>
        </View>
        <View className='at-row good_live_buybtn'>
        <View className='at-col at-col-8' style={{paddingTop: '4px', color: '#ec7413', paddingLeft: '13px'}}>
          <View><Text style={{ paddingRight: '8px', color: '#333'}}>共 <Text style={{fontWeight: 'bold'}}>{buyNum}</Text> 件，合计</Text><Text style={{fontSize: '14px', paddingRight: '2px'}}>¥</Text><Text style={{fontSize: '24px'}}>{showPrice}</Text></View> 
        </View>
        <View className='at-col at-col-4' onClick={ async () => {
          
          await addOrder()
        }}>            
        <View className={(parseInt(initData.data.gc_inventory, 10) > 0) ? 'go_btn go_buy' : 'go_btn go_out'} style={{marginRight: '20px'}}>{(parseInt(initData.data.gc_inventory, 10) > 0) ? '立即支付' : '已售罄'}</View>
      </View>
    </View>
    <View style={{height: '90px'}}></View>
    <AtFloatLayout isOpened={dateShow} title="请选择游玩时间" onClose={() => setDateShow(false)}>
      <View className='at-row at-row--wrap' style={{margin: '6px 0'}}>
        {
          ableDate().map(v => <View className='at-col at-col-4'><View style={{border: '1px solid #03A9F4', width: '100px', padding: '5px', textAlign: 'center', margin: '6px 0', color: `${userInfo.bookdate === v.day ? '#fff' : '#03A9F4'}`, background: `${userInfo.bookdate === v.day ? '#03A9F4' : '#fff'}`, borderRadius: '4px'}} onClick={() => {
            setUserInfo({
              ...userInfo,
              bookdate: v.day,
              bookweek: v.week,
              bookdateval: v.val
            })
            setDateShow(false)
          }}>{v.week} {v.day}</View></View>)
        }
      </View>
    </AtFloatLayout>
  </View>)
}
export default ProductBuy
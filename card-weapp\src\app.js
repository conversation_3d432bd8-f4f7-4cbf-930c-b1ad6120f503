/*
 * @Author: 高超
 * @Date: 2021-10-30 10:32:40
 * @LastEditTime: 2021-12-12 17:54:58
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/app.js
 * love jiajia
 */
import { Component } from 'react'
import Taro from '@tarojs/taro'
import { createStore } from "redux";
import { Provider } from 'react-redux'
import Layout from '@/components/layout'
import 'taro-ui/dist/style/index.scss'
import 'taro-skeleton/dist/index.css'
import './app.scss'

const initialState = { login: false, location : false };

const reducer = (state, action) => {
  switch (action.type) {
    case "login":
      return {login : true}
    case "location":
      return {location : true}
    default:
      return state;
  }
};
const store = createStore(reducer, initialState);

class App extends Component {

  componentWillMount() {
    const noNeedRelogin = Taro.getStorageSync('no_need_relogin');
    if (noNeedRelogin && noNeedRelogin > 0) {
      console.warn("noNeedRelogin yes -----------------------------------")
    } else {
      console.warn("noNeedRelogin no --------------------------------")
      Taro.removeStorageSync('token');
      Taro.removeStorageSync('userInfo');
      Taro.removeStorageSync('session_key');
      Taro.setStorageSync('no_need_relogin', 1);
    }
    this.updateNick();
  }

  async componentDidMount () {
    this.updateNick();
  }
  
  componentDidShow () {}

  componentDidHide () {}

  componentDidCatchError () {}



  updateNick () {
    if (Taro.canIUse('getUpdateManager')) {
      const updateManager = Taro.getUpdateManager();
      updateManager.onCheckForUpdate(function (res) {
        if(res.hasUpdate) {
          updateManager.onUpdateReady(function () {
            Taro.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: function (res) {
                if (res.confirm) {
                  updateManager.applyUpdate()
                }
              }
            })
          });
          updateManager.onUpdateFailed(function () {
            Taro.showToast({
              title: '更新失败',
              icon: 'none',
              duration: 2000
            })
          });
        }
      });
    } else {
      Taro.showModal({
        title: '提示',
        content: '当前版本过低，无法使用该功能，请升级到最新版本后重试。'
      })
    }
  }

  // this.props.children 是将要会渲染的页
  render () {
    return (
      <Provider store={store}>
        <Layout>{this.props.children}</Layout>
      </Provider>
    )
  }
}

export default App

/*
 * @Author: 高超
 * @Date: 2021-10-30 11:39:41
 * @LastEditTime: 2022-07-21 10:14:38
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/utils/request.js
 * love jiajia
 */
import Taro from '@tarojs/taro';
import Api from '@/utils/api';
import tools from '@/utils/tools';

export default (options = { method: 'GET', data: {} }, apiUrl = 'init') => {
  const token = (tools.getDataSafe('token') === null) ? 'unlogin' : tools.getData('token');
  const sendData = options.data || {}
  if (Boolean(sendData.point)) {
    const point = tools.getDataSafe('mypoint')
    sendData.point = `${point.api.latitude},${point.api.longitude}`
  }
  const getApiUrl = (apiUrl === 'init') ? Api.baseUrl : apiUrl
  return Taro.request({
    url: getApiUrl + options.url,
    data: {
      ...sendData
    },
    header: {
      'Content-Type': 'application/json',
      'authorization' : `Bearer ${token}`,
      'customize-authorize': tools.creatSign("0tTu8etSrB971nwaktfT91gOzIHD", options.method, { ...sendData }, "WehMeafRaFjtc2iZMpYEgglCcI3ZfoPh")
    },
    credentials: 'include',
    method: options.method.toUpperCase(),
  }).then((res) => {
    Taro.hideLoading();
    const { statusCode, data } = res;
    if (statusCode >= 200 && statusCode < 300) {
      if (data.code !== 200 && data.code !== 0) {
        Taro.showToast({
          title: `${res.data.message || res.data.msg}~` || res.data.code,
          icon: 'none',
          mask: true,
        });
      }
      if (data.code === 404) {
        Taro.navigateTo({
          url: '/pages/index/index?auth=none',
          success: function (res) {
            Taro.removeStorageSync('token');
            Taro.removeStorageSync('userInfo');
          }
        })
      }
      return data;
    } else {
      Taro.redirectTo({
        url: '/pages/tips/index?type=102'
      })
      throw new Error(`网络请求错误，状态码${statusCode}`);
    }
  })
}
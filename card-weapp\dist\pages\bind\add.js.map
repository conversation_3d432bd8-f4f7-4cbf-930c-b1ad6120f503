{"version": 3, "file": "pages/bind/add.js", "sources": ["webpack:///./src/pages/bind/add.jsx", "webpack:///./src/components/back/index.scss", "webpack:///./src/components/back/modal.jsx", "webpack:///./src/pages/bind/add.jsx?69ad"], "sourcesContent": ["import { Component, useEffect, useState, useRef } from 'react'\r\nimport Taro from '@tarojs/taro'\r\nimport { View, Text, Input, Image, Picker } from '@tarojs/components'\r\nimport MyModal from '@/components/back/modal';\r\nimport Bg from '@/components/tool/bg'\r\nimport { AtIcon } from \"taro-ui\"\r\nimport tools from '@/utils/tools'\r\nimport \"./index.scss\"\r\n\r\nfunction Index (props) { \r\n  const params =  tools.getQuery()\r\n  const who = Boolean(params.data.rel) ? parseInt(params.data.rel, 10) : 0\r\n  const good_key = Boolean(params.data.key) ? params.data.key : null\r\n  console.warn(\"good_key -------------\", good_key, who)\r\n  const prve = (who === 0) ? '' : tools.relation[who].label\r\n  const [ myphoto, setMyphoto ] = useState(null)\r\n  const [ isCheck, setIsCheck ]= useState(false)\r\n  const [ isClose, setIsClose ]= useState(false)\r\n  const [ isOk, setIsOk ]= useState(false)\r\n  const [ cardType, setCardType ]= useState(0)\r\n  const [ cardVal, setCardVal] = useState('')\r\n  const [ cut, setCut] = useState(false)\r\n  const [ info, setInfo ] = useState({\r\n    user_img: \"\",\r\n    user_img_check: \"请上传持卡人照片\",\r\n    real_name : null,\r\n    real_name_check: \"请填写姓名\",\r\n    id_card_no: \"fake\",\r\n    id_card_no_check: \"请填写证件号码\",\r\n    good_key: good_key,\r\n    good_key_check: \"请填写卡片激活码\",\r\n  })\r\n  const imageCropper = useRef(null);\r\n  const id_card_list_str  = ['身份证','护照', '港澳居民来往内地通行证', '台湾省居民来往大陆通行证', '军官证' ]\r\n  const id_card_list = [\r\n    {\r\n      len: 10,\r\n      title: '身份证',\r\n      val: 0,\r\n      type: 'card'\r\n    },\r\n    {\r\n      len: 5,\r\n      title: '护照',\r\n      val: 1,\r\n      type: 'hz'\r\n    },\r\n    {\r\n      len: 5,\r\n      title: '港澳居民来往内地通行证',\r\n      val: 2,\r\n      type: 'gh'\r\n    },\r\n    {\r\n      len: 5,\r\n      val: 3,\r\n      title: '台湾省居民来往大陆通行证',\r\n      type: 'tai'\r\n    },\r\n    {\r\n      len: 5,\r\n      val: 4,\r\n      title: '军官证',\r\n      type: 'jun'\r\n    },\r\n  ]\r\n  const uploadOk = data => {\r\n    setMyphoto(data[0])\r\n    info.user_img = data[0]\r\n    setInfo(info)\r\n  }\r\n  const checkAll = (val, len, title, name, type = \"null\") =>{\r\n    if(val.detail.value.length > 0 ) {\r\n      const check = tools.checkData(val.detail.value, len, title, type)\r\n      if (check.check !== true) {\r\n        Taro.showToast({\r\n          title: check.check,\r\n          icon: 'none',\r\n          mask: true,\r\n          duration: 2000\r\n        })\r\n        info[`${name}_check`] = check.check\r\n        info[name] = null\r\n      } else {\r\n        info[name] = check.data\r\n      }\r\n      setInfo(info)\r\n    }\r\n  }\r\n  const reData = () => {\r\n    const tips =[]\r\n    Object.keys(info).forEach(val => {\r\n          if (!val.includes(\"_check\")) {\r\n            if (info[val] === null) {\r\n              tips.push(info[`${val}_check`])\r\n            }\r\n          }\r\n        })\r\n    if (tips.length > 0) {\r\n      setIsCheck(true)\r\n    } else {\r\n      setIsOk(true)\r\n    }\r\n  }\r\n  const bindCard = async () => {\r\n    setIsOk(false)\r\n    if (isClose) {\r\n      return false\r\n    }\r\n    setIsClose(true)\r\n    Taro.showLoading({\r\n      mask: true,\r\n      title: '提交中'\r\n    });\r\n    const userYear = (cardType === 0) ? tools.checkData(info.id_card_no, 17, \"身份证\", \"card\") : {\r\n      year: 1984,\r\n      sex: 1\r\n    }\r\n    const data = {\r\n      good_key: info.good_key.toUpperCase(),\r\n      info : {\r\n        id_card_no : info.id_card_no,\r\n        id_card_type : cardType,\r\n        real_name : info.real_name,\r\n        user_tel : info.user_tel,\r\n        user_img : info.user_img,\r\n        user_year: userYear.year,\r\n        user_gender: userYear.sex,\r\n        user_rel: who\r\n      }\r\n    }\r\n    const oper = await tools.Api.bindCard(data)\r\n    if (oper.code === 200) {\r\n      Taro.redirectTo({\r\n        url: \"/pages/tips/index?type=100\"\r\n      })\r\n    } else {\r\n      setIsClose(false)\r\n    }\r\n  }\r\n  const isAuthorize = async (authorize) => {\r\n\t\tlet authSetting = await Taro.getSetting();\r\n\t\tif (!authSetting[authorize]) {\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tTaro.authorize({\r\n\t\t\t\t\tscope: \"scope.writePhotosAlbum\",\r\n\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\tresolve(\"yes\");\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail() {\r\n\t\t\t\t\t\treject();\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n\t\treturn Promise.resolve(\"yes\");\r\n\t}\r\n  const handleOk = async() => {\r\n\t\tlet isAuthSetting = await isAuthorize(\"scope.writePhotosAlbum\");\r\n\t\tif (isAuthSetting === \"yes\") {\r\n\t\t\tlet result = await this.imageCropper.current._getImg();\r\n\t\t\tconsole.log(result, \"result\");\r\n\t\t\t// this.setState({\r\n\t\t\t// \tpreviewUrl: result.tempFilePath,\r\n\t\t\t// });\r\n\t\t\t// Taro.saveImageToPhotosAlbum({\r\n\t\t\t// \tfilePath: result.tempFilePath,\r\n\t\t\t// \tsuccess: function(res) {\r\n\t\t\t// \t\tTaro.showToast({\r\n\t\t\t// \t\t\ttitle: \"保存图片成功\",\r\n\t\t\t// \t\t});\r\n\t\t\t// \t},\r\n\t\t\t// \tfail() {\r\n\t\t\t// \t\tTaro.showToast({\r\n\t\t\t// \t\t\ttitle: \"保存图片失败\",\r\n\t\t\t// \t\t\ticon: \"fail\",\r\n\t\t\t// \t\t});\r\n\t\t\t// \t},\r\n\t\t\t// });\r\n\t\t} else {\r\n\t\t\tTaro.showToast({\r\n\t\t\t\ttitle: \"请在右上角授权\",\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n  return (\r\n    <View>\r\n      <Bg />\r\n      <View className='addPage'>\r\n         {/* <View className=\"\">\r\n            <View className=\"image\" onClick={() => {\r\n              wx.chooseImage({\r\n                count: 1,\r\n                sizeType: ['compressed'],\r\n                success : async res  => {\r\n                  const tempFilePaths = res.tempFilePaths\r\n                  setCut(tempFilePaths[0])\r\n                  await tools.uploadImages(tempFilePaths, uploadOk)\r\n                }\r\n              })\r\n            }}> \r\n                {(myphoto === null) ? <View>\r\n                  <View style={{marginTop: '30px'}}><AtIcon value='share-2' size='24' color='#2196F3'></AtIcon></View>\r\n                  <View style={{marginTop: '5px'}}>上传持卡人照片</View>\r\n                </View> : <View>\r\n                  <Image src={myphoto} mode=\"aspectFit\" className=\"photo\"/>\r\n                </View>\r\n                }\r\n            </View>\r\n          </View>*/}\r\n          <View class=\"item\" style={{marginTop: \"18px\"}}>\r\n            <Text style={{width: \"80px\"}}>{prve}姓名</Text>\r\n            <Input type='text' placeholder='请输入姓名' className=\"input\" maxlength={10} onBlur={(val) => {\r\n               checkAll(val, 2, \"姓名\", \"real_name\")\r\n            }}/>\r\n          </View>\r\n          {/*<View style={{marginTop: \"18px\"}}>\r\n            <Text>{prve}证件类型</Text>\r\n            <Picker mode='selector' range={id_card_list_str} onChange={val => {\r\n              setCardVal('')\r\n              setCardType(parseInt(val.detail.value, 10))\r\n            }}>\r\n              <View className=\"at-row input\">\r\n                <View className='at-col at-col-11'>{id_card_list[cardType].title}</View>\r\n                <View className='at-col at-col-1' style={{textAlign: 'right'}}><AtIcon value='chevron-right' size='20' color='#2196f3'></AtIcon></View>\r\n              </View>\r\n            </Picker>\r\n          </View>\r\n          <View style={{marginTop: \"18px\"}}>\r\n            <Text>{prve}证件号码</Text>\r\n            <Input type='text' value={cardVal} placeholder={`请输入${id_card_list[cardType].title}号码`} className=\"input\" maxlength={18} onBlur={(val) => {\r\n              setCardVal(val.detail.value)\r\n              checkAll(val, id_card_list[cardType].len, id_card_list[cardType].title, \"id_card_no\", id_card_list[cardType].type)\r\n            }}/>\r\n          </View>*/}\r\n          <View class=\"item\" style={{marginTop: \"18px\"}}>\r\n            <Text style={{width: \"80px\"}}>卡激活码</Text>\r\n            <Input type='text' placeholder='请输入卡激活码' className=\"input\" maxlength={15} value={good_key} onBlur={(val) => {\r\n                checkAll(val, 8, \"卡激活码\", \"good_key\")\r\n            }}/>\r\n          </View>\r\n          <View className=\"card\">\r\n            <Image src={`${tools.ip}/card.jpg`} mode=\"widthFix\" style={{width: \"70%\", marginBottom: \"12px\"}}/>\r\n            <View style={{color: \"#3b883e\", fontSize: '25rpx'}}>实体卡右下角为激活码，电子票请在小程序我的订单中查看</View>\r\n          </View>\r\n      </View>\r\n      <View className=\"jbtn\" onClick={() => {\r\n          reData()\r\n      }}>提 交</View>\r\n      <MyModal open={isCheck} title=\"提示\" ok={{name: \"重新填写\", fun : () => {\r\n        setIsCheck(false)\r\n      }}}>\r\n        <View style={{fontSize: \"16px\"}}>\r\n          {Object.keys(info).map(val => {\r\n            if (info[val] === null) {\r\n              return <View style={{padding :'4px'}}><Text style={{color: \"red\"}}>X</Text> {info[`${val}_check`]}</View>\r\n            }\r\n          })}\r\n        </View>\r\n      </MyModal>\r\n      <MyModal open={isOk} title=\"确认信息\" ok={{name: \"立即提交\", fun : () => {\r\n        bindCard()\r\n      }}} close={{name: \"重新填写\", fun : () => {\r\n        setIsOk(false)\r\n      }}}>\r\n        <View style={{fontSize: \"14px\", padding: \"0 5px\"}}>\r\n          <View style={{paddingBottom: \"5px\"}}>真实姓名: {info.real_name}</View>\r\n{/*          <View style={{paddingBottom: \"5px\"}}>证件类型: {id_card_list[cardType].title}</View>\r\n          <View style={{paddingBottom: \"5px\"}}>证件号码: {info.id_card_no}</View>*/}\r\n          <View style={{paddingBottom: \"5px\"}}>卡激活码: {info.good_key}</View>\r\n        </View>\r\n      </MyModal>     \r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "// extracted by mini-css-extract-plugin", "import { View, Text, Button } from '@tarojs/components'\r\nimport { AtModal, AtModalHeader, AtModalContent, AtModalAction } from \"taro-ui\"\r\nimport \"./index.scss\"\r\n\r\nconst Modal = (props) => {\r\n    const {open, title, ok, close} = props;\r\n    return (\r\n        <AtModal isOpened={open}>\r\n        <AtModalHeader>{title}</AtModalHeader>\r\n        <AtModalContent>\r\n          {props.children}\r\n        </AtModalContent>\r\n        <AtModalAction> \r\n            {Boolean(close) ? <Button onClick={() => close.fun()}>{close.name}</Button> : null}\r\n            <Button onClick={() => ok.fun()}>{ok.name}</Button> \r\n        </AtModalAction>\r\n      </AtModal>\r\n    )\r\n}\r\nexport default Modal ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./add.jsx\"\nvar config = {\"navigationBarTitleText\":\"用卡信息提交\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/bind/add', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AATA;AAAA;AAUA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAnCA;AAAA;AAAA;AAoCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAhBA;AAAA;AAAA;AAiBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AA3BA;AAAA;AAAA;AA4BA;AACA;AAEA;AAAA;AAsBA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAqBA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AACA;;;;;;;;;;;ACnRA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;AACA;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
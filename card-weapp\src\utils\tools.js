import Taro from '@tarojs/taro';
import Api from '@/utils/api';
import moment from 'moment';
import { hex_md5 } from './md5';
export default {
    vin: 'v1.0.1',
    Api,
    pageSize: 20,
    videoCoverH: 730,
    uploadUrl: 'https://zhonghuivideo.oss-accelerate.aliyuncs.com',
    picUrl: 'https://test.qqyhmmwg.com/res/card',
    webUrl: 'https://zhlx.dfxpw.cn',
    showNum: 20,
    phone: "4006091798",
    line_code: "hBFjF",
    ip: "https://test.qqyhmmwg.com/image",
    OSSAccessKeyId: 'LTAI4GCmXdLYnme6Qh6LuGjP',
    relation: [{label: '自己', value: 0}, {label: '爷爷', value: 1}, {label: '奶奶', value: 2}, {label: '父亲', value: 3}, {label: '母亲', value: 4}, {label: '配偶', value: 5}, {label: '子女', value: 6}, {label: '亲朋', value: 7}],
    getQuery () {
      const paramOper = Taro.useRouter();
      return {
        path: paramOper.path,
        data : (Boolean(paramOper.params.scene))
        ? this.urlToObj(decodeURIComponent(paramOper.params.scene))
        : paramOper.params
      } 
      ;
    },
    getTrim(str, s1 = " ", s2 = "") {
      return str.replace(new RegExp(s1, "gm"), s2);
    },
    checkData(str, len = 0 , title = "", type = 'null') {
      const data = this.getTrim(str.toString())
      const partten_tel = /^[1][3456789]\d{9}$/;
      const partten_card = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
      const partten_tai = /^\d{8}|^[a-zA-Z0-9]{10}|^\d{18}$/;
      const partten_gh = /^([A-Z]\d{6,10}(\(\w{1}\))?)$/;
      const partten_hz = /^([a-zA-z]|[0-9]){5,17}$/;
      const partten_jun = /^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/;
      const other = {}
      const getBirthBySfzh = (sfzh) => {
        const year = sfzh.substr(6, 4); // 身份证年
        const month = sfzh.substr(10, 2); // 身份证月
        const date = sfzh.substr(12, 2); // 身份证日
        const birth = `${year}-${month}-${date}`;
        return birth;
      };
      const getSexBySfzh = (sfzh) => {
        const num = sfzh.substring(16, 17);
        return num % 2 === 0 ? 0 : 1;
      };
      let result = ''
      if (data.length >= len) {
        switch (type) {
          case "null" :
            result = true
          break;
          case "tel" :
            result = partten_tel.test(data) ? true : `${title}号码格式错误`
          break;
          case "tai" :
            result = partten_tai.test(data) ? true : `${title}号码格式错误`
          break;
          case "gh" :
            result = partten_gh.test(data) ? true : `${title}号码格式错误`
          break;
          case "hz" :
            result = partten_hz.test(data) ? true : `${title}号码格式错误`
          break;
          case "jun" :
            result = partten_jun.test(data) ? true : `${title}号码格式错误`
          break;
          case "card" :
            const cardYn = partten_card.test(data)
            result = cardYn ? true : `${title}号码格式错误`
            if (cardYn) {
              other.year = getBirthBySfzh(data)
              other.sex = getSexBySfzh(data)
            }
          break;
        }
      } else {
        result = `请填写正确的${title}号码`
      }
      return {
        check: result,
        data,
        ...other
      }
    },
    // getLocation() {
    //   Taro.getLocation({
    //    type: 'wgs84',
    //    isHighAccuracy: true,
    //      success:  (res) => {
    //        const baiduMap = this.txMap_to_bdMap(res.latitude, res.longitude);
    //        this.setData('location', {
    //          latitude: baiduMap.lat,
    //          longitude: baiduMap.lng
    //        });
    //      }
    //    });
    // },
    setData: (key, value) => {
      try {
        Taro.setStorageSync(key, value);
        return true
      } catch (e) {
        return false
      }
    },
    goLoginSession: () => {
      Taro.showModal({
        title: '您的登录状态已过期',
        content: '请重新登录后进行操作',
        confirmText: '立即登录',
        cancelText:  '暂不登录',
        success (res) {
          if (res.confirm) {
            Taro.navigateTo({
              url: '/pages/auth/index',
            })
          }
        }
      })
    },
    async updateUserInfo(data) {
      const userLogin = await Api.userReg(data)
      this.setData('userInfo', userLogin.data)
      this.setData('token', userLogin.data.token)
    },
    async alipayUpdateUserInfo(data) {
      const userLogin = await Api.alipayUserReg(data)
      this.setData('userInfo', userLogin.data)
      this.setData('token', userLogin.data.token)
    },
    getLogin() {
      let check = false
      const loginStatus = this.getDataSafe('userInfo');
      const session_key = this.getDataSafe('session_key');
      if(loginStatus === null || session_key === null) {
        check = false;
      } else {
        // check = (Boolean(loginStatus.union_id) === false || Boolean(loginStatus.mem_id) === false) ? false : true
        check = (Boolean(loginStatus.union_id) === false) ? false : true
      }
      return check
    },
    async userLoginHide () {
      let check = false
      Taro.showLoading({
        mask: true,
        title: 'Loading'
      });
      if (Taro.getEnv() === Taro.ENV_TYPE.ALIPAY) { 
        const aliPromise = new Promise((resolve, reject) => {
          my.getAuthCode({
            scopes: 'auth_base',
            success: async res => {
              Taro.hideLoading();
              try {
                const openid = await Api.getAlipayUserCode({code: res.authCode, from: "alipay"})
                this.setData('session_key', {session_key: openid.data.session_key, endtime: openid.data.endtime})
                await this.alipayUpdateUserInfo({openid : openid.data.openid, unionid: openid.data.unionid, mem_id: null})
                resolve(true)
              } catch(e) {
                console.log(e)
                reject(false)
              }
            }
          })
        })
        return await aliPromise.then(result => {
          return result
        }).catch((err) => {
          return false
        })
      } else {
        const promise = new Promise((resolve, reject) => {
          Taro.login({
            success: async res => {
              Taro.hideLoading();
              try {
                const openid = await Api.getUserCode({code: res.code, from: "weixin"})
                this.setData('session_key', {session_key: openid.data.session_key, endtime: openid.data.endtime})
                // const zhMem = await Api.regZhMem({ unionid: openid.data.unionid, origin: 'NK'})
                // await this.updateUserInfo({openid : openid.data.openid, unionid: openid.data.unionid, mem_id: zhMem.data})
                await this.updateUserInfo({openid : openid.data.openid, unionid: openid.data.unionid, mem_id: null})
                resolve(true)
              } catch(e) {
                console.log(e)
                reject(false)
              }
            }
          })
        })
        return await promise.then(result => {
          return result
        }).catch((err) => {
          return false
        })
      }
    },
    relogin () {
      Taro.showModal({
        title: '您的登录状态已过期',
        content: '请重新登录后进行操作',
        confirmText: '立即登录',
        cancelText:  '暂不登录',
        success : async (res) => {
          if (res.confirm) {
            await this.userLoginHide()
          }
        }
      })
    },
    getDva : (namespace) => {
      const mapStateToProps = (state) => {
        const auth = state[namespace]
        return {
          auth
        }
      }
      return mapStateToProps;
    },
    async getPhoneNumber (data) {
      Taro.showLoading({
        mask: true,
        title: 'Loading'
      });
      const session_key = this.getDataSafe("session_key")
      const userInfo = this.getDataSafe("userInfo")
      const _self = this
      if (session_key === null) {
        this.relogin()
      }
      if(data.detail.errMsg === 'getPhoneNumber:ok') {
        Taro.checkSession({
          success: async () => {
            const phone = await Api.getPhone({
              ...data.detail,
              session_key : session_key.session_key
            })
            Taro.hideLoading();
            if (phone.data.check) {
              userInfo.user_tel = phone.data.purePhoneNumber
              this.setData("userInfo", userInfo)
              Taro.navigateTo({
                url: '/pages/bind/rel',
              });
            }
          },
          fail: function () {
            Taro.hideLoading();
            _self.relogin()
          }
        });
      } else {
        Taro.hideLoading();
      }
    },
    async getUserInfo (callback) {
      const userInfo = this.getDataSafe('userInfo');
      const session_key = this.getDataSafe('session_key');
      const { nickname , avatar } = userInfo
      if (nickname !== null && avatar !== null) {
        callback()
        return false
      }
      Taro.getUserProfile({
        desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
        success: async res => {
          if (userInfo !== null && session_key !== null) {
            const data = {
              openid: userInfo.code,
              all: {
                session_key: session_key.session_key,
                encryptedData: res.encryptedData,
                iv: res.iv
              }
            }
            await this.updateUserInfo(data)
            callback(true)
          }
        },
        fail: (res) => {
          callback(false)
        }
      })
    },
    goLogin: () => {
      Taro.showModal({
        title: '您还未登录',
        content: '请登录后进行操作',
        confirmText: '立即登录',
        cancelText:  '暂不登录',
        success (res) {
          if (res.confirm) {
            //todo
          }
        }
      })
    },  
    getDataSafe: key => {
      const data = Taro.getStorageSync(key);
      if (Boolean(data)) {
        return data;
      }
      return null;
    },
    getData: key => {
      const data = Taro.getStorageSync(key);
      if (Boolean(data)) {
        return data;
      }
      return {
        id: 0
      };
    },
    getSamll: (str, num) => {
        if (str === null) {
          return '';
        }
        try {
          return str.length > num ? str.slice(0, num) + '...' : str;
        } catch(e) {
          return 'error';
        }
    },
    getImageSize: async (url) => {
      let sizeData = null;
      await Taro.getImageInfo({
        src: url,
        success:(res) => {
          sizeData = res
        },
        fail: () => {
          sizeData = null
        }
      });
      return sizeData
    },
    createId: (l = 10) => {
        const x = '1234567890poiuytrewqasdfghjklmnbvcxz';
        let tmp = '';
        for (let i = 0; i < l; i++) {
            tmp += x.charAt(Math.ceil(Math.random() * 100000000) % x.length);
        }
        return tmp;
    },
    textBr(txt, type = 0) {
      const per = (type === 0) ? '<br/>' : ' ';
      return txt.replace(/\r\n/g, per).replace(/\n/g, per).replace(/\s/g, ' '); //转换格式
    },
    saveToAlbum : (img) => {
      Taro.saveImageToPhotosAlbum({
        filePath: img,
        success:  () => {
          Taro.showToast({
            title: '保存图片成功',
            icon: 'success',
            duration: 2000,
          });
        },
        fail: () => {
          Taro.showToast({
            title: '请在小程序设置中打开保存相册权限',
            icon: 'fail',
            duration: 2000,
          });
        }
      });
    },
    delTmpFile: () => {
      console.log(Taro.env.TEMP_DATA_PATH)
      const fsm = Taro.getFileSystemManager();
      fsm.readdir({dirPath: Taro.env.USER_DATA_PATH,
        success(res){
          console.log(res)
          res.files.forEach((el) => { // 遍历文件列表里的数据
            // 删除存储的垃圾数据
            if (el.includes('.png')) {
              fsm.unlink({
                filePath: `${wx.env.USER_DATA_PATH}/${el}`, // 这里注意。文件夹也要加上，如果直接文件名的话会无法找到这个文件
                fail(e){
                  console.log('readdir文件删除失败：',e)
                }
              });
            }
          })
        }
      })
    },
    findKeyword: (arr, data) => {
      return arr.reduce((prev, cur) => {
          if (data.includes(cur.id)) {
            prev.push(cur.title)
          }
          return prev
        }, [])
    },
    findMap: (data, keyword) => {
      return data.reduce((prev, cur) => {
        if (cur.title.includes(keyword)) {
          prev.push(cur)
        }
        return prev
      }, [])
    },
    arrSwich: (arr, val) => {
      if (!arr.includes(val)) {
        return [
          ...arr,
          val
        ];
      } else {
        const num = arr.indexOf(val);
        arr.splice(num, 1);
        return [
          ...arr
        ];
      }
    },
    setHttps: (url) => {
      if (Boolean(url)) {
        return url.includes('http://') ? url.replace('http://', 'https://') : url
      } else {
        return ''
      }
    },
    reSetH: (top) => {
      const res = Taro.getSystemInfoSync();
      const bet = top;
      const clientHeight = res.windowHeight;
      const height = clientHeight
      // const clientWidth = res.windowWidth;
      // const changeHeight = 750 / clientWidth;
      // const height = (clientHeight * changeHeight) - bet;
      return height + 210;
    },
    reTopH: (top = 13) => {
      const res = Taro.getSystemInfoSync();
      const clientHeight = res.statusBarHeight;
      const clientWidth = res.windowWidth;
      const changeHeight = 750 / clientWidth;
      const height = (clientHeight * changeHeight);
      return height + top;
    },
    reVideoH: (h, w) => {
      const videoFix = w / h;
      const res = Taro.getSystemInfoSync();
      const clientWidth = res.windowWidth;
      const height = parseInt((clientWidth / videoFix), 10);
      return height;
    },
    getSize() {
      const res = Taro.getSystemInfoSync();
      if (res.windowWidth < 375) {
        return '320'
      }
      return ''
    },
    getArea: (mode) => {
      if (mode === 0) {
        return 'width: 100%;height:1200rpx'
      } else if(mode === 1) {
        return 'width: 100%;height:600rpx'
      } else if(mode === 2) {
        return 'width: 100%;height:700rpx'
      }
    },
    getExf: (name) => {
      const exf = name.split('.');
      return (exf.length === 0) ? '.mp4111' : `.${exf[exf.length - 1]}`;
    },
    clearS: (string) => {
      try {
        return string.replace(/\n/g, ' ');
      } catch(e) {
        return string
      }
    },
    getDateDiff(time) {
      try {
        const nowTime = new Date();
        const day = nowTime.getDate();
        const hours = parseInt(nowTime.getHours(), 10);
        const minutes = nowTime.getMinutes();
        const timeyear = time.substring(0, 4);
        const timemonth = time.substring(5, 7);
        const timeday = time.substring(8, 10);
        const timehours = parseInt(time.substring(11, 13), 10);
        const timeminutes = time.substring(14, 16);
        const d_day = Math.abs(day - timeday);
        const d_hours = Math.abs(hours - timehours);
        const d_minutes = Math.abs(minutes - timeminutes);
        if (d_day > 1) {
          return timemonth + '-' + timeday;
        } else if (d_day === 0 && d_hours > 0 && d_hours <= 24) {
          return d_hours + '小时前';
        } else if (d_day === 1) {
          return '昨天';
        } else if (d_minutes > 0 && d_minutes <= 60) {
          return '刚刚';
        }
        return '刚刚';
      } catch(e) {
        return '';
      }
    },
    getPrice : (price, num = 2) => {
      if (isNaN(price)) {
        return price;
      }
      price = parseFloat(price);
      let _re = (Number.isInteger(price)) ? parseInt(price, 10) : parseFloat(price).toFixed(num);
      _re = _re.toString();
      if ( _re.charAt(_re.length - 1) === '0' && _re.includes('.')) {
         _re = _re.substring(0, _re.length - 1);
         if (_re.charAt(_re.length - 1) === '.') {
          _re = _re.substring(0, _re.length - 1);
         }
      }
      return _re;
    },
    urlToObj(str){
      let obj = {};
      const arr2 = str.split("&");
      for(let i=0 ; i < arr2.length; i++){
        const res = arr2[i].split("=");
        obj[res[0]] = res[1];
      }
      return obj;
    },
    async uploadImages(data, callback) {
      const uploadList = [];
      Taro.showLoading({
        mask: true,
        title: '上传中'
      });
      await data.forEach( async (val) => {
        await Taro.uploadFile({
          url: this.Api.baseUrl + 'uploadFiles',
          filePath: val,
          name: 'media',
          header: {
            'authorization' : this.getData('token'),
            'customize-authorize': this.creatSign("0tTu8etSrB971nwaktfT91gOzIHD", 'post', {}, "WehMeafRaFjtc2iZMpYEgglCcI3ZfoPh")
          },
          success (res){
            const result = JSON.parse(res.data)
            if (result.code === 200) {
              const resData = JSON.parse(res.data);
              uploadList.push(resData.data[0]);
              if(data.length === uploadList.length){
                Taro.hideLoading();
                callback(uploadList)
              }
            } else {
              Taro.hideLoading();
              Taro.showToast({
                title: '上传失败：图片含有敏感信息',
                icon: 'none',
                duration: 2000,
            });
            }
          }
        })
      });
    },
    removeCss(content) {
      let reg=/(style|class)="[^"]+"/gi;
      let img=/<img[^>]+>/gi;
      const regClass = new RegExp( 'class="' , "g" )
      const regstyle = new RegExp( 'style="' , "g" )
      let res;
      if(img.test(content))
      {
        res = content.match(img);
        for(let i=0;i<res.length;i++){
          content=content.replace(res[i],res[i].replace(reg,""));
        }
      }
      const removeHtml = content.replace(regstyle,'data-style="').replace(regClass,'data-none="').replace(/\<img/gi, '<img class="richImg" ');
      return `<div style="line-height: 25px">${removeHtml}</div>`
    },
    getWxNote(callback , type = 'neworder') {
      Taro.showLoading({
        title: '读取中'
      });
      let tid= ['3Q1GMvMyGNVxbRA2Qk-42MXHRBDiSEfcq_hlRfNxjm0'];
      if (Taro.canIUse('requestSubscribeMessage')) {
        Taro.getSetting({
          withSubscriptions: true,
          success (res) {
            if (!res.subscriptionsSetting.mainSwitch) {
              Taro.hideLoading();
              callback();
            } else {
              try {
                Taro.requestSubscribeMessage({
                  tmplIds: tid,
                  success: function (res) {
                    Taro.hideLoading();
                    callback();
                  }
                })
              } catch(e) {
                Taro.hideLoading();
                callback();
              }
            }
          }
        });
      }
    },
    txMap_to_bdMap : (lat,lng) => {
      const pi = 3.14159265358979324 * 3000.0 / 180.0;
      const x = lng;
      const y = lat;
      const z =Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * pi);
      const theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * pi);
      lng = z * Math.cos(theta) + 0.0065;
      lat = z * Math.sin(theta) + 0.006;
      return {lat, lng};
    },
    bdMap_to_txMap : (lat,lng) => {
      const pi = 3.14159265358979324 * 3000.0 / 180.0;
      const x = lng - 0.0065;
      const y = lat - 0.006;
      const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * pi);
      const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * pi);
      lng = z * Math.cos(theta);
      lat = z * Math.sin(theta);
      return {lat, lng};
   },
   dateInfo : (stime, etime, enable_week) => {
     const weekStr = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
     if (stime === '0' && etime === '0') {
       const out = [];
       const week_arr = enable_week.split(',');
       week_arr.map(val => out.push(weekStr[parseInt(val, 10)]))
       return `${out.join('，')}可用`;
     }
     return `${moment(parseInt(stime, 10) * 1000).format('MM-DD')} 至 ${moment(parseInt(etime, 10) * 1000).format('MM-DD')}可用`
   },
  async addNoteList(data, callback = () => {}) {
    const can = data.reduce((prev, cur) => {
      if (cur.had === false) {
        prev.push({
          coupon_id: cur.id,
          cash: cur.cash,
          user_id: this.getDataSafe('userInfo').id || 0
        })
        cur.had = true;
      }
      return prev
    }, []);
    const mlist = ['G1DCV-HjVlxGsCAPcb6N-ek7ASl_QWrbIAXYoJkUJ5s', 'eaLH6aQHL72rl0eROCPLi99PNUX-rV0w2nh8QPFvx4M']
    wx.requestSubscribeMessage({
      tmplIds: mlist,
      success : async (res) =>  {
        //todo
      }
    })
    return true
  },
  creatSign (accessKeyId, method, data = {}, accessKey)  {
    const timestamp = (() => {
      return parseInt(Date.parse(new Date()) / 1000, 10)
    })()
    const nonce = `${this.createId(12)}`;
    const stringToSignArray = [
      `accessKeyId=${accessKeyId}`,
      `nonce=${nonce}`,
      `timestamp=${timestamp}`,
      `method=${method.toUpperCase()}`,
      `key=${accessKey}`
    ]
    stringToSignArray.push(`content=${hex_md5(encodeURI(JSON.stringify(data))).toUpperCase()}`);
    const stringToSign = stringToSignArray.sort();
    // console.log(encodeURI(JSON.stringify(data)))
    const signStr = hex_md5(stringToSign.join('&_*')).toString().toUpperCase()
    return [
      `accessKeyId=${accessKeyId}`,
      `nonce=${nonce}`,
      `timestamp=${timestamp}`,
      `signature=${signStr}`
      ].join(';')
  },
  getLocation : () => {
    return new Promise((resolve, reject) => {
        let _locationChangeFn = (res) => {
            Taro.offLocationChange(_locationChangeFn)
            Taro.stopLocationUpdate()
            resolve(res)
        }
        Taro.startLocationUpdate({
            success: (res) => {
              Taro.onLocationChange(_locationChangeFn)
            },
            fail: (err) => {
                // Taro.openSetting({
                //     success(res) {
                //         res.authSetting = {
                //             "scope.userLocation": true
                //         }
                //     }
                // })
                reject(err)
            }
        })
    })
  },
  brandRank: num => {
    let str = '优质景区'
    const temp = []
    if (num >= 3) {
        for (let i = 0; i < num; i++) {
          temp.push('A')
        }
        str=`${temp.join('')}级景区`
    }
    return str
  },
  brandSpace: num => {
    return (num === 'IN') ? "室内" :  (num === 'OUT') ? "室外" : "室内+室外" 
  },
  async getCardList (params = []) {
      const data = await this.Api.myCardList(params)
      if (data.code === 200) {
        return data.data
      }
  },
  cardCheck (cardList, type = 'all') {
    return (type === 'all') ?  cardList.filter(v => v.enable === true)  : cardList.filter(v => v.enable_now === true)
  },
  async appConfig (type) {
    const data = await this.Api.appConfig()
    if (data.code === 200) {
      switch (type) {
        case 'buy':
          Taro.navigateToMiniProgram(data.data.buyCard)
        case 'menu':
          Taro.navigateToMiniProgram(data.data.menu)
        case 'order':
          Taro.navigateToMiniProgram(data.data.order)  
        default:
          return data.data
      }
    }
    return null
  },
  getlimt : (type, num) =>{
    if (type === '1') {
      return `提前${num}小时`;
    } else if (type === '2') {
      return `提前${num}天`;
    } else if (type === '3') {
      return (num === '0') ? '当日发货' : (num === '1') ? '次日发货' : `${num}日后发货`;
    }
    if (type === '0' && num === '-1') {
      return '预售抢购';
    }
    if (type === '0' && num === '-2') {
      return '预约使用';
    }
    return '随买随用';
  },
  showUseDate : (is_book, open_time, close_time) => {
    if (parseInt(is_book, 10) === 1){
      return 0;
    } else {
      return parseInt(moment(close_time).diff(moment(open_time), 'day'), 10) >= 180 ? 1 : -1;
    }
  },
  dateInfo : (stime, etime, enable_week) => {
    const weekStr = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    if (stime === '0' && etime === '0') {
      const out = [];
      const week_arr = enable_week.split(',');
      week_arr.map(val => out.push(weekStr[parseInt(val, 10)]))
      return `${out.join('，')}可用`;
    }
    return `${moment(parseInt(stime, 10) * 1000).format('MM-DD')} 至 ${moment(parseInt(etime, 10) * 1000).format('MM-DD')}可用`
  },
  IdentityCodeValid(idCard) {
    const regIdCard = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
    return regIdCard.test(idCard);
  },
}

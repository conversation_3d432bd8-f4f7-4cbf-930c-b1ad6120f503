{"version": 3, "file": "pages/index/index.js", "sources": ["webpack:///./src/pages/index/index.jsx", "webpack:///./src/components/brand/index.jsx", "webpack:///./src/components/memu/top.jsx", "webpack:///./src/pages/index/index.jsx?d46f", "webpack:///./src/pages/index/index.scss"], "sourcesContent": ["import { useState, useEffect } from 'react'\r\nimport Taro, { usePageScroll, useShareAppMessage } from '@tarojs/taro';\r\nimport { useSelector } from 'react-redux'\r\nimport { View, Text, Image, Swiper, SwiperItem } from '@tarojs/components'\r\nimport BrandIndex from '@/components/brand'\r\nimport Memu from '@/components/memu'\r\nimport Top from '@/components/memu/top'\r\nimport { AtGrid, AtToast } from \"taro-ui\"\r\nimport tools from '@/utils/tools'\r\nimport './index.scss'\r\n\r\n\r\nfunction Index (props) { \r\n  const [isOpened, setIsOpened] = useState(false)\r\n  const [blurVal, setBlurVal] = useState(0)\r\n  const login = useSelector(state => state.login)\r\n  const [init, setInit] = useState(null)\r\n  const [loading, setLoading] = useState(true)\r\n  const [btnStatus, setBtnStatus] = useState('LOAD')\r\n  const [buyList , setBuyList] = useState({\r\n    list: [],\r\n    title: \"年票福利\",\r\n    code: 0,\r\n    type: 'buy'\r\n  })  \r\n  const loadData = async () => {\r\n    // Taro.showLoading({\r\n    //   mask: true,\r\n    //   title: '读取中'\r\n    // })\r\n    const data = await tools.Api.index({\r\n      line_id : tools.line_code,\r\n      order : \"km\",\r\n      point : true,\r\n      page : {\r\n          \"current\": 1,\r\n          \"pageSize\" : 5\r\n      }\r\n    })\r\n    //Taro.hideLoading()\r\n    return data\r\n  }\r\n\r\n  useEffect( async () => {\r\n    if (login) {\r\n      const userInfo = tools.getDataSafe('userInfo');\r\n      const point = tools.getDataSafe('mypoint')\r\n      const buyItem = {\"admin\":\"buyer\",\"showlist\":1,\"from\":\"fe\",\"page\":0,\"enter_user_id\":0,\"agent_id\":1,\"pageSize\":8,\"currentPage\":1,\"buyer\":userInfo.mem_id,\"order\":\"a.ordernum\",\"ordertype\":\"desc\",\"range\":{\"latitude\":point.api.latitude,\"longitude\":point.api.longitude,\"val\":5000}}\r\n      // const buyData = await tools.Api.brandBuyList(buyItem)\r\n      // if (buyData.code === 0) {\r\n      //   setBuyList({\r\n      //     ...buyList,\r\n      //     list: buyData.list\r\n      //   })\r\n      // }\r\n      const data = await loadData()\r\n      const cardStatus = await tools.getCardList()\r\n      setBtnStatus(cardStatus.status)\r\n      if (data.code === 200) {\r\n        setInit(data.data)\r\n        setLoading(false)\r\n      }\r\n    }\r\n  }, [login])\r\n\r\n  useShareAppMessage(() => {\r\n    return {\r\n      title: '北京风景名胜年票',\r\n      path: '/pages/index/index',\r\n      imageUrl: 'https://cardall.oss-cn-beijing.aliyuncs.com/card/2021/12/1640067294_4f8NtU3L.jpg'\r\n    }\r\n  })\r\n\r\n  usePageScroll(res => {\r\n    if (res.scrollTop >= 100) {\r\n      setBlurVal(25)\r\n    }\r\n    if (res.scrollTop < 100) {\r\n      setBlurVal(0)\r\n    }\r\n  })\r\n\r\n  return (\r\n    <View>\r\n        {(init === null) ?  <AtToast isOpened text=\"请稍等\" status=\"loading\" duration={0}></AtToast> :\r\n        <View>\r\n        <Top bg={blurVal}/>\r\n        <View className=\"top_pic\" style={{filter: `blur(${blurVal}px)`}}>\r\n          <Swiper\r\n            className='index_swiper'\r\n            indicatorColor='#999'\r\n            indicatorActiveColor='#333'\r\n            circular\r\n            indicatorDots\r\n            autoplay>\r\n            {\r\n              init.imgList.trunList.map(val => <SwiperItem onClick={() => {\r\n                Taro.navigateTo({\r\n                  url : `/pages/brand/index?v=${val.link_code}`\r\n                })\r\n              }}>\r\n                <Image src={val.image} mode=\"aspectFill\" className=\"images\"/>\r\n              </SwiperItem>)\r\n            }\r\n          </Swiper>\r\n          {/*<Image src={`${tools.picUrl}/index/bg4.png`}  className=\"images_bottom\" mode=\"widthfix\"/>*/}\r\n        </View>\r\n        \r\n        <View className=\"index_center\">\r\n          <View className=\"nav\">\r\n            <AtGrid hasBorder={false} columnNum={4} data={\r\n                [\r\n                  {\r\n                    image: `${tools.picUrl}/index/card.png`,\r\n                    value: '购买年票',\r\n                    page: 'buy/index'\r\n                  },\r\n                  {\r\n                    image: `${tools.picUrl}/index/plan.png`,\r\n                    value: '入园记录',\r\n                    page: 'my/use'\r\n                  },\r\n                  {\r\n                    image: `${tools.picUrl}/index/book.png`,\r\n                    value: '景区预约',\r\n                    page: 'sreach/book'\r\n                  },\r\n                  {\r\n                    image: `${tools.picUrl}/index/news.png`,\r\n                    value: '最新通知',\r\n                    page: 'news/index'\r\n                  }\r\n                ]\r\n              }  onClick={ async (item) => {\r\n                if (item.page === 'tel') {\r\n                  Taro.makePhoneCall({\r\n                    phoneNumber: tools.phone\r\n                  })\r\n                } else if (item.page === 'card') {\r\n                  // await tools.appConfig('buy')\r\n                } else {\r\n                  Taro.navigateTo({\r\n                    url: `/pages/${item.page}`\r\n                  })\r\n                }\r\n              }}/>\r\n          </View>\r\n          {<View className='btn' style={{fontWeight: 'bold'}} onClick={() => {\r\n            Taro.navigateTo({\r\n              url: (btnStatus === 'BOOK') ? '/pages/qr/index' : '/pages/bind/index'\r\n            })\r\n          }}>{(btnStatus === 'BOOK') ? '我的入园码' : '激活年票'}</View>}\r\n          <Swiper\r\n            className='swiper'\r\n            indicatorColor='#999'\r\n            indicatorActiveColor='#333'\r\n            circular\r\n            interval= {8000}\r\n            autoplay>\r\n            {\r\n              init.imgList.topicList.map(val => <SwiperItem>\r\n                <Image src={val.image} mode=\"aspectFill\" className=\"images\" onClick={() => {\r\n                  Taro.navigateTo({\r\n                    url : `/pages/sreach/topic?v=${val.link_code}`\r\n                  })\r\n                }}/>\r\n              </SwiperItem>)\r\n            }\r\n          </Swiper>\r\n        </View>\r\n        <View className=\"content_index\">\r\n        {\r\n              init.brandList.map(val =>   {\r\n                val.type = \"book\";\r\n                return (val.list.length > 1) ? <BrandIndex data={val}/> : null\r\n              })\r\n          }\r\n         \r\n        </View>\r\n        <Memu now={0}/>\r\n        </View>\r\n      }\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2022-07-21 23:34:26\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/brand/index.jsx\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport { View, Text, Image, Swiper, SwiperItem } from '@tarojs/components'\r\nimport Taro, { getEnv, ENV_TYPE } from '@tarojs/taro'\r\nimport BrandItem from './item'\r\nimport BrandBuyItem from './itembuy'\r\nimport { AtIcon } from \"taro-ui\"\r\nimport \"./index.scss\"\r\n\r\nconst BrandIndex = (props) => {\r\n    const { data : { list, title, code, type} } = props\r\n\r\n    // 根据平台设置不同的 displayMultipleItems 值\r\n    const getDisplayMultipleItems = () => {\r\n        const env = getEnv()\r\n        if (env === ENV_TYPE.ALIPAY) {\r\n            // 支付宝小程序使用整数值避免重叠问题\r\n            return 1\r\n        }\r\n        // 微信小程序等其他平台使用 1.1 显示预览效果\r\n        return 1.1\r\n    }\r\n\r\n    return (\r\n        <View>\r\n            <View className='index_brand_top'>\r\n                <View className='at-row'>\r\n                    <View className='at-col at-col-6 title'>{title}</View>\r\n                    <View className='at-col at-col-6 more' onClick={() => {\r\n                        Taro.navigateTo({\r\n                            url: (type === 'buy') ? '/pages/sreach/listbuy' : `/pages/sreach/type?v=${code}`\r\n                        })\r\n                    }}>全部<AtIcon value='chevron-right' size='18' color='#FFF'></AtIcon></View>\r\n                </View>\r\n            </View>\r\n            <Swiper\r\n              className='swiper'\r\n              circular={false}\r\n              indicatorDots={getEnv() === ENV_TYPE.ALIPAY && list.length > 1}\r\n              autoplay={false}\r\n              displayMultipleItems={getDisplayMultipleItems()}\r\n            >\r\n              {list.map((val, index) =>\r\n                <SwiperItem\r\n                  key={index}\r\n                  className={getEnv() === ENV_TYPE.ALIPAY ? '' : 'swiper-item-with-margin'}\r\n                >\r\n                  {type === 'buy' ? <BrandBuyItem data={val} cname='images' /> : <BrandItem data={val} cname='images' />}\r\n                </SwiperItem>\r\n              )}\r\n            </Swiper>\r\n\r\n        </View>\r\n    )\r\n}\r\nexport default BrandIndex ", "/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2021-12-24 21:57:44\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/memu/top.jsx\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport { View, Text, Image, Swiper, SwiperItem } from '@tarojs/components'\r\nimport Taro from '@tarojs/taro'\r\nimport tools from '@/utils/tools'\r\n\r\nconst Top = (props) => {\r\n    return (\r\n        <View className=\"topNav\" style={{display: \"none\", backgroundColor: (props.bg === 0) ? `#c5071100` : '#c50711'}}>\r\n            <View style={{height:  `${tools.reTopH(6)}rpx`}}></View>\r\n            <View className='at-row'>\r\n                <View className='at-col at-col-1 left' onClick={() => {\r\n                    Taro.navigateTo({\r\n                        url: '/pages/sreach/index'\r\n                    })\r\n                }}>\r\n                    <Image src={`${tools.picUrl}/memu/find.png`} mode='widthFix' className=\"topIcon\"/>\r\n                </View>\r\n                <View className='at-col at-col-2' onClick={() => {\r\n                    Taro.showToast({\r\n                        title: '快速入园，敬请期待',\r\n                        icon: 'none',\r\n                        duration: 2000\r\n                      })\r\n                }}>\r\n                    <Image src={`${tools.picUrl}/memu/scan.png`} mode='widthFix' className=\"topIcon\" style={{marginLeft: '15px'}}/>\r\n                </View>\r\n                <View className='at-col at-col-8'></View>\r\n            </View>\r\n        </View>\r\n    )\r\n}\r\nexport default Top ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.jsx\"\nvar config = {\"navigationBarTitleText\":\"\",\"navigationStyle\":\"custom\",\"enableShareAppMessage\":true,\"transparentTitle\":\"always\",\"titlePenetrate\":\"YES\"};\n\ncomponent.enableShareAppMessage = true\nvar inst = Page(createPageConfig(component, 'pages/index/index', {root:{cn:[]}}, config || {}))\n\n", "// extracted by mini-css-extract-plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AALA;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AARA;AAAA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAhBA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAEA;AAIA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAEA;AAEA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AAIA;AAAA;AAAA;AACA;AAIA;AACA;;;;;;;;;;;;;ACzLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAGA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAIA;AACA;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AACA;AAGA;AACA;;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA;;;;A", "sourceRoot": ""}
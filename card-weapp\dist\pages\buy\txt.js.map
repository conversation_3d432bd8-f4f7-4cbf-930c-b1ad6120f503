{"version": 3, "file": "pages/buy/txt.js", "sources": ["webpack:///./src/pages/buy/txt.jsx", "webpack:///./src/pages/buy/txt.jsx?e894"], "sourcesContent": ["import { Component } from 'react'\r\nimport { View, Text, Button } from '@tarojs/components'\r\nimport { AtIcon } from \"taro-ui\"\r\n\r\nfunction Index (props) { \r\n  return (\r\n    <View className='index' style={{padding: \"20px\", color: \"#666\"}}>\r\n        <View style={{textAlign: \"center\", fontWeight: 'bold', marginBottom: \"15px\"}}>《用户授权协议》</View>\r\n        <View style={{marginBottom: '15px'}}>《年票线上购买协议》（以下简称“本协议”）是北京市风景名胜区协会（以下简称“协会”）就北京风景名胜年票线上售票一事，与用户（以下简称“您”）所订立的有效合约。请您在签署同意字样前，详细阅读本协议内容。如您对本协议内容或页面提示信息有疑问，请勿进行下一步操作。您可通过协会在线客服或官方热线 4006091798 进行咨询，以便协会为您解释和说明。如您通过页面点击或协会认可的其他方式确认本协议即表示您已同意本协议条款。\r\n</View>\r\n        <View style={{marginBottom: '15px'}}>1、北京风景名胜年票由北京市风景名胜区协会发行，当年度所售年票（实体卡、电子卡）均为本年度内使用，不含保险；</View>\r\n        <View style={{marginBottom: '15px'}}>2、年票仅限本人使用，年票激活时需上传本人免冠照片，进入景区验证时如照片与本人不符或非本人使用，景区有权拒绝游客进入景区，年票使用权予以作废；</View>\r\n        <View style={{marginBottom: '15px'}}>3、在线购买的年票（电子卡）需激活后使用，请遵守景区相关规定和要求，正确使用年票；</View>\r\n        <View style={{marginBottom: '15px'}}>4、如景区因特殊原因临时关闭，请以公众号通知或景区公示为准；</View>\r\n        <View style={{marginBottom: '15px'}}>5、景区资讯请关注公众号：“北京风景名胜年票”；</View>\r\n        <View style={{marginBottom: '15px'}}>6、特殊景区入园政策详见公众号景区目录查看；</View>\r\n        <View style={{marginBottom: '15px'}}>7、本年票最终解释权协会所有；</View>\r\n        <View style={{marginBottom: '15px'}}>8、为方便您通过第三方服务，完成线上年票购买，以下条款需得到您的同意并授权：</View>\r\n        <View style={{marginBottom: '15px'}}>（1）您同意协会将您的姓名、证件号、手机号及页面提示的相关信息传递给第三方。页面提示上会展示具体授权对象以及授权信息类型，您的信息将通过加密通道传递给第三方。协会会要求第三方严格遵守相关法律法规与监管要求，依法使用您的信息，并对您的信息保密。点击授权之后，授权关系长期有效，直至您主动解除。为了更好地保护您的信息安全，协会采用了信息查询令牌技术。授权有效期内，当您使用被授权方服务时，将激活令牌，被授权方仅在令牌激活期内方可查询您的信息。激活期届满后，令牌将暂时失效，直至您再次使用被授权方服务时被激活。</View>\r\n        <View style={{marginBottom: '15px'}}>（2）协会是经北京市民政局正式注册的社会团体，上述第三方服务由该第三方独立运营并独立承担全部责任。因第三方服务或其使用您的信息而产生的纠纷，或第三方服务违反相关法律法规或协议约定，或您在使用第三方服务过程中遭受损失的，请您和第三方协商解决。</View>\r\n        <View style={{marginBottom: '15px'}}>（3）如协会对本协议进行变更，协会将通过公告或客户端消息等方式予以通知，该等变更自通知载明的生效时间开始生效。若您无法同意变更修改后的协议内容，您有权停止使用相关服务；双方协商一致的，也可另行变更相关服务和对应协议内容。</View>\r\n        <View style={{marginBottom: '15px'}}>（4）本协议之效力、解释、变更、执行与争议解决均适用中华人民共和国法律。因本协议产生的争议，均应依照中华人民共和国法律予以处理，并由被告住所地人民法院管辖。</View>\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./txt.jsx\"\nvar config = {\"navigationBarTitleText\":\"用户授权协议\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/buy/txt', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;ACzBA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-07-21 23:24:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/listbuy.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import { useSelector } from 'react-redux'
import Taro, { useReachBottom } from '@tarojs/taro'
import Memu from '@/components/memu'
import None from '@/components/tips/none'
import SreachTools from './bar'
import Item from '@/components/brand/itembuy'
import tools from '@/utils/tools'
import "./index.scss"

const SreachIndex = (props) => {
    const query = tools.getQuery()
    const login = useSelector(state => state.login)
    const point = tools.getDataSafe('mypoint')
    const [init, setInit] = useState(null)
    const [config, setConfig] = useState(false)
    const [page , setPage] = useState(1)
    const [params , setParams] = useState({
      order: "km",
      ordertype: "asc",
      range: {
        latitude: point.api.latitude,
        longitude: point.api.longitude,
        val: 5000
      }
    })
    const [brandList , setBrandList] = useState([])  
    const [word, setWord] = useState(query.data.v)
    const [loaded, setLoaded] = useState(false)


    useEffect( async () => {
        if (login) {
          
          const selData = await tools.Api.brandBuy({
            latitude: point.api.latitude,
            longitude: point.api.longitude
          })
          if (selData.code === 200) {
            setConfig(selData.data)
          }
        }
    }, [login])

    useEffect( async () => {
        setLoaded(false)
        console.log(params)
        const userInfo = tools.getDataSafe('userInfo');
        Taro.showLoading({
          mask: true,
          title: '请稍等'
        })
        if (login && config) {
          // const apiParams = {
          //   line_id: tools.line_code,
          //   order : "km",
          //   point : true,
          //   page : {
          //       current: page,
          //       pageSize : 20
          //   },
          //   ...params
          // }
          const apiParams = {
            admin: "buyer",
            showlist: 1,
            from: "fe",
            page: page - 1,
            enter_user_id: 0,
            agent_id: 1,
            pageSize: 20,
            currentPage: page,
            buyer: userInfo.mem_id,
            ...params
          }
          if (Boolean(word)) {
            apiParams.keywords = word
          }
          const data = await tools.Api.brandBuyList(apiParams)
          if (data.code === 0) {
            setInit(data.pagination)
            setBrandList([
                ...brandList,
                ...data.list
            ])
          }
          setLoaded(true)
          Taro.hideLoading()
        }
    }, [login, page, params, word, config])

    useReachBottom(() => {
        const nextPage = init.current + 1
        if (nextPage <= init.totalPage) {
          setPage(nextPage)
        } else {
          Taro.showToast({
            title: '暂无更多内容',
            icon: 'none',
            duration: 2000
          })
        }
    })

    const configParams = data => {
      let formatData = {}
      if (Boolean(data.orderVal)) {
        formatData.orderVal = data.orderVal[0]
      }
      if (Boolean(data.range)) {
        formatData.range = data.range[0]
      }
      if (Boolean(data.order)) {
        formatData =  {
          ...formatData,
          ...data.order
        }
      }
      setParams({
        ...params,
        ...formatData
      })
    }

    return (
        <View>
         {config ? <SreachTools config={config} callBack={data => {
            setBrandList([])
            configParams(data)
            setPage(1)
          }} setkey = {(v) => {
            setBrandList([])
            setWord(v)
            setPage(1)
          }} init={word}/> : null}
          <View>
            {
                (brandList.length > 0) ?  brandList.map(v => <Item className="list" data={v} cname="images_l"/>) : <None loaded={loaded}/>
            }
          </View>
          <Memu now={10} />
        </View>
    )
}
export default SreachIndex 
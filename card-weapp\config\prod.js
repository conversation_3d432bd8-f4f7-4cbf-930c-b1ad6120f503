/*
 * @Author: 高超
 * @Date: 2021-10-30 10:32:40
 * @LastEditTime: 2022-07-20 09:49:16
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/config/prod.js
 * love jiajia
 */

// 支付宝 appid: "2021004133667106"
// 微信 appid: "wx99dcb5f0415eb002"

module.exports = {
  env: {
    NODE_ENV: '"production"'
  },
  appid: "wx99dcb5f0415eb002",
  baseUrl: "https://test.qqyhmmwg.com/weapp/",
  zhbaseUrl: "https://zhlx.dfxpw.cn/dfx/index.php/api/",
  zhnodeUrl: "https://vlog.dfxpw.cn/nick/",
  defineConstants: {
  },
  mini: {},
  h5: {
    /**
     * 如果h5端编译后体积过大，可以使用webpack-bundle-analyzer插件对打包体积进行分析。
     * 参考代码如下：
     * webpackChain (chain) {
     *   chain.plugin('analyzer')
     *     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
     * }
     */
  }
}

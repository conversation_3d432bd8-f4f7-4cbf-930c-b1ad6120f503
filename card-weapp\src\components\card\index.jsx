/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-17 17:41:14
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/card/index.jsx
 */
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import moment from 'moment';
import "./index.scss"

const Card = (props) => {
    const { data , oper} = props;
    return (
       <View className="cardlayout" style= {{position: 'relative'}} onClick={() => {
            if (data.enable) {
                if (<PERSON><PERSON><PERSON>(oper)) {
                    oper(data.good_id, data.real_name)
                } else {
                    Taro.navigateTo({
                        url: `/pages/qr/index?code=${data.good_id}`
                    })
                }
            }
       }}>  
           {
               (data.enable === false) ? <View style={{position: 'absolute', width: '100%', height: '100%', backgroundColor: '#777272de', top: 0, left: 0, borderRadius: '7px', textAlign:'center', lineHeight: '100px', fontWeight: 'bold'}}>{data.enable_str}</View> : null
           }
           
           <View className='at-row'>
               <View className='at-col at-col-4 number' style={{textAlign: 'left'}}>
                    {/* <Text style={{border: '1px solid #ccc', padding: '2px', display: 'inline-block', marginLeft: '6px', minWidth: '35px', textAlign: 'center', borderRadius: '5px'}}>{data.enable_str}</Text>  */}
               </View>
               <View className='at-col at-col-8 number' style={{textAlign: 'right'}}>
                    <Text style={{paddingRight: '5px'}}>{data.real_name}</Text> No.{data.good_id}
               </View>
           </View>
           <View className='at-row'>
            <View className='at-col at-col-2' style={{textAlign: 'right'}}>
                <Image src={data.user_img} className="logo" mode='aspectFill'/>
            </View>
            <View className='at-col at-col-9' style={{marginLeft: "10px"}}>
                <View className="title">{data.card_name}</View>
                <View className="date">有效期: {moment(data.enable_start_time_unix * 1000).format("YYYY年MM月DD日")} 至 {moment(data.enable_end_time_unix * 1000).format("YYYY年MM月DD日")}</View>
            </View>
           </View>
           <View className="bottom"></View>
       </View>
    )
}
export default Card 
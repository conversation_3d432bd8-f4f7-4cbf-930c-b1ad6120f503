{"version": 3, "file": "pages/sreach/topic.js", "sources": ["webpack:///./src/pages/sreach/topic.jsx", "webpack:///./src/pages/sreach/topic.jsx?0e2d"], "sourcesContent": ["/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2021-12-18 00:28:12\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/topic.jsx\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport { View, Image } from '@tarojs/components'\r\nimport { useSelector } from 'react-redux'\r\nimport Taro, { useReachBottom } from '@tarojs/taro'\r\nimport Memu from '@/components/memu'\r\nimport None from '@/components/tips/none'\r\nimport SreachTools from './bar'\r\nimport Item from '@/components/brand/item'\r\nimport tools from '@/utils/tools'\r\nimport \"./index.scss\"\r\n\r\nconst SreachIndex = (props) => {\r\n    const query = tools.getQuery()\r\n    const login = useSelector(state => state.login)\r\n    const [init, setInit] = useState(null)\r\n    const [page , setPage] = useState(1)\r\n    const [brandList , setBrandList] = useState([])  \r\n    const [loaded, setLoaded] = useState(false)\r\n    const [title, setTitle] = useState(null)\r\n    useEffect( async () => {\r\n        setLoaded(false)\r\n        Taro.showLoading({\r\n          mask: true,\r\n          title: '请稍等'\r\n        })\r\n        if (login && query.data.v) {\r\n          const apiParams = {\r\n            point : true,\r\n            code : query.data.v,\r\n          }\r\n          const data = await tools.Api.topicInfo(apiParams)\r\n          if (data.code === 200) {\r\n            setBrandList(data.data.list)\r\n            setTitle(data.data.topic)\r\n            Taro.setNavigationBarTitle({\r\n              title: `${data.data.topic.name}`\r\n            })\r\n          }\r\n          setLoaded(true)\r\n          Taro.hideLoading()\r\n        }\r\n    }, [login, page])\r\n\r\n    useReachBottom(() => {\r\n        const nextPage = init.current + 1\r\n        if (nextPage <= init.totalPage) {\r\n          setPage(nextPage)\r\n        } else {\r\n          Taro.showToast({\r\n            title: '暂无更多内容',\r\n            icon: 'none',\r\n            duration: 2000\r\n          })\r\n        }\r\n    })\r\n\r\n    return (\r\n        <View>\r\n          {\r\n            (title !== null) ? <View>\r\n              <Image src={title.image} mode='widthFix' style={{width: '100%', marginBottom: '20px'}}/>\r\n            </View> : null\r\n          }\r\n          \r\n          <View>\r\n            {\r\n                (brandList.length > 0) ?  brandList.map(v => <Item className=\"list\" data={v} cname=\"images_l\"/>) : <None loaded={loaded}/>\r\n            }\r\n          </View>\r\n          <Memu now={3} />\r\n        </View>\r\n    )\r\n}\r\nexport default SreachIndex ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./topic.jsx\"\nvar config = {\"navigationBarTitleText\":\"景区专题\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/sreach/topic', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
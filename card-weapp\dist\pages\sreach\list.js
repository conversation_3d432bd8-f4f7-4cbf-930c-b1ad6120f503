(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/sreach/list"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/sreach/list.jsx":
/*!*******************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/sreach/list.jsx ***!
  \*******************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _components_memu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/memu */ "./src/components/memu/index.jsx");
/* harmony import */ var _components_tips_none__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/tips/none */ "./src/components/tips/none.jsx");
/* harmony import */ var _bar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./bar */ "./src/pages/sreach/bar.jsx");
/* harmony import */ var _components_brand_item__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/brand/item */ "./src/components/brand/item.jsx");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./index.scss */ "./src/pages/sreach/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__);





/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-08-03 18:05:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/list.jsx
 */












var SreachIndex = function SreachIndex(props) {
  var query = _utils_tools__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"].getQuery();
  var login = Object(react_redux__WEBPACK_IMPORTED_MODULE_7__[/* useSelector */ "c"])(function (state) {
    return state.login;
  });
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_5__["useState"])(null),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])(_useState, 2),
    init = _useState2[0],
    setInit = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_5__["useState"])(false),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])(_useState3, 2),
    config = _useState4[0],
    setConfig = _useState4[1];
  var _useState5 = Object(react__WEBPACK_IMPORTED_MODULE_5__["useState"])(1),
    _useState6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])(_useState5, 2),
    page = _useState6[0],
    setPage = _useState6[1];
  var _useState7 = Object(react__WEBPACK_IMPORTED_MODULE_5__["useState"])({}),
    _useState8 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])(_useState7, 2),
    params = _useState8[0],
    setParams = _useState8[1];
  var _useState9 = Object(react__WEBPACK_IMPORTED_MODULE_5__["useState"])([]),
    _useState10 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])(_useState9, 2),
    brandList = _useState10[0],
    setBrandList = _useState10[1];
  var _useState11 = Object(react__WEBPACK_IMPORTED_MODULE_5__["useState"])(query.data.v),
    _useState12 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])(_useState11, 2),
    word = _useState12[0],
    setWord = _useState12[1];
  var _useState13 = Object(react__WEBPACK_IMPORTED_MODULE_5__["useState"])(false),
    _useState14 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"])(_useState13, 2),
    loaded = _useState14[0],
    setLoaded = _useState14[1];
  Object(react__WEBPACK_IMPORTED_MODULE_5__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee() {
    var selData;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (!login) {
            _context.next = 5;
            break;
          }
          _context.next = 3;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"].Api.brandCity({
            line_id: _utils_tools__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"].line_code
          });
        case 3:
          selData = _context.sent;
          if (selData.code === 200) {
            setConfig(selData.data);
          }
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [login]);
  Object(react__WEBPACK_IMPORTED_MODULE_5__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().mark(function _callee2() {
    var apiParams, data;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          setLoaded(false);
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default.a.showLoading({
            mask: true,
            title: '请稍等'
          });
          if (!(login && config)) {
            _context2.next = 11;
            break;
          }
          apiParams = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({
            line_id: _utils_tools__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"].line_code,
            order: "km",
            point: true,
            page: {
              current: page,
              pageSize: 20
            }
          }, params);
          if (Boolean(word)) {
            apiParams.keywords = word;
          }
          _context2.next = 7;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"].Api.brandList(apiParams);
        case 7:
          data = _context2.sent;
          if (data.code === 200) {
            setInit(data.data.page);
            setBrandList([].concat(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(brandList), Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(data.data.list)));
          }
          setLoaded(true);
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default.a.hideLoading();
        case 11:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [login, page, params, word, config]);
  Object(_tarojs_taro__WEBPACK_IMPORTED_MODULE_8__["useReachBottom"])(function () {
    var nextPage = init.current + 1;
    if (nextPage <= init.totalPage) {
      setPage(nextPage);
    } else {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default.a.showToast({
        title: '暂无更多内容',
        icon: 'none',
        duration: 2000
      });
    }
  });
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
    children: [config ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_bar__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"], {
      config: config,
      callBack: function callBack(data) {
        setBrandList([]);
        setParams(data);
        setPage(1);
      },
      setkey: function setkey(v) {
        setBrandList([]);
        setWord(v);
        setPage(1);
      },
      init: word
    }) : null, /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      children: brandList.length > 0 ? brandList.map(function (v) {
        return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_components_brand_item__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"], {
          className: "list",
          data: v,
          cname: "images_l"
        });
      }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_components_tips_none__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"], {
        loaded: loaded
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_components_memu__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], {
      now: 2
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (SreachIndex);

/***/ }),

/***/ "./src/pages/sreach/list.jsx":
/*!***********************************!*\
  !*** ./src/pages/sreach/list.jsx ***!
  \***********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_list_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./list.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/sreach/list.jsx");


var config = {"navigationBarTitleText":"精选景区"};


var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_list_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/sreach/list', {root:{cn:[]}}, config || {}))



/***/ })

},[["./src/pages/sreach/list.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=list.js.map
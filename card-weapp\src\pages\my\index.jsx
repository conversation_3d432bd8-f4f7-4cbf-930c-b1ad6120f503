/*
 * @Author: 高超
 * @Date: 2021-11-27 11:01:05
 * @LastEditTime: 2022-08-03 18:05:51
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/my/index.jsx
 * love jiajia
 */
import { useState, useEffect } from 'react'
import Taro from '@tarojs/taro';
import { useSelector } from 'react-redux'
import { View, Text, Image } from '@tarojs/components'
import { AtList, AtListItem, AtCurtain } from "taro-ui"
import MyCard from '@/components/card/list';
import Memu from '@/components/memu'
import tools from '@/utils/tools'
import './index.scss'


function Index (props) { 
  const [isOpened, setIsOpened] = useState(false)
  const login = useSelector(state => state.login)
  const [loginOk, setLoginOk] = useState(false)
  useEffect( async () => {
    const userInfo = tools.getDataSafe('userInfo')
    if (userInfo !== null) {
      if (userInfo.token !== null) setLoginOk(true)
    }
  }, [])

  return (
    <View>
    <View style={{height:  `${tools.reTopH(6)}rpx` ,backgroundColor: '#fff'}}></View>
    {loginOk ? <View className='at-row my_avatr'>
      <View className='at-col at-col-3'>
        <Image src={tools.getDataSafe('userInfo').avatar ? tools.getDataSafe('userInfo').avatar : `${tools.picUrl}/index/head.png`} mode="widthFix" className="myhead"/>
      </View>
      <View className='at-col at-col-9'>
         <View className="nickname">{tools.getDataSafe('userInfo').nickname ? tools.getDataSafe('userInfo').nickname : "已登录"}</View>
         <View className="nickinfo">欢迎使用北京风景名胜年票</View>
      </View>
    </View> : <View className='at-row my_avatr' onClick={() => {
      tools.getUserInfo((res) => setLoginOk(res))
    }}>
      <View className='at-col at-col-3'>
        <Image src={`${tools.picUrl}/index/head.png`} mode="widthFix" className="myhead"/>
      </View>
      <View className='at-col at-col-9'>
         <View className="nickname">请登录</View>
         <View className="nickinfo">登录查看更多内容</View>
      </View>
    </View>}
    
    <AtList>
      <AtListItem title='我的卡包' arrow='right' thumb={`${tools.picUrl}/index/card.png`} onClick={() => {
        Taro.navigateTo({
          url: '/pages/my/card',
        });
      }}/>
      <AtListItem title='预约记录' arrow='right' thumb={`${tools.picUrl}/index/book.png`} onClick={() => {
         Taro.navigateTo({
          url: '/pages/my/book',
        });
      }}/>
      <AtListItem title='入园记录' arrow='right' thumb={`${tools.picUrl}/index/plan.png`} onClick={() => {
        Taro.navigateTo({
          url: '/pages/my/use',
        });
      }}/>
      <AtListItem title='我的订单' arrow='right' thumb={`${tools.picUrl}/index/card.png`} onClick={() => {
        Taro.navigateTo({
          url: '/pages/my/order',
        });
      }}/>
      {/* <AtListItem title='我的订单' arrow='right' thumb={`https://test.qqyhmmwg.com/res/card/memu/gift.png`} onClick={ async () => {
        await tools.appConfig('order')
      }}/> */}
    </AtList>
    <View style={{height: "8px"}}></View>
    <AtList>
      <AtListItem title='我的收藏' arrow='right' thumb={`${tools.picUrl}/index/love.png`} onClick={() => {
         Taro.navigateTo({
          url: '/pages/my/love',
        });
      }}/>
      <AtListItem title='联系客服' arrow='right' thumb={`${tools.picUrl}/index/help.png`} onClick={() => {
        Taro.makePhoneCall({
          phoneNumber: "4006091798"
        })
      }} />
    </AtList>
    <View className="cardList">
      <Text className="lastuse">最近使用</Text>
      {login ? <MyCard callback={() => setLoginOk(true)} max={3} />  : null}
    </View>
    <AtCurtain
        isOpened={isOpened}
        onClose={()=>{
          setIsOpened(false)
        }}
      >
        <Image
          style={{width: "100%", borderRadius: "15rpx"}}
          mode='widthFix'
          src={`${tools.ip}/go.jpg`}
        />
      </AtCurtain>
      <Memu now={3} />
  </View>
  )
}
export default Index;

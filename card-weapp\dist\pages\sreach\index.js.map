{"version": 3, "file": "pages/sreach/index.js", "sources": ["webpack:///./src/pages/sreach/index.jsx", "webpack:///./src/pages/sreach/index.jsx?985e"], "sourcesContent": ["/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2021-12-13 00:27:44\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/index.jsx\r\n */\r\nimport {  useState } from 'react'\r\nimport { View, Text } from '@tarojs/components'\r\nimport Taro from '@tarojs/taro'\r\nimport SreachTools from './bar'\r\nimport tools from '@/utils/tools'\r\nimport \"./index.scss\"\r\n\r\nconst SreachIndex = () => {\r\n    const [keyWords, setKeyWords] = useState(tools.getDataSafe('keyWords'))\r\n    return (\r\n        <View>\r\n          <SreachTools config={[]}/>\r\n          <View className=\"sreach_index\">\r\n              <Text>搜索历史</Text>\r\n              <View>\r\n                {\r\n                    (keyWords !== null) ? keyWords.map(v => <Text className=\"sreach_tags\" onClick={() => {\r\n                      Taro.navigateTo({\r\n                        url : `/pages/sreach/list?v=${v}`\r\n                      })\r\n                    }}>#{v}</Text>) : <View style={{paddingTop: '12px', color: '#a9a6a6'}}>暂无搜索记录</View>\r\n                }\r\n              </View>\r\n          </View>\r\n        </View>\r\n    )\r\n}\r\nexport default SreachIndex ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.jsx\"\nvar config = {\"navigationBarTitleText\":\"景区检索\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/sreach/index', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAGA;AACA;;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
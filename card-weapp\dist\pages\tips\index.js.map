{"version": 3, "file": "pages/tips/index.js", "sources": ["webpack:///./src/pages/tips/index.js", "webpack:///./src/pages/tips/index.js?e21f", "webpack:///./src/pages/tips/index.scss"], "sourcesContent": ["/*\r\n * @Author: 高超\r\n * @Date: 2021-12-17 14:44:03\r\n * @LastEditTime: 2022-07-21 10:47:08\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/tips/index.js\r\n * love jiajia\r\n */\r\nimport Taro from '@tarojs/taro';\r\nimport { AtIcon, AtButton } from 'taro-ui';\r\nimport { View } from '@tarojs/components';\r\nimport tools from '@/utils/tools'\r\nimport './index.scss';\r\n\r\n\r\nfunction Tips() {\r\n  const { params:{type} } = Taro.useRouter();\r\n    return (\r\n      <View>\r\n\r\n        {(type === 'pay100') ? <View className='tips_wrap'>\r\n          <AtIcon value='check' size='40' color='#5cc323' className='tips_t'></AtIcon>\r\n          <View className='tips_m'>支付成功</View>\r\n          <View className='tips_s'>感谢您的使用，我们将竭诚为您服务</View>\r\n          <View className='tip_btn'>\r\n            <AtButton type='primary' className='tips_btn_bg' onClick={() => {\r\n              Taro.redirectTo({\r\n                url: '/pages/sreach/listbuy',\r\n              });\r\n            }}>再去看看</AtButton>\r\n          </View>\r\n          <View className='tip_btn'>\r\n              <AtButton type='secondary' className='tips_btn_line' onClick={() => {\r\n                Taro.redirectTo({\r\n                  url: '/pages/my/index',\r\n                });\r\n              }}>我的订单</AtButton>\r\n          </View>\r\n        </View> : null}\r\n\r\n        {(type === 'pay105') ? <View className='tips_wrap'>\r\n          <AtIcon value='alert-circle' size='60' color='#b80e0e'></AtIcon>\r\n          <View className='tips_m'>支付失败</View>\r\n          <View className='tips_s'>您取消了支付请求～</View>\r\n          <View className='tip_btn'>\r\n          <AtButton type='primary' className='tips_btn_bg' onClick={() => {\r\n              Taro.redirectTo({\r\n                url: '/pages/sreach/listbuy',\r\n              });\r\n            }}>再去看看</AtButton>\r\n          </View>\r\n          <View className='tip_btn'>\r\n          <AtButton type='secondary' className='tips_btn_line' onClick={() => {\r\n                Taro.redirectTo({\r\n                  url: '/pages/my/index',\r\n                });\r\n              }}>我的订单</AtButton>\r\n          </View>\r\n        </View> : null}\r\n\r\n        {(type === 'pay106') ? <View className='tips_wrap'>\r\n          <AtIcon value='alert-circle' size='60' color='#b80e0e'></AtIcon>\r\n          <View className='tips_m'>支付失败</View>\r\n          <View className='tips_s'>订单状态错误～您可以拨打客服电话：4006091798</View>\r\n          <View className='tip_btn'>\r\n          <AtButton type='primary' className='tips_btn_bg' onClick={() => {\r\n              Taro.redirectTo({\r\n                url: '/pages/sreach/listbuy',\r\n              });\r\n            }}>再去看看</AtButton>\r\n          </View>\r\n          <View className='tip_btn'>\r\n          <AtButton type='secondary' className='tips_btn_line' onClick={() => {\r\n                Taro.redirectTo({\r\n                  url: '/pages/my/index',\r\n                });\r\n              }}>我的订单</AtButton>\r\n          </View>\r\n        </View> : null}\r\n        \r\n        {(type === '100') ? <View className='tips_wrap'>\r\n          <AtIcon value='check' size='40' color='#5cc323' className='tips_t'></AtIcon>\r\n          <View className='tips_m'>激活成功</View>\r\n          <View className='tips_s'>感谢您的使用，我们将竭诚为您服务</View>\r\n          <View className='tip_btn'>\r\n            <AtButton type='primary' className='tips_btn_bg' onClick={() => {\r\n              Taro.redirectTo({\r\n                url: '/pages/index/index',\r\n              });\r\n            }}>立即预约景区</AtButton>\r\n          </View>\r\n          <View className='tip_btn'>\r\n              <AtButton type='secondary' className='tips_btn_line' onClick={() => {\r\n                Taro.redirectTo({\r\n                  url: '/pages/my/index',\r\n                });\r\n              }}>我的年票</AtButton>\r\n          </View>\r\n        </View> : null}\r\n\r\n        {(type === '101') ? <View className='tips_wrap'>\r\n          <AtIcon value='alert-circle' size='60' color='#b80e0e'></AtIcon>\r\n          <View className='tips_m'>暂无可用</View>\r\n          <View className='tips_s'>请购买景区年票后按说明绑定激活卡片</View>\r\n          <View className='tip_btn'>\r\n            <AtButton type='primary' className='tips_btn_bg' onClick={() => {\r\n              Taro.redirectTo({\r\n                url: '/pages/bind/index',\r\n              });\r\n            }}>立即绑卡</AtButton>\r\n          </View>\r\n         {/* <View className='tip_btn'>\r\n              <AtButton type='secondary' className='tips_btn_line' onClick={ async () => {\r\n                await tools.appConfig('buy')\r\n              }}>购买年票</AtButton>\r\n          </View>*/}\r\n        </View> : null}\r\n\r\n        {(type === '102') ? <View className='tips_wrap'>\r\n          <AtIcon value='alert-circle' size='60' color='#b80e0e'></AtIcon>\r\n          <View className='tips_m'>程序开小差</View>\r\n          <View className='tips_s'>请您稍后再试</View>\r\n          <View className='tip_btn'>\r\n            <AtButton type='primary' className='tips_btn_bg' onClick={() => {\r\n              Taro.redirectTo({\r\n                url: '/pages/index/index',\r\n              });\r\n            }}>返回首页</AtButton>\r\n          </View>\r\n        </View> : null}\r\n\r\n      </View>\r\n    )\r\n}\r\nexport default Tips", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.js\"\nvar config = {\"navigationBarTitleText\":\"提示\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/tips/index', {root:{cn:[]}}, config || {}))\n\n", "// extracted by mini-css-extract-plugin"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAQA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAIA;AACA;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA;;;;A", "sourceRoot": ""}
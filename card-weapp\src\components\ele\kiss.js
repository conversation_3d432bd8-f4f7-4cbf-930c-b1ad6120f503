/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-01-17 17:51:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /dfx-umi/Users/<USER>/dfx-video/src/components/ele/kiss.js
 */
import Taro, { useEffect, useState, usePullDownRefresh } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtIcon, AtAvatar } from 'taro-ui';
import moment from 'moment';
import tools from '@/utils/tools';
const KissEle = (props) => { 
    const showNum = (parseInt(tools.getDataSafe('kiss') || 0, 10) < moment().valueOf())
    const [show, setShow] = useState(showNum)
    const bg = (props.type === 1) ? {
      bg: '#fd4b4b',
      cl: '#dbe91e'
    } : {
      bg: '#ffffff',
      cl: '#E91E63'
    }
    return (
      <View>
       { (show) ? null : null}
      </View>
    )
    // return (
    //   <View>
    //    { (show) ? <View onClick= {() => {
    //        tools.setData('kiss', moment(moment().add('7', 'd')).valueOf())
    //        setShow(false)
    //        Taro.navigateTo({
    //           url: `/pages/web/h5?to=erma&id=1`
    //        });
    //     }}>
    //     <View className='at-row' style={{backgroundColor: bg.bg, padding: '5rpx 10rpx', margin: '0 0 0 0'}}>
    //         <View className='at-col at-col-2' style={{paddingTop: '5rpx'}}>
    //             <AtAvatar size='small' circle  image={'https://test.qqyhmmwg.com/res/logo.png'}></AtAvatar>
    //           </View>
    //           <View className='at-col at-col-8'>
    //             <View style={{fontSize: '30rpx', lineHeight: '50px', color: bg.cl, fontWeight: 'bold'}}>微信关注众惠旅行，福利到账不遗漏</View>
    //           </View>
    //           <View className='at-col at-col-1' style={{textAlign: 'center', paddingTop: '12px'}}>
    //             <View style={{padding: '3px 0', width: '50px', background: '#3c9a40', color: '#fff', borderRadius: '5px', fontWeight: 'bold'}}>关注</View>
    //             <View style={{height: '3px'}}></View>
    //           </View>
    //     </View>
    //     </View> : null}
    //   </View>
    // )
}
KissEle.options = {
    addGlobalClass: true
  }
export default KissEle;
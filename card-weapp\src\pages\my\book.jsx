import { Component, useState, useEffect } from 'react'
import Taro, { useReachBottom } from '@tarojs/taro';
import { View } from '@tarojs/components'
import tools from '@/utils/tools'
import BookItem from '@/components/my/book';
import { AtList, AtTabs, AtIcon } from "taro-ui"
import './index.scss'

function Index (props) { 
  const query = tools.getQuery()
  const [init, setInit] = useState([])
  const [initPage, setInitPage] = useState()
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(-1)
  const [tabs, setTabs] = useState([])
  const tabList = [{ title: '景区预约' }, { title: '预约记录' }]
  useEffect( async () => {
    const code = Boolean(query.data.code) ? query.data.code : 'ALL'
    Taro.showLoading({
      mask: true,
      title: '读取中'
    })
    const data = await tools.Api.myBookList({
      code,
      page : {
          current: page,
          pageSize : 20
      }
    })
    Taro.hideLoading()
    if (data.code === 200) {
      for (const v of data.data.data) {
        if (tabs.includes(v.book_time_tab) === false) {
          tabs.push(v.book_time_tab)
          v.tabTop = true
        } else {
          v.tabTop = false
        }
      }
      setInit([
        ...init,
        ...data.data.data
      ])
      setInitPage(data.data.page)
      setTotal(data.data.page.total)
    }
  }, [ page ])

  useReachBottom(() => {
    const nextPage = initPage.current + 1
    if (nextPage <= initPage.totalPage) {
      setPage(nextPage)
    } else {
      Taro.showToast({
        title: '暂无更多内容',
        icon: 'none',
        duration: 2000
      })
    }
  })

  return (
    <View className='index'>
      <AtTabs current={1} tabList={tabList} onClick={(v) => {
        if (v === 0) {
          Taro.navigateTo({
            url: '/pages/sreach/book'
          })
        }
      }}></AtTabs>
      {(total === 0) ? <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "16px"}}>
          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>
          <View style={{marginTop: "10px" , color: "#848181"}}>暂无预约记录</View>
        </View> :
        <View>
          {init.map(v => {
            if (v.tabTop) {
              return <View>
                <View style={{backgroundColor: '#ffd65b', fontSize: '14px', color: '#af6c09', padding: '4px 7px', width: '80px', textAlign: 'center', margin: '8px', borderRadius: '20px', marginTop: '13px'}}>
                  {v.book_time_tab}
                </View>
                <BookItem data={v}/>
              </View>
            } else {
              return <BookItem data={v}/>
            }
          })}   
        </View>
        }
      <View style={{height: '30px'}}></View>  
    </View>
  )
}
export default Index;

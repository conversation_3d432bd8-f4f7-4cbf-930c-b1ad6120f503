/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-08-03 18:00:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/memu/index.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { AtTabBar } from 'taro-ui'
import tools from '@/utils/tools'

const Memu = (props) => {
    return (
        <View>
            <View style={{height: '100px'}}></View>
            <View className='memu'>
                <AtTabBar
                tabList={[
                    { title: '首页', image: `${tools.picUrl}/memu/index.png`, selectedImage: `${tools.picUrl}/memu/index_s.png`},
                    { title: '地图', image: `${tools.picUrl}/memu/map.png`, selectedImage: `${tools.picUrl}/memu/map_s.png` },
                    { title: '景区', image: `${tools.picUrl}/memu/brand.png`, selectedImage: `${tools.picUrl}/memu/brand_s.png` },
                    { title: '我的', image: `${tools.picUrl}/memu/mem.png`, selectedImage: `${tools.picUrl}/memu/mem_s.png` }
                ]}
                onClick={ async (value) => {
                   switch (value) {
                        case 0:
                           Taro.redirectTo({url: '/pages/index/index'})
                           break;
                        case 1:
                            Taro.redirectTo({url:'/pages/map/index'})
                            break;   
                        case 2:
                            Taro.redirectTo({url:'/pages/sreach/list'})
                            break;
                        case 3:
                            Taro.redirectTo({url:'/pages/my/index'})
                            break;
                        default:
                            break
                   }
                }}
                current={props.now}
                />
            </View>
        </View>
    )
}
export default Memu 
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-27 17:24:13
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/news/info.jsx
 */
import {  useState, useEffect } from 'react'
import { View, RichText } from '@tarojs/components'
import Taro from '@tarojs/taro'
import tools from '@/utils/tools'
import "./index.scss"

const SreachIndex = () => {
    const query = tools.getQuery()
    const [list, setList] = useState(null)
    useEffect( async () => {
      Taro.showLoading({
        mask: true,
        title: '读取中'
      })
      const data = await tools.Api.noteInfo({
        code : query.data.v
      })
      if (data.code === 200) {
        setList(data.data)
        Taro.setNavigationBarTitle({title:data.data.title})
      }
      Taro.hideLoading()
    }, [])
    
    return (
      <View>
        {
          list !== null ? 
          <View className='at-article'>
          <View className='at-article__h1' style={{fontSize: '22px', lineHeight: '28px'}}>
            {list.title}
          </View>
          <View className='at-article__info' style={{color: '#968e8e', marginTop: '8px'}}>
          {list.add_time}
          </View>
          <View className='at-article__content'>
            <View className="brand_html">
              <RichText className='htmlformat' style={{lineHeight: '30px'}} nodes={tools.removeCss(list.content)}/>
            </View>
          </View>
        </View>
          : null
        }
             
      </View>

    )
}
export default SreachIndex 
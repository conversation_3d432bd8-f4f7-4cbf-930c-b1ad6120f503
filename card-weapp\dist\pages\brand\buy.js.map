{"version": 3, "file": "pages/brand/buy.js", "sources": ["webpack:///./src/pages/brand/buy.jsx", "webpack:///./src/components/brand/ticket.js", "webpack:///./src/components/ele/timer.js", "webpack:///./src/pages/brand/buy.jsx?4565"], "sourcesContent": ["import { useState, useEffect } from 'react'\r\nimport Taro, { usePageScroll, useShareAppMessage } from '@tarojs/taro';\r\nimport { useSelector } from 'react-redux'\r\nimport { View, Text, Image, Swiper, SwiperItem, Video, RichText } from '@tarojs/components'\r\nimport { AtIcon , AtFloatLayout, AtNoticebar} from \"taro-ui\"\r\nimport Skeleton from 'taro-skeleton'\r\nimport Ticket from '@/components/brand/ticket'\r\nimport tools from '@/utils/tools'\r\nimport './index.scss'\r\n\r\n\r\nfunction Index (props) { \r\n  const query = tools.getQuery()\r\n  const login = useSelector(state => state.login)\r\n  const [loading, setLoading] = useState(true)\r\n  const [init, setInit] = useState(null)\r\n  const [goodList, setGoodList] = useState([])\r\n  const [card, setCard] = useState({\r\n    num: 0,\r\n    good_id: '--',\r\n    list: [],\r\n    name: '--',\r\n  })\r\n\r\n  const brandCode = query.data.v\r\n  useEffect( async () => {\r\n    if (login && Boolean(brandCode)) {\r\n      const data = await tools.Api.brandInfoZh(brandCode)\r\n      const goods = await tools.Api.brandGoodZh(brandCode)\r\n      if (data.code === 200) {\r\n        const cardList = await tools.getCardList({\r\n          list: data.data.brand.templet_list_enable\r\n        })\r\n        if (cardList.enable_card_num > 0) {\r\n          const enableCard = cardList.card_list.filter(v => v.enable === true)\r\n          setCard({\r\n            num : enableCard.length,\r\n            good_id: enableCard[0].good_id,\r\n            list: enableCard\r\n          })\r\n        }\r\n        setInit(data.data)\r\n        setLoading(false)\r\n      }\r\n      if (goods.code === 0) {\r\n        setGoodList(goods.data.ticket)\r\n      }\r\n    }\r\n  }, [login])\r\n\r\n  useShareAppMessage(() => {\r\n    return {\r\n      title: init.brand.name,\r\n      path: `/pages/brand/buy?v=${brandCode}`,\r\n      imageUrl: `${init.brand.logo}?x-oss-process=image/resize,m_fill,h_400,w_400`\r\n    }\r\n  })\r\n\r\n  return (\r\n    <View>\r\n       {(init === null && Boolean(brandCode) && goodList.length === 0 ) ?  <Skeleton title row={30} loading={loading}></Skeleton> :\r\n    <View>\r\n      <View className=\"topNav\" style={{position: 'absolute'}}>\r\n            <View style={{height:  `${tools.reTopH(6)}rpx`}}></View>\r\n            <View className='at-row'>\r\n                <View className='at-col at-col-1 left' onClick={() => {\r\n                        Taro.navigateBack({\r\n                            delta: 1,\r\n                            fail: () => {\r\n                                Taro.navigateTo({\r\n                                    url: '/pages/index/index'\r\n                                })\r\n                            }\r\n                        })\r\n                }}>\r\n                    <Image src={`${tools.picUrl}/memu/back.png`} mode='widthFix' className=\"topIcon_b\" />\r\n                </View>\r\n                <View className='at-col at-col-9'></View>\r\n            </View>\r\n        </View>\r\n      <View className=\"brand_top_pic\">\r\n        <Swiper\r\n          className='brand_swiper'\r\n          indicatorColor='#999'\r\n          indicatorActiveColor='#fff'\r\n          circular\r\n          indicatorDots\r\n          autoplay={true}\r\n        >\r\n          {init.brand.images.map(v => <SwiperItem>\r\n            <Image src={v.path} mode=\"heightfix\" className=\"images\"/>\r\n          </SwiperItem>)}\r\n        </Swiper>\r\n      </View>\r\n      {/* {\r\n        (init.brand.brand_note !== 'none' && init.brand.brand_note.length > 4) ? <AtNoticebar>{init.brand.brand_note}</AtNoticebar> : null\r\n      } */}\r\n     \r\n      <View className=\"brand_content\">\r\n        <View className='at-row'>\r\n          <View className='at-col at-col-9'>\r\n            <View className=\"title\">{init.brand.name}</View>\r\n            <View className=\"memo\" style={{padding: 0}}>\r\n            {\r\n                init.brand.tagsname.map( (v, index) => {\r\n                    return (index <= 3) ? <View className='tips_three' style={(index === 0) ? {marginLeft: '0'} : {}}>{v.tagname}</View> : null\r\n                } )\r\n            }\r\n            </View>\r\n          </View>\r\n          <View className='at-col at-col-3'>\r\n            <View style={{marginTop: '44px', color: '#777575', fontSize: '14px', textAlign:'right'}}>已售: {init.brand.ordernum + init.brand.ordernum_show}</View>\r\n          </View>\r\n        </View>\r\n        \r\n       \r\n        <View className='at-row list_show' onClick={() => {\r\n          Taro.makePhoneCall({\r\n            phoneNumber: init.brand.tel\r\n          })\r\n        }}>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/time.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text'>\r\n            营业时间 {(init.brandTime === null) ? '全天' : init.brandTime[0]}\r\n          </View>\r\n          <View className='at-col at-col-1'>\r\n            <AtIcon value='phone' size='18' color='#afafaf'></AtIcon>\r\n          </View>\r\n        </View>\r\n\r\n        <View className='at-row list_show' onClick={() => {\r\n          const wxMap = tools.bdMap_to_txMap(init.brand.latitude, init.brand.longitude);\r\n          Taro.openLocation({\r\n            latitude: wxMap.lat,\r\n            longitude: wxMap.lng,\r\n            scale: 15\r\n          })\r\n        }}>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/map.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text'>\r\n            {`${init.brand.province}${init.brand.city}${init.brand.address}`}\r\n          </View>\r\n          <View className='at-col at-col-1'>\r\n            <AtIcon value='chevron-right' size='18' color='#afafaf'></AtIcon>\r\n          </View>\r\n        </View>\r\n        \r\n        <View className='at-row list_show'>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/shop.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text' style={{fontWeight: 'bold'}}>\r\n            产品预订\r\n          </View>\r\n          <View className='at-col at-col-1'>\r\n            <AtIcon value='chevron-down' size='18' color='#afafaf'></AtIcon>\r\n          </View>\r\n          <View></View>\r\n        </View>\r\n        {(goodList.length > 0) ?  <Ticket \r\n                                    allT={goodList} \r\n                                    vid={0} \r\n                                    rank={1} \r\n                                    openmm={() => {}} \r\n                                    weixinadinfo={false}\r\n                                    coupon = {[]}\r\n                                    card= {card.num}\r\n                                  /> : null}\r\n        \r\n      </View>\r\n\r\n\r\n      {/* {init.brand.rule_list !== null && init.brand.rule_list.map(v =>  <View>\r\n        <View className=\"brand_title\">\r\n            <View className=\"t_icon\"></View>\r\n            <View className=\"t_name\">{v.val.title}</View>\r\n        </View>\r\n        <View className=\"brand_html\">\r\n          <RichText className='htmlformat' style={{lineHeight: '30px'}} nodes={tools.removeCss(v.val.memo)}/>\r\n        </View>\r\n      </View>)} */}\r\n     \r\n\r\n      <View>\r\n        <View className=\"brand_title\">\r\n            <View className=\"t_icon\"></View>\r\n            <View className=\"t_name\">特色介绍</View>\r\n        </View>\r\n        <View className=\"brand_html\">\r\n          <RichText  nodes={tools.removeCss(init.brand.memo)}/>\r\n        </View>\r\n      </View>\r\n\r\n    \r\n      <View style={{height: '80px'}}></View>\r\n     \r\n    </View>}\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { useState, useEffect } from 'react'\r\nimport Taro from '@tarojs/taro';\r\nimport { View, Text, Image, RichText } from '@tarojs/components';\r\nimport { AtFloatLayout, AtBadge } from 'taro-ui';\r\nimport Timer from '@/components/ele/timer';\r\nimport tools from '@/utils/tools';\r\n\r\nconst Ticket = (props) => {\r\n    const { allT, openmm, weixinadinfo, coupon } = props;\r\n    const [showNum, setShowNum] = useState(tools.showNum);\r\n    const [tipsBar, setTipsBar] = useState(false);\r\n    const [selData, setSelData] = useState({tips: []});\r\n    const [showList, setShowList] = useState([]);\r\n    const [canCoupon, setCanCoupon] = useState(coupon);\r\n    const brandAid = (() => {\r\n      try {\r\n        return Boolean(weixinadinfo) ? weixinadinfo.toString().split('.')[0] : 0;\r\n      } catch(e) {\r\n        return 0;\r\n      }\r\n    })();\r\n    useEffect(() => {\r\n      setCanCoupon(coupon);\r\n    }, [coupon])\r\n    const showDate = ['指定日期', '超长有效'];\r\n    return (<View><View>\r\n         <View className='brand_tt' style={{padding: 0}}>\r\n          {/* <View className='brand_tt_top'>在线预定</View> */}\r\n          {allT.map((val, index) =>\r\n            (val.good.id && index < showNum) ?\r\n            <View>\r\n             <View className='at-row brand_tt_per' key={val.id} onClick={ async () => {\r\n              if (parseInt(val.good.place_origin, 10) === 0) {\r\n                setSelData({\r\n                    ...val.good,\r\n                    name: val.name,\r\n                    itemname: null\r\n                });\r\n                openmm(false);\r\n                setTipsBar(true);\r\n              } else {\r\n                  const checked = showList.filter(item => item.goods_id === val.good.id)\r\n                  if (checked.length > 0) {\r\n                    setShowList([]);\r\n                  } else {\r\n                    Taro.showLoading({\r\n                      title: '读取中',\r\n                      mask: true\r\n                    });\r\n                    const allPrice = await tools.Api.getBrandDatePrice({id: val.good.id});\r\n                    setShowList(allPrice.data.rules);\r\n                    Taro.hideLoading();\r\n                  }\r\n              }\r\n             }}>\r\n              <View className='at-col at-col-8'>\r\n                {(parseInt(val.good.place_origin, 10) === 1) ? <View style={{height: '10rpx'}}></View> : null}\r\n                <View className='at-col--wrap brand_tt_name'>\r\n                  <Text className='brand_tt_name'>{val.name}</Text>\r\n                </View>\r\n                <View className='brand_tt_tags'>\r\n                  <Text className='brand_tt_tips' style={{color: '#28bb65'}}>{tools.getlimt(val.good.limittype, val.good.limitnum)}</Text>\r\n                  <Text className='brand_tt_shu'>|</Text>\r\n                  <Text className='brand_tt_tips' style={{color: '#e23b14'}}>{['随时退', '有效可退', '条件退'][parseInt(val.good.if_refund)]}</Text>\r\n                  <Text style={(tools.showUseDate(val.good.is_book,val.good.open_time,val.good.close_time) !== -1) ? {display: 'inline-block'} : {display: 'none'}}>\r\n                    <Text className='brand_tt_shu'>|</Text>\r\n                    <Text className='brand_tt_tips' style={{color: '#ff9800'}}>{showDate[tools.showUseDate(val.good.is_book,val.good.open_time,val.good.close_time)]}</Text>\r\n                  </Text>\r\n                  {/* <Text className='brand_tt_shu'>|</Text>\r\n                  <Text className='brand_tt_sale'>{tools.getSale(val.good.sales_count)}</Text> */}\r\n\r\n                  {(parseInt(val.good.place_origin, 10) === 1) ? null : <Text><Text className='brand_tt_shu'>|</Text>\r\n                  \r\n                  <Text className='brand_tt_tips' style={{color:'#d02114', fontWeight: 'bold'}}>购买须知 必读\r\n                  \r\n                  </Text></Text>}\r\n\r\n\r\n                  {(val.good.buy_time === '1' && parseInt(val.good.place_origin, 10) === 0) ? <View style={{width: '60%', margin: '10rpx 0'}}><Timer end={val.good.end_time} start={val.good.start_time} /></View> : null}\r\n                </View>\r\n              </View>\r\n              {(parseInt(val.good.place_origin, 10) === 0) ?   <View className='at-col at-col-4' style={{textAlign: 'right'}}>\r\n                  <View className='brand_buy_price'><Text className='price_t'>¥</Text> {tools.getPrice(val.good.share_price)}\r\n                  <Text className='brand_buy_showp'>¥{tools.getPrice(val.good.show_price)}</Text></View>\r\n                  {(val.good.buy_time === '1') ? <View className='brand_buy_btn_time '>抢购</View> : <View className='brand_buy_btn'>购买</View>}\r\n                </View> :   <View className='at-col at-col-4' style={{textAlign: 'right'}}>\r\n                    <View className='brand_buy_price'><Text className='price_t'>¥</Text> {tools.getPrice(val.good.share_price)}\r\n                    <Text className='brand_buy_showqi'>起</Text></View>\r\n                    <View style={{color: '#fff', fontSize: '26rpx', background: '#03A9F4', width: '62%', textAlign: 'center', marginLeft: '43%', borderRadius: '25rpx',padding:'8rpx 0'}}>立即预定</View>\r\n                  </View>}\r\n            </View>\r\n            {showList.map( rules =>\r\n              (rules.goods_id == val.good.id) ? <View className='at-row brand_tt_per' key={rules.code} style={{background: '#fbfbfb'}} onClick={() => {\r\n                setSelData({\r\n                  ...val.good,\r\n                  rulescode: rules.title_code,\r\n                  show_price: rules.show_price,\r\n                  buy_price: rules.buy_price,\r\n                  share_price: rules.share_price,\r\n                  name: val.name,\r\n                  itemname: rules.title\r\n                });\r\n                openmm(false);\r\n                setTipsBar(true);\r\n              }}>\r\n                  <View className='at-col at-col-8'>\r\n                    <View className='at-col--wrap brand_tt_name'>\r\n                      <Text className='brand_tt_name' style={{fontSize: '26rpx', color: '#636161'}}>{rules.title}</Text>\r\n                    </View>\r\n                    <View className='brand_tt_tags'>\r\n                      <View style={{color: 'red'}}><Text><Text className='brand_tt_tips' style={{color:'#6fb2f9'}}>购买须知</Text><Text className='brand_tt_shu'>|</Text></Text>{tools.dateInfo(rules.stime, rules.etime, rules.enable_week)}</View>\r\n                    </View>\r\n                  </View>\r\n                  <View className='at-col at-col-4' style={{textAlign: 'right'}}>\r\n                    <View className='brand_buy_price'><Text className='price_t'>¥</Text> {tools.getPrice(rules.share_price)}\r\n                    <Text className='brand_buy_showp'>¥{tools.getPrice(rules.show_price)}</Text></View>\r\n                    <View className='brand_buy_btn'>购买</View>\r\n                  </View>\r\n                </View> : null\r\n          )}\r\n          </View> : null\r\n        )}\r\n        </View>\r\n        {allT.length > tools.showNum ? <View className='brand_show_all' onClick={() => {\r\n            if (showNum === tools.showNum) {\r\n              setShowNum(99)\r\n            } else {\r\n              setShowNum(tools.showNum)\r\n            }\r\n          }}>{(showNum > tools.showNum) ? `收起` : `查看全部${allT.length}类产品`}\r\n        </View> : null}\r\n      </View>\r\n      {(tipsBar) ? <AtFloatLayout isOpened={tipsBar} onClose={() => {\r\n        setTipsBar(false);\r\n        openmm(true);\r\n      }} title='购买须知'>\r\n        <View className='brand_tips'>\r\n            <View className='at-row'>\r\n                <View className='at-col at-col-3'>\r\n                    <Image src={selData.home_recommended_images} className='brand_good_img' mode={'aspectFill'}/>\r\n                </View>\r\n                <View className='at-col at-col-9' style={{paddingLeft: '15rpx'}}>\r\n                    <View className='at-col--wrap brand_tt_name'>\r\n                        <Text className='brand_tt_name'>{tools.getSamll(selData.name, 15)}</Text>\r\n                    </View>\r\n                    {(selData.itemname !== null) ? <View>{selData.itemname}</View>: null}\r\n                    <View className='brand_tt_tags' style={(selData.itemname !== null) ? {padding: 0} : {}}>\r\n                      <Text className='brand_tt_tips' style={{color: '#28bb65'}}>{tools.getlimt(selData.limittype, selData.limitnum)}</Text>\r\n                      <Text className='brand_tt_shu'>|</Text>\r\n                      <Text className='brand_tt_tips' style={{color: '#e23b14'}}>{['随时退', '有效可退', '条件退'][parseInt(selData.if_refund)]}</Text>\r\n                      <Text style={(tools.showUseDate(selData.is_book,selData.open_time,selData.close_time) !== -1) ? {display: 'inline-block'} : {display: 'none'}}>\r\n                        <Text className='brand_tt_shu'>|</Text>\r\n                        <Text className='brand_tt_tips' style={{color: '#ff9800'}}>{showDate[tools.showUseDate(selData.is_book,selData.open_time,selData.close_time)]}</Text>\r\n                      </Text>\r\n                      {/* <Text className='brand_tt_shu'>|</Text>\r\n                      <Text className='brand_tt_sale'>{tools.getSale(selData.sales_count)}</Text> */}\r\n                      {(selData.buy_time === '1') ? <View style={{width: '60%', margin: '10rpx 0'}}><Timer end={selData.end_time} start={selData.start_time} /></View> : null}\r\n\r\n                    </View>\r\n                </View>\r\n            </View>\r\n            {(selData.tips.length === 0)\r\n                ? <View>{selData.content_web}</View> :\r\n                <View style={{paddingBottom: '50rpx'}}> {selData.tips.map(txt => {\r\n                return (<View key={selData.title}>\r\n                    <View style={{margin: '15rpx 0 5rpx 0', fontWeight: 'bold'}}>{txt.title}</View>\r\n                    {(txt.memo.includes('<span class=\"none\"></span>')) ? <RichText nodes={tools.removeCss(txt.memo)} style={{color: '#585656'}}/> : <Text style={{color: '#585656'}}>{txt.memo}</Text>}\r\n                    <View style={{height: '30rpx'}}></View>\r\n                    {/* <Text style={{color: '#777373'}}>{txt.memo}</Text> */}\r\n                </View>)\r\n                })}</View>\r\n            }\r\n\r\n        </View>\r\n      </AtFloatLayout> : null}\r\n      {(tipsBar) ?   <View className='brand_bar'>\r\n          <View className='at-row'>\r\n              <View className='at-col at-col-8'>\r\n                  <View className='brand_buy_price' style={{fontSize: '40rpx'}}>\r\n                      <Text className='price_t'>¥</Text> {tools.getPrice(selData.share_price)}\r\n                      <Text className='brand_buy_showp'>¥{tools.getPrice(selData.show_price)}</Text>\r\n                  </View>\r\n              </View>\r\n              {/*<View className='at-col at-col-4'>\r\n                      <View className='brand_bar_btn' onClick={() => {\r\n                          if (props.card === 0) {\r\n\r\n                            Taro.showModal({\r\n                              title: '友情提示',\r\n                              content: '需持有并激活《北京风景年票》后购买此特价产品！',\r\n                              confirmText: '购买年票',\r\n                              cancelText:  '放弃折扣',\r\n                              success : async (res) => {\r\n                                if (res.confirm) {\r\n                                  await tools.appConfig('buy')\r\n                                }\r\n                              }\r\n                            })\r\n                          } else {\r\n                            Taro.navigateTo({\r\n                              url: `/pages/wxshop/productBuy?productId=${selData.id}&rulescode=${(Boolean(selData.rulescode) ? selData.rulescode : 'none')}`\r\n                            });\r\n                          }\r\n                      }}>立即购买</View>\r\n              </View>*/}\r\n          </View>\r\n        </View> : null}\r\n    </View>)\r\n}\r\nTicket.defaultProps = {\r\n  allT: [],\r\n  coupon: []\r\n}\r\nexport default Ticket;\r\n", "import { Component, useEffect, useState } from 'react'\r\nimport Taro, { usePullDownRefresh } from '@tarojs/taro';\r\nimport { View, Text } from '@tarojs/components';\r\nimport { AtIcon } from 'taro-ui';\r\nimport moment from 'moment';\r\nconst Timer = (props) => {\r\n  const [init, setInit] = useState(['--','--','--']);\r\n  const [showTxt, setShowTxt] = useState('--');\r\n  const [showStatus, setShowStatus] = useState('m');\r\n  let timer = null;\r\n  useEffect(() => {\r\n    const  {start, end} = props;\r\n    const now = moment();\r\n    const end_diff = moment(end).diff(now);\r\n    const start_diff = moment(start).diff(now);\r\n    const timer_status = (start_diff > 0) ? 'will' : 'buy';\r\n    setShowStatus(timer_status);\r\n    const operTimer = (timer_status === 'buy') ? {\r\n      day: moment(end).diff(now, 'day'),\r\n      ms: moment.duration(moment(end).diff(now), 'milliseconds')\r\n    } : {\r\n      day: moment(start).diff(now, 'day'),\r\n      ms: moment.duration(moment(start).diff(now), 'milliseconds')\r\n    }\r\n    //if (timer_status === 'buy') {\r\n      setShowTxt((timer_status === 'buy') ? '结束于' : '开始于');\r\n      //if ( operTimer.day > 2) {\r\n      if (timer_status === 'buy') {\r\n        setInit(moment(end).format('MM-DD-HH').split('-'));\r\n      } else {\r\n        setInit(moment(start).format('MM-DD-HH').split('-'));\r\n      }\r\n      setShowStatus('m');\r\n      //} else {\r\n        // timer = setTimeout(() => {\r\n        //   const timer_all = operTimer.ms;\r\n        //   const timer_hh = (parseInt(timer_all.days(), 10) * 24) + timer_all.hours();\r\n        //   setInit([timer_hh, \r\n        //         (parseInt(timer_all.minutes(), 10) < 10 ? `0${timer_all.minutes()}` : timer_all.minutes()), \r\n        //         (parseInt(timer_all.seconds(), 10) < 10 ? `0${timer_all.seconds()}` : timer_all.seconds())\r\n        //         ]);\r\n        // }, 1000);\r\n          // const timer_all = operTimer.ms;\r\n          // const timer_hh = (parseInt(timer_all.days(), 10) * 24) + timer_all.hours();\r\n          // setInit([timer_hh, \r\n          //       (parseInt(timer_all.minutes(), 10) < 10 ? `0${timer_all.minutes()}` : timer_all.minutes()), \r\n          //       (parseInt(timer_all.seconds(), 10) < 10 ? `0${timer_all.seconds()}` : timer_all.seconds())\r\n          //       ]);\r\n          // setShowStatus('s');\r\n      //}\r\n     //}\r\n  }, [])\r\n  \r\n    return (\r\n        <View>\r\n            <View className='at-row' >\r\n                    <View className='at-col at-col-1'>\r\n                      <AtIcon value='bell' size='16' color='#fa6f14'></AtIcon>   \r\n                    </View>\r\n                    <View className='at-col at-col-4' style={{textAlign: 'center'}}>\r\n                    <Text style={{fontSize: '24rpx'}}>{showTxt}</Text>\r\n                    </View>\r\n                    {(showStatus === 's') ? <View className='at-col at-col-7'>\r\n                      <View className='time_item time_month'>{init[0]}</View>\r\n                      <Text style={{fontSize: '20rpx'}}>时</Text>\r\n                      <View className='time_item time_month'>{init[1]}</View>\r\n                      <Text style={{fontSize: '20rpx'}}>分</Text>\r\n                      <View className='time_item time_month'>{init[2]}</View>\r\n                      <Text style={{fontSize: '20rpx'}}>秒</Text>\r\n                    </View> : <View className='at-col at-col-7'>\r\n                      <View className='time_item time_month'>{init[0]}</View>\r\n                      <Text style={{fontSize: '20rpx'}}>月</Text>\r\n                      <View className='time_item time_month'>{init[1]}</View>\r\n                      <Text style={{fontSize: '20rpx'}}>日</Text>\r\n                      <View className='time_item time_month'>{init[2]}</View>\r\n                      <Text style={{fontSize: '20rpx'}}>时</Text>\r\n                    </View>}\r\n                    \r\n                  </View>\r\n                  {/* <View className='at-row' >\r\n                    <View className='at-col at-col-1'>\r\n                      <AtIcon value='bell' size='16' color='#fa6f14'></AtIcon>   \r\n                    </View>\r\n                    <View className='at-col at-col-4' style={{textAlign: 'center'}}>\r\n                      <Text style={{fontSize: '24rpx'}}>结束于</Text>\r\n                    </View>\r\n                    <View className='at-col at-col-7'>\r\n                      <View className='time_item time_month'>11</View>\r\n                      <Text style={{fontSize: '20rpx'}}>月</Text>\r\n                      <View className='time_item time_month'>11</View>\r\n                      <Text style={{fontSize: '20rpx'}}>日</Text>\r\n                      <View className='time_item time_month'>20</View>\r\n                      <Text style={{fontSize: '20rpx'}}>时</Text>\r\n                    </View>\r\n                  </View> */}\r\n        </View>\r\n    )\r\n}\r\nTimer.options = {\r\n    addGlobalClass: true\r\n  }\r\n  Timer.defaultProps = {\r\n    start: '2020-12-12',\r\n    end: '2020-12-12'\r\n  }\r\nexport default Timer;", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./buy.jsx\"\nvar config = {\"navigationBarTitleText\":\"风景名胜年票\",\"navigationStyle\":\"custom\",\"enableShareAppMessage\":true,\"transparentTitle\":\"always\",\"titlePenetrate\":\"YES\"};\n\ncomponent.enableShareAppMessage = true\nvar inst = Page(createPageConfig(component, 'pages/brand/buy', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AALA;AAAA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAFA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AACA;AAEA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAMA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAEA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAIA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAgBA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAIA;AAAA;AAAA;AAAA;AAAA;AAEA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5MA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAGA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAuBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;ACrNA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
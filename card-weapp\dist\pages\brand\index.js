(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/brand/index"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/brand/index.jsx":
/*!*******************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/brand/index.jsx ***!
  \*******************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _components_calendar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/calendar */ "./src/components/calendar/index.jsx");
/* harmony import */ var _components_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/card */ "./src/components/card/index.jsx");
/* harmony import */ var taro_skeleton__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! taro-skeleton */ "./node_modules/taro-skeleton/dist/index.umd.js");
/* harmony import */ var taro_skeleton__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(taro_skeleton__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _components_memu_brand__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/memu/brand */ "./src/components/memu/brand.jsx");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! moment */ "./node_modules/moment/dist/moment.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./index.scss */ "./src/pages/brand/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__);

















function Index(props) {
  var query = _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].getQuery();
  var login = Object(react_redux__WEBPACK_IMPORTED_MODULE_5__[/* useSelector */ "c"])(function (state) {
    return state.login;
  });
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(true),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(null),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState3, 2),
    init = _useState4[0],
    setInit = _useState4[1];
  var _useState5 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(true),
    _useState6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState5, 2),
    auto = _useState6[0],
    setAuto = _useState6[1];
  var _useState7 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState8 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState7, 2),
    love = _useState8[0],
    setLove = _useState8[1];
  var _useState9 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState10 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState9, 2),
    bookShow = _useState10[0],
    setBookShow = _useState10[1];
  var _useState11 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState12 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState11, 2),
    changeCard = _useState12[0],
    setChangeCard = _useState12[1];
  var _useState13 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])({
      num: 0,
      good_id: '--',
      list: [],
      name: '--'
    }),
    _useState14 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState13, 2),
    card = _useState14[0],
    setCard = _useState14[1];
  var _useState15 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])({
      start: "2022-01-01",
      end: "2022-02-01",
      list: []
    }),
    _useState16 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState15, 2),
    dateLine = _useState16[0],
    setDateLine = _useState16[1];
  var _useState17 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(""),
    _useState18 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState17, 2),
    weather = _useState18[0],
    setWeather = _useState18[1];
  var brandCode = query.data.v;
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var data, cardList, enableCard;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (!(login && Boolean(brandCode))) {
            _context.next = 14;
            break;
          }
          _context.next = 3;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].Api.brandInfo({
            code: brandCode
          });
        case 3:
          data = _context.sent;
          if (!(data.code === 200)) {
            _context.next = 14;
            break;
          }
          _context.next = 7;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].getCardList({
            list: data.data.brand.templet_list_enable
          });
        case 7:
          cardList = _context.sent;
          if (cardList.enable_card_num > 0) {
            enableCard = cardList.card_list.filter(function (v) {
              return v.enable === true;
            });
            setCard({
              num: enableCard.length,
              good_id: enableCard[0].good_id,
              list: enableCard
            });
          }
          console.warn("--------- data.data --------", data.data);
          setInit(data.data);
          setLove(data.data.brand.collect);
          setLoading(false);
          getWeather(data.data.brand.area);
        case 14:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [login]);
  var getWeather = function getWeather(area) {
    var url = "";
    if (area.length > 2) {
      url = "https://wis.qq.com/weather/common?source=pc&weather_type=observe|air&province=".concat(area[0], "&city=").concat(area[1], "&county=").concat(area[2]);
    } else {
      url = "https://wis.qq.com/weather/common?source=pc&weather_type=observe|air&province=".concat(area[0], "&city=").concat(area[0], "&county=").concat(area[1]);
    }
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.request({
      url: url,
      method: "get"
    }).then(function (res) {
      var data = res.data.data;
      // const weather_text = `${data.observe.weather} / ${data.observe.degree}° / 湿度 ${data.observe.humidity}% / ${data.observe.wind_direction_name} ${data.observe.wind_power} 级 / 空气质量 ${data.air.aqi_name}`
      var weather_text = "".concat(data.observe.weather, " / ").concat(data.observe.degree, "\xB0 / ").concat(data.observe.wind_direction_name, " ").concat(data.observe.wind_power, " \u7EA7");
      setWeather(weather_text);
    });
  };
  Object(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__["useShareAppMessage"])(function () {
    return {
      title: init.brand.brand_name,
      path: "/pages/brand/index?v=".concat(brandCode),
      imageUrl: "".concat(init.brand.image[0].thumbUrl, "?x-oss-process=image/resize,m_fill,h_400,w_400")
    };
  });
  var operBook = /*#__PURE__*/function () {
    var _ref2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee2() {
      var data;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            if (!(card.num > 0)) {
              _context2.next = 9;
              break;
            }
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showLoading({
              mask: true,
              title: '读取中'
            });
            _context2.next = 4;
            return _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].Api.brandBookList({
              brand_code: brandCode,
              good_id: card.good_id,
              today: init.book.today_time
            });
          case 4:
            data = _context2.sent;
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.hideLoading();
            if (data.code === 200) {
              if (data.data.list.length > 0) {
                setDateLine({
                  start: data.data.list[0].val,
                  end: data.data.list[data.data.list.length - 1].val,
                  list: data.data.list
                });
                setBookShow(true);
              }
            } else {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showToast({
                title: data.message,
                icon: 'none',
                duration: 2000
              });
            }
            _context2.next = 10;
            break;
          case 9:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
              url: '/pages/bind/index'
            });
          case 10:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function operBook() {
      return _ref2.apply(this, arguments);
    };
  }();
  var showStatus = function showStatus(txt) {
    if (txt.includes('|')) {
      return txt.split('|');
    }
    return [txt];
  };
  var showTime = function showTime(time) {
    var newTime = [];
    time.forEach(function (v) {
      newTime.push(v.replace(':00', ''));
    });
    return newTime.join('~');
  };
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
    children: init === null && Boolean(brandCode) ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(taro_skeleton__WEBPACK_IMPORTED_MODULE_10___default.a, {
      title: true,
      row: 30,
      loading: loading
    }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_components_memu_brand__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"], {
        love: love,
        oper: /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee3() {
          return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                _context3.next = 2;
                return _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].Api.brandCollect({
                  brand_code: brandCode,
                  action: love === false ? 'ADD' : 'DEL'
                });
              case 2:
                setLove(!love);
              case 3:
              case "end":
                return _context3.stop();
            }
          }, _callee3);
        }))
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "brand_top_pic",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Swiper"], {
          className: "brand_swiper",
          indicatorColor: "#999",
          indicatorActiveColor: "#fff",
          circular: true,
          indicatorDots: true,
          autoplay: auto,
          children: [init.brand.video !== null && init.brand.video.map(function (v) {
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["SwiperItem"], {
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Video"], {
                src: v.url,
                controls: true,
                autoplay: false,
                showProgress: true,
                poster: '',
                initialTime: "0",
                enablePlayGesture: true,
                autoPauseIfOpenNative: true,
                muted: false,
                className: "images",
                objectFit: "contain",
                onPlay: function onPlay() {
                  setAuto(false);
                },
                onEnded: function onEnded() {
                  setAuto(true);
                }
              })
            });
          }), init.brand.static_list.map(function (v) {
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["SwiperItem"], {
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
                src: v.url,
                mode: "heightfix",
                className: "images"
              })
            });
          })]
        })
      }), init.brand.brand_note !== 'none' && init.brand.brand_note.length > 4 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtNoticebar */ "o"], {
        children: init.brand.brand_note
      }) : null, /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "brand_content",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row",
          style: {
            marginBottom: '20px'
          },
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10",
            children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "title",
              children: init.brand.brand_name
            }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "memo",
              children: init.brand.intro
            })]
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-2",
            children: init.limit.is_open_config.is_open ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "open",
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                style: {
                  marginTop: '5px'
                },
                children: showStatus(init.limit.is_open_config.is_open_tips).map(function (v) {
                  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                    className: "num",
                    children: v
                  });
                })
              })
            }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "open close",
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                style: {
                  marginTop: '5px'
                },
                children: showStatus(init.limit.is_open_config.is_open_tips).map(function (v) {
                  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                    className: "num",
                    children: v
                  });
                })
              })
            })
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.makePhoneCall({
              phoneNumber: init.brand.tel
            });
          },
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].picUrl, "/info/time.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            children: ["\u8425\u4E1A\u65F6\u95F4 ", showTime(init.brand.jijie.time)]
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtIcon */ "f"], {
              value: "phone",
              size: "18",
              color: "#afafaf"
            })
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.openLocation({
              latitude: parseFloat(init.brand.latitude),
              longitude: parseFloat(init.brand.longitude),
              name: init.brand.brand_name,
              address: init.brand.address,
              scale: 12
            });
          },
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].picUrl, "/info/map.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            children: "".concat(init.brand.area.join('')).concat(init.brand.address)
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtIcon */ "f"], {
              value: "chevron-right",
              size: "18",
              color: "#afafaf"
            })
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].picUrl, "/info/rank.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            children: [_utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].brandRank(init.brand.rank), " ", _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].brandSpace(init.brand.in_out)]
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1"
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].picUrl, "/info/bookt.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            children: ["\u5165\u56ED\u65B9\u5F0F ", /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
              style: {
                color: init.limit.is_book ? '#FF9800' : '#4CAF50',
                fontWeight: 600
              },
              children: init.limit.is_book ? '需提前预约' : '无需预约'
            }), init.limit.today_num > -1 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
              style: {
                color: 'red'
              },
              children: [" ", init.limit.today_num_str]
            }) : null]
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].picUrl, "/info/date.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            children: ["\u5B9E\u65F6\u5929\u6C14 ", /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
              children: weather
            })]
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].picUrl, "/info/date.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              children: ["\u5F00\u653E\u65F6\u95F4 ", "".concat(Object(moment__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"])(init.limit.enable_start_time_unix * 1000).format('YYYY年M月D日'), " \u81F3 ").concat(Object(moment__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"])(init.limit.enable_end_time_unix * 1000).format('YYYY年M月D日'))]
            }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              style: {
                paddingTop: '4px',
                color: '#4CAF50',
                fontWeight: 400
              },
              children: [init.limit.enable_week_str.join(' , '), init.limit.is_book ? '可约' : '开放']
            })]
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1"
          })]
        })]
      }), init.brand.rule_list !== null && init.brand.rule_list.map(function (v) {
        return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "brand_title",
            children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "t_icon"
            }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "t_name",
              children: v.val.title
            })]
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "brand_html",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["RichText"], {
              className: "htmlformat",
              style: {
                lineHeight: '30px'
              },
              nodes: _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].removeCss(v.val.memo)
            })
          })]
        });
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "brand_title",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "t_icon"
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "t_name",
            children: "\u7279\u8272\u4ECB\u7ECD"
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "brand_html",
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["RichText"], {
            nodes: _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].removeCss(init.brand.content)
          })
        })]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        style: {
          height: '80px'
        }
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "at-row brand_memu",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-col at-col-4 card_status",
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-row",
            children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "at-col at-col-2",
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
                mode: "widthFix",
                src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].picUrl, "/index/card.png"),
                className: "c_icon"
              })
            }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "at-col at-col-9",
              onClick: function onClick() {
                if (card.num > 0) {
                  setChangeCard(true);
                }
              },
              children: card.num === 0 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                className: "nocard",
                children: "\u6682\u65E0\u53EF\u7528\u5361"
              }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                className: "hadcard",
                children: ["NO.", card.good_id, " ", /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                  className: "ok",
                  children: "\u6362\u5361"
                }), " "]
              })
            })]
          })
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-col at-col-4",
          children: init.limit.is_open ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "book",
            style: init.limit.is_book ? {
              backgroundColor: "#FF5722"
            } : {},
            onClick: function onClick() {
              //setBookShow(true)
              setChangeCard(true);
              // operBook()
            },
            children: card.num === 0 ? '激活年票' : function () {
              return init.limit.is_book ? '预约入园' : '开放日历';
            }()
          }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "book_close",
            children: "\u6682\u505C\u5165\u56ED"
          })
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-col at-col-4",
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "book",
            style: {
              backgroundColor: "#FF5722"
            },
            onClick: function onClick() {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
                url: '/pages/buy/index'
              });
            },
            children: "\u8D2D\u4E70\u5E74\u7968"
          })
        })]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtFloatLayout */ "d"], {
        isOpened: bookShow,
        title: init.limit.is_book ? "\u8BF7\u60A8\u9009\u62E9\u9884\u7EA6\u65E5\u671F" : '景区开放日历',
        onClose: function onClose() {
          setBookShow(false);
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          style: {
            backgroundColor: '#fff'
          },
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_components_calendar__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], {
            start: dateLine.start,
            end: dateLine.end,
            list: dateLine.list,
            book: init.limit.is_book,
            onChange: function onChange(date) {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showModal({
                title: "No.".concat(card.good_id, "\u9884\u7EA6\u786E\u8BA4"),
                content: "\u9884\u7EA6 ".concat(date, " \u5165\u56ED\uFF1F"),
                success: function () {
                  var _success = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee4(res) {
                    var params, data;
                    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee4$(_context4) {
                      while (1) switch (_context4.prev = _context4.next) {
                        case 0:
                          if (!res.confirm) {
                            _context4.next = 9;
                            break;
                          }
                          setBookShow(false);
                          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showLoading({
                            mask: true,
                            title: '提交中'
                          });
                          params = {
                            brand_code: brandCode,
                            good_id: card.good_id,
                            day: date
                          };
                          _context4.next = 6;
                          return _utils_tools__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"].Api.addBrandBook(params);
                        case 6:
                          data = _context4.sent;
                          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.hideLoading();
                          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showToast({
                            title: data.message,
                            icon: 'none',
                            duration: 2000
                          });
                        case 9:
                        case "end":
                          return _context4.stop();
                      }
                    }, _callee4);
                  }));
                  function success(_x) {
                    return _success.apply(this, arguments);
                  }
                  return success;
                }()
              });
            }
          })
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtFloatLayout */ "d"], {
        isOpened: changeCard,
        title: "\u8BF7\u60A8\u9009\u62E9\u9700\u9884\u7EA6\u5361",
        onClose: function onClose() {
          setChangeCard(false);
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          style: {
            backgroundColor: '#fff',
            width: '93%',
            margin: '0 auto 20px auto'
          },
          children: card.list.map(function (v) {
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_15__["jsx"])(_components_card__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], {
              data: v,
              oper: function oper(id, name) {
                setCard({
                  num: card.num,
                  good_id: id,
                  list: card.list,
                  name: name
                });
                setChangeCard(false);
                operBook();
              }
            });
          })
        })
      })]
    })
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Index);

/***/ }),

/***/ "./src/components/calendar/index.jsx":
/*!*******************************************!*\
  !*** ./src/components/calendar/index.jsx ***!
  \*******************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./index.scss */ "./src/components/calendar/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__);








var Main = function Main(props) {
  //每月多少天
  var MONTH_DAYS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  var WEEK_NAMES = ['日', '一', '二', '三', '四', '五', '六'];
  var start = props.start,
    end = props.end,
    list = props.list,
    book = props.book;
  var LINES = [1, 2, 3, 4, 5, 6];
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])(0),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState, 2),
    year = _useState2[0],
    setLoinsYear = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])(0),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState3, 2),
    month = _useState4[0],
    seLoinstMonth = _useState4[1];
  var _useState5 = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])(0),
    _useState6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState5, 2),
    endYear = _useState6[0],
    setEndYear = _useState6[1];
  var _useState7 = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])(0),
    _useState8 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState7, 2),
    endMonth = _useState8[0],
    setEndMonth = _useState8[1];
  var currentDate = new Date();
  var _useState9 = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])(false),
    _useState10 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState9, 2),
    tag = _useState10[0],
    setTag = _useState10[1];
  Object(react__WEBPACK_IMPORTED_MODULE_1__["useEffect"])(function () {
    if (Boolean(start) && Boolean(end)) {
      var startDate = new Date(start);
      var endDate = new Date(end);
      setLoinsYear(startDate.getFullYear());
      seLoinstMonth(startDate.getMonth());
      setEndYear(endDate.getFullYear());
      setEndMonth(endDate.getMonth());
    } else {
      setLoinsYear(currentDate.getFullYear());
      seLoinstMonth(currentDate.getMonth());
      setEndYear(currentDate.getFullYear());
      setEndMonth(currentDate.getMonth() + 3);
    }
  }, [start, end]);
  //获取当前月份
  var getMonth = function getMonth(date) {
    return date.getMonth();
  };
  //获取当前年份
  var getFullYear = function getFullYear(date) {
    return date.getFullYear();
  };
  var getCurrentMonthDays = function getCurrentMonthDays(month, year) {
    var _year = year + currentDate.getFullYear();
    if (_year % 100 != 0 && _year % 4 == 0 || _year % 400 == 0) {
      MONTH_DAYS = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    }
    return MONTH_DAYS[month];
  };
  //当前月第一天是周几
  var getDateByYearMonth = function getDateByYearMonth(year, month) {
    var day = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;
    var date = new Date();
    date.setFullYear(year);
    date.setMonth(month, day);
    return date;
  };
  var getWeeksByFirstDay = function getWeeksByFirstDay(year, month) {
    var date = getDateByYearMonth(year, month);
    return date.getDay();
  };
  var formatNum = function formatNum(num) {
    var day = parseInt(num, 10);
    return day < 10 ? "0".concat(day) : "".concat(day);
  };
  var getDayText = function getDayText(line, weekIndex, weekDay, monthDays) {
    var number = line * 7 + weekIndex - weekDay + 1;
    var date = new Date();
    if (number <= 0 || number > monthDays) {
      return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
        className: "day-c",
        children: "\xA0"
      }, weekIndex);
    }
    var today = "".concat(date.getFullYear(), "-").concat(formatNum(date.getMonth() + 1), "-").concat(formatNum(date.getDate()));
    var now = "".concat(year, "-").concat(formatNum(month + 1), "-").concat(formatNum(number));
    var enable = function () {
      var dateEnable = function () {
        if (list.length === 0) {
          return true;
        } else {
          var check = [];
          list.forEach(function (ele) {
            if (ele.val === now && ele.enable === true) {
              check.push(ele);
            }
          });
          return Boolean(check.length > 0);
        }
      }();
      if (new Date(now) >= new Date(start) && new Date(now) <= new Date(end) && dateEnable) {
        return true;
      }
    }();
    return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(react__WEBPACK_IMPORTED_MODULE_1___default.a.Fragment, {
      children: enable ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
        className: "day-c",
        onClick: function onClick() {
          book ? props.onChange(now) : _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default.a.showToast({
            title: '无需预约直接入园',
            icon: 'none',
            duration: 1000
          });
        },
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "day",
          style: today === now ? {
            width: '25px',
            height: '25px',
            borderRadius: '25px',
            backgroundColor: '#f12828',
            color: '#fff',
            margin: '0 auto',
            fontSize: '14px',
            lineHeight: '25px'
          } : {},
          children: number
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "desc",
          style: {
            color: book ? '#26942a' : '#2196f3'
          },
          children: book ? '可约' : '开放'
        })]
      }, weekIndex) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
        className: "day-c",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "day",
          style: today === now ? {
            width: '25px',
            height: '25px',
            borderRadius: '25px',
            backgroundColor: '#e2d7d7',
            color: '#fff',
            margin: '0 auto',
            fontSize: '14px',
            lineHeight: '25px'
          } : {
            color: '#c7c3c3'
          },
          children: number
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "desc",
          style: {
            color: '#fff'
          },
          children: "\u8FC7\u671F"
        })]
      }, weekIndex)
    });
  };
  var setCurrentYearMonth = function setCurrentYearMonth(date) {
    var month = getMonth(date);
    var year = getFullYear(date);
    setLoinsYear(year);
    seLoinstMonth(month);
    setTag(false);
  };
  var monthChange = function monthChange(monthChanged) {
    if (tag) {
      return;
    } else {
      setTag(true);
    }
    var monthAfter = month + monthChanged;
    var operMonth = function () {
      if (monthChanged === 1) {
        return month === 11 ? 0 : monthAfter;
      } else {
        return month === 0 ? 11 : monthAfter;
      }
    }();
    var operYear = function () {
      if (month === 11 && monthChanged === 1) {
        return year + 1;
      } else if (month === 0 && monthChanged === -1) {
        return year - 1;
      } else {
        return year;
      }
    }();
    var now = new Date("".concat(operYear), "".concat(operMonth + 1));
    var limitStart = new Date("".concat(new Date(start).getFullYear()), "".concat(new Date(start).getMonth() + 1));
    var limitEnd = new Date("".concat(new Date(end).getFullYear()), "".concat(new Date(end).getMonth() + 1));
    if (now >= limitStart && now <= limitEnd) {
      var date = getDateByYearMonth(operYear, operMonth);
      setCurrentYearMonth(date);
    } else {
      setTag(false);
    }
  };
  var formatNumber = function formatNumber(num) {
    var _num = num + 1;
    return _num < 10 ? "0".concat(_num) : "".concat(_num);
  };

  // let monthDays = getCurrentMonthDays(month);
  var weekDay = getWeeksByFirstDay(year, month);
  var _startX = 0;
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(react__WEBPACK_IMPORTED_MODULE_1___default.a.Fragment, {
    children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
      className: "loins-calendar",
      onTouchEnd: function onTouchEnd(val) {
        if (_startX > val.changedTouches[0]['clientX'] + 30) {
          monthChange(1);
        }
        if (_startX < val.changedTouches[0]['clientX'] - 30) {
          monthChange(-1);
        }
      },
      onTouchStart: function onTouchStart(val) {
        _startX = val.changedTouches[0]['clientX'];
      },
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
        className: "loins-calendar-tabbar",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_4__[/* AtIcon */ "f"], {
            value: "chevron-left",
            size: "25",
            color: "#297AF8",
            onClick: function onClick() {
              monthChange(-1);
            }
          })
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "loins-calendar-title",
          children: [year, " \u5E74 ", formatNumber(month), "\u6708"]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_4__[/* AtIcon */ "f"], {
            value: "chevron-right",
            size: "25",
            color: "#297AF8",
            onClick: function onClick() {
              monthChange(1);
            }
          })
        })]
      }), WEEK_NAMES.map(function (week, key) {
        return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "title-c",
          children: week
        }, key);
      }), LINES.map(function (l, key) {
        return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "day-content",
          children: WEEK_NAMES.map(function (week, index) {
            return getDayText(key, index, weekDay, getCurrentMonthDays(month, year));
          })
        }, key);
      })]
    })
  });
};
/* harmony default export */ __webpack_exports__["a"] = (Main);

/***/ }),

/***/ "./src/components/calendar/index.scss":
/*!********************************************!*\
  !*** ./src/components/calendar/index.scss ***!
  \********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/components/memu/brand.jsx":
/*!***************************************!*\
  !*** ./src/components/memu/brand.jsx ***!
  \***************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-24 21:59:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/memu/brand.jsx
 */






var Brand = function Brand(props) {
  var love = props.love;
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
    className: "topNav",
    style: {
      position: 'absolute'
    },
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
      style: {
        height: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].reTopH(6), "rpx")
      }
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
      className: "at-row",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
        className: "at-col at-col-1 left",
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default.a.navigateBack({
            delta: 1,
            fail: function fail() {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default.a.navigateTo({
                url: '/pages/index/index'
              });
            }
          });
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["Image"], {
          src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].picUrl, "/memu/back.png"),
          mode: "widthFix",
          className: "topIcon_b"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
        className: "at-col at-col-1",
        onClick: function onClick() {
          props.oper();
        },
        children: love ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["Image"], {
          src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].picUrl, "/memu/love_s.png"),
          mode: "widthFix",
          className: "topIcon_b"
        }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["Image"], {
          src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].picUrl, "/memu/love.png"),
          mode: "widthFix",
          className: "topIcon_b"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
        className: "at-col at-col-9"
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (Brand);

/***/ }),

/***/ "./src/pages/brand/index.jsx":
/*!***********************************!*\
  !*** ./src/pages/brand/index.jsx ***!
  \***********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./index.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/brand/index.jsx");


var config = {"navigationBarTitleText":"","navigationStyle":"custom","enableShareAppMessage":true,"transparentTitle":"always","titlePenetrate":"YES"};

_node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].enableShareAppMessage = true
var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/brand/index', {root:{cn:[]}}, config || {}))



/***/ })

},[["./src/pages/brand/index.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=index.js.map
page {
    background-color: #EDEDED;
}
.bindBtn {
    border-radius: 10px;
    text-align: center;
    margin-top: 20px;
    font-weight: bold;
    color: #fff;
    background-color: #02b921;
    padding: 20px 0;
    vertical-align: top;
}
.cardList {
    padding: 20px 30px;
    overflow: hidden;
}
.agree {
    width: 35px;
    height: 35px;
    border: 2px solid #02b921;
    .ok {
        width: 35px;
        height: 35px;
    }
    .at-icon {
        margin-top: -18rpx;
    }
}
.at-curtain {
    background-color: rgba(0, 0, 0, 0.7);
}
.at-float-layout__container {
    min-height: 10px;
}
.at-float-layout .layout-body__content {
    min-height: 10px;
}
.at-float-layout .layout-body {
    min-height: 10px;
}
.mask {
   position: fixed;
   width: 100%;
   height: 100%;
   background-color: #000; 
   opacity: 0.7;
   z-index: 101;
}

.memu {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 100;
}

.topNav {
    position: fixed;
    top:0;
    z-index: 20;
    width: 100%;
    padding-bottom: 15px;
    .topIcon {
        width: 60px;
        height: 60px;
        margin-right: 20px;
    }
    .topIcon_b {
        width: 50px;
        height: 50px;
    }
    .left {
        margin-left: 25px;
    }
}
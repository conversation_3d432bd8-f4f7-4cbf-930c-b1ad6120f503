import { Component, useState, useEffect } from 'react'
import Taro, { useReachBottom } from '@tarojs/taro';
import { View } from '@tarojs/components'
import tools from '@/utils/tools'
import Item from '@/components/brand/item'
import { AtIcon, AtTabs } from "taro-ui"
import './index.scss'

function Index (props) { 
  const [init, setInit] = useState([])
  const [initPage, setInitPage] = useState()
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(-1)
  const tabList = [{ title: '景区预约' }, { title: '预约记录' }]
  useEffect( async () => {
    Taro.showLoading({
      mask: true,
      title: '读取中'
    })
    const data = await tools.Api.brandList({
      line_id: tools.line_code,
      order : "km",
      point: true,
      is_book: 0,
      page : {
          current: page,
          pageSize : 20
      }
    })
    Taro.hideLoading()
    if (data.code === 200) {
      setInit([
        ...init,
        ...data.data.list
      ])
      setInitPage(data.data.page)
      setTotal(data.data.page.total)
    }
  }, [ page ])

  useReachBottom(() => {
    const nextPage = initPage.current + 1
    if (nextPage <= initPage.totalPage) {
      setPage(nextPage)
    } else {
      Taro.showToast({
        title: '暂无更多内容',
        icon: 'none',
        duration: 2000
      })
    }
  })

  return (
    <View className='index'>
      <AtTabs current={0} tabList={tabList} onClick={(v) => {
        if (v === 1) {
          Taro.navigateTo({
            url: '/pages/my/book'
          })
        }
      }}></AtTabs>
      {(total === 0) ? <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "16px"}}>
          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>
          <View style={{marginTop: "10px" , color: "#848181"}}>暂无需预约景区</View>
        </View> :
        <View style={{marginTop: '20px'}}>
          {init.map(v => <Item data={v} className="list" data={v} cname="images_l"/>)}   
        </View>
        }
      <View style={{height: '30px'}}></View>  
    </View>
  )
}
export default Index;

/*
 * @Author: 高超
 * @Date: 2021-10-30 10:32:40
 * @LastEditTime: 2022-07-21 11:03:39
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/app.config.js
 * love jiajia
 */

// 支付宝插件，打包时要复制到下面的 config 里面
// #ifdef MP-ALIPAY
// "plugins": {
//   "myPlugin": {
//     "version": "*", // 自动选择最新版本
//     "provider": "2021004123636623"
//   },
// },
// #endif

export default {
  pages: [
    'pages/index/index',
    'pages/qr/index',
    'pages/map/index',
    'pages/brand/index',
    'pages/brand/buy',
    'pages/tips/index',
    'pages/pay/index',
    'pages/wxshop/productBuy'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '北京风景名胜区协会',
    navigationBarTextStyle: 'black'
  },
  "plugins": {
    "myPlugin": {
      "version": "*", // 自动选择最新版本
      "provider": "2021004123636623"
    },
  },
  "requiredPrivateInfos": [
    "chooseLocation",
    "onLocationChange",
    "startLocationUpdate"
  ],
  "permission": {
    "scope.userLocation": {
      "desc": "你的位置信息将用于展示景区位置距离", // 高速公路行驶持续后台定位
    }
  },
  subPackages: [
    {
      "root": "pages/bind",
      "pages": [
        'index',
        'txt',
        'rel',
        'add'
      ]
    },
    {
      "root": "pages/sreach",
      "pages": [
        'index',
        'list',
        'type',
        'topic',
        'listbuy',
        'book'
      ]
    },
    {
      "root": "pages/my",
      "pages": [
        'index',
        'book',
        'use',
        'card',
        'love',
        'order',
      ]
    },
    {
      "root": "pages/news",
      "pages": [
        'index',
        'info'
      ]
    },
    {
      "root": "pages/buy",
      "pages": [
        'index',
        'txt',
        'ok'
      ]
    }
  ]
}

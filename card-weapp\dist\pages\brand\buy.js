(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/brand/buy"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/brand/buy.jsx":
/*!*****************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/brand/buy.jsx ***!
  \*****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var taro_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! taro-skeleton */ "./node_modules/taro-skeleton/dist/index.umd.js");
/* harmony import */ var taro_skeleton__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(taro_skeleton__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _components_brand_ticket__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/brand/ticket */ "./src/components/brand/ticket.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./index.scss */ "./src/pages/brand/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__);














function Index(props) {
  var query = _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].getQuery();
  var login = Object(react_redux__WEBPACK_IMPORTED_MODULE_5__[/* useSelector */ "c"])(function (state) {
    return state.login;
  });
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(true),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(null),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState3, 2),
    init = _useState4[0],
    setInit = _useState4[1];
  var _useState5 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])([]),
    _useState6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState5, 2),
    goodList = _useState6[0],
    setGoodList = _useState6[1];
  var _useState7 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])({
      num: 0,
      good_id: '--',
      list: [],
      name: '--'
    }),
    _useState8 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState7, 2),
    card = _useState8[0],
    setCard = _useState8[1];
  var brandCode = query.data.v;
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var data, goods, cardList, enableCard;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (!(login && Boolean(brandCode))) {
            _context.next = 15;
            break;
          }
          _context.next = 3;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].Api.brandInfoZh(brandCode);
        case 3:
          data = _context.sent;
          _context.next = 6;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].Api.brandGoodZh(brandCode);
        case 6:
          goods = _context.sent;
          if (!(data.code === 200)) {
            _context.next = 14;
            break;
          }
          _context.next = 10;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].getCardList({
            list: data.data.brand.templet_list_enable
          });
        case 10:
          cardList = _context.sent;
          if (cardList.enable_card_num > 0) {
            enableCard = cardList.card_list.filter(function (v) {
              return v.enable === true;
            });
            setCard({
              num: enableCard.length,
              good_id: enableCard[0].good_id,
              list: enableCard
            });
          }
          setInit(data.data);
          setLoading(false);
        case 14:
          if (goods.code === 0) {
            setGoodList(goods.data.ticket);
          }
        case 15:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [login]);
  Object(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__["useShareAppMessage"])(function () {
    return {
      title: init.brand.name,
      path: "/pages/brand/buy?v=".concat(brandCode),
      imageUrl: "".concat(init.brand.logo, "?x-oss-process=image/resize,m_fill,h_400,w_400")
    };
  });
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
    children: init === null && Boolean(brandCode) && goodList.length === 0 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_skeleton__WEBPACK_IMPORTED_MODULE_8___default.a, {
      title: true,
      row: 30,
      loading: loading
    }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "topNav",
        style: {
          position: 'absolute'
        },
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          style: {
            height: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].reTopH(6), "rpx")
          }
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1 left",
            onClick: function onClick() {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateBack({
                delta: 1,
                fail: function fail() {
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
                    url: '/pages/index/index'
                  });
                }
              });
            },
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/memu/back.png"),
              mode: "widthFix",
              className: "topIcon_b"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-9"
          })]
        })]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "brand_top_pic",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Swiper"], {
          className: "brand_swiper",
          indicatorColor: "#999",
          indicatorActiveColor: "#fff",
          circular: true,
          indicatorDots: true,
          autoplay: true,
          children: init.brand.images.map(function (v) {
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["SwiperItem"], {
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
                src: v.path,
                mode: "heightfix",
                className: "images"
              })
            });
          })
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "brand_content",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-9",
            children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "title",
              children: init.brand.name
            }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "memo",
              style: {
                padding: 0
              },
              children: init.brand.tagsname.map(function (v, index) {
                return index <= 3 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "tips_three",
                  style: index === 0 ? {
                    marginLeft: '0'
                  } : {},
                  children: v.tagname
                }) : null;
              })
            })]
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-3",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              style: {
                marginTop: '44px',
                color: '#777575',
                fontSize: '14px',
                textAlign: 'right'
              },
              children: ["\u5DF2\u552E: ", init.brand.ordernum + init.brand.ordernum_show]
            })
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.makePhoneCall({
              phoneNumber: init.brand.tel
            });
          },
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/info/time.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            children: ["\u8425\u4E1A\u65F6\u95F4 ", init.brandTime === null ? '全天' : init.brandTime[0]]
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtIcon */ "f"], {
              value: "phone",
              size: "18",
              color: "#afafaf"
            })
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          onClick: function onClick() {
            var wxMap = _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].bdMap_to_txMap(init.brand.latitude, init.brand.longitude);
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.openLocation({
              latitude: wxMap.lat,
              longitude: wxMap.lng,
              scale: 15
            });
          },
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/info/map.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            children: "".concat(init.brand.province).concat(init.brand.city).concat(init.brand.address)
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtIcon */ "f"], {
              value: "chevron-right",
              size: "18",
              color: "#afafaf"
            })
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row list_show",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/info/shop.png"),
              mode: "widthFix",
              className: "icon"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-10 text",
            style: {
              fontWeight: 'bold'
            },
            children: "\u4EA7\u54C1\u9884\u8BA2"
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-1",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtIcon */ "f"], {
              value: "chevron-down",
              size: "18",
              color: "#afafaf"
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {})]
        }), goodList.length > 0 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_components_brand_ticket__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], {
          allT: goodList,
          vid: 0,
          rank: 1,
          openmm: function openmm() {},
          weixinadinfo: false,
          coupon: [],
          card: card.num
        }) : null]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "brand_title",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "t_icon"
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "t_name",
            children: "\u7279\u8272\u4ECB\u7ECD"
          })]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "brand_html",
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["RichText"], {
            nodes: _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].removeCss(init.brand.memo)
          })
        })]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        style: {
          height: '80px'
        }
      })]
    })
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Index);

/***/ }),

/***/ "./src/components/brand/ticket.js":
/*!****************************************!*\
  !*** ./src/components/brand/ticket.js ***!
  \****************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _components_ele_timer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ele/timer */ "./src/components/ele/timer.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__);












var Ticket = function Ticket(props) {
  var allT = props.allT,
    openmm = props.openmm,
    weixinadinfo = props.weixinadinfo,
    coupon = props.coupon;
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_4__["useState"])(_utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showNum),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_useState, 2),
    showNum = _useState2[0],
    setShowNum = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_4__["useState"])(false),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_useState3, 2),
    tipsBar = _useState4[0],
    setTipsBar = _useState4[1];
  var _useState5 = Object(react__WEBPACK_IMPORTED_MODULE_4__["useState"])({
      tips: []
    }),
    _useState6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_useState5, 2),
    selData = _useState6[0],
    setSelData = _useState6[1];
  var _useState7 = Object(react__WEBPACK_IMPORTED_MODULE_4__["useState"])([]),
    _useState8 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_useState7, 2),
    showList = _useState8[0],
    setShowList = _useState8[1];
  var _useState9 = Object(react__WEBPACK_IMPORTED_MODULE_4__["useState"])(coupon),
    _useState10 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_useState9, 2),
    canCoupon = _useState10[0],
    setCanCoupon = _useState10[1];
  var brandAid = function () {
    try {
      return Boolean(weixinadinfo) ? weixinadinfo.toString().split('.')[0] : 0;
    } catch (e) {
      return 0;
    }
  }();
  Object(react__WEBPACK_IMPORTED_MODULE_4__["useEffect"])(function () {
    setCanCoupon(coupon);
  }, [coupon]);
  var showDate = ['指定日期', '超长有效'];
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "brand_tt",
        style: {
          padding: 0
        },
        children: allT.map(function (val, index) {
          return val.good.id && index < showNum ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "at-row brand_tt_per",
              onClick: /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
                var checked, allPrice;
                return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      if (!(parseInt(val.good.place_origin, 10) === 0)) {
                        _context.next = 6;
                        break;
                      }
                      setSelData(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({}, val.good), {}, {
                        name: val.name,
                        itemname: null
                      }));
                      openmm(false);
                      setTipsBar(true);
                      _context.next = 17;
                      break;
                    case 6:
                      checked = showList.filter(function (item) {
                        return item.goods_id === val.good.id;
                      });
                      if (!(checked.length > 0)) {
                        _context.next = 11;
                        break;
                      }
                      setShowList([]);
                      _context.next = 17;
                      break;
                    case 11:
                      _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showLoading({
                        title: '读取中',
                        mask: true
                      });
                      _context.next = 14;
                      return _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].Api.getBrandDatePrice({
                        id: val.good.id
                      });
                    case 14:
                      allPrice = _context.sent;
                      setShowList(allPrice.data.rules);
                      _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
                    case 17:
                    case "end":
                      return _context.stop();
                  }
                }, _callee);
              })),
              children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                className: "at-col at-col-8",
                children: [parseInt(val.good.place_origin, 10) === 1 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  style: {
                    height: '10rpx'
                  }
                }) : null, /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "at-col--wrap brand_tt_name",
                  children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    className: "brand_tt_name",
                    children: val.name
                  })
                }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "brand_tt_tags",
                  children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    className: "brand_tt_tips",
                    style: {
                      color: '#28bb65'
                    },
                    children: _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getlimt(val.good.limittype, val.good.limitnum)
                  }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    className: "brand_tt_shu",
                    children: "|"
                  }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    className: "brand_tt_tips",
                    style: {
                      color: '#e23b14'
                    },
                    children: ['随时退', '有效可退', '条件退'][parseInt(val.good.if_refund)]
                  }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    style: _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showUseDate(val.good.is_book, val.good.open_time, val.good.close_time) !== -1 ? {
                      display: 'inline-block'
                    } : {
                      display: 'none'
                    },
                    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                      className: "brand_tt_shu",
                      children: "|"
                    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                      className: "brand_tt_tips",
                      style: {
                        color: '#ff9800'
                      },
                      children: showDate[_utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showUseDate(val.good.is_book, val.good.open_time, val.good.close_time)]
                    })]
                  }), parseInt(val.good.place_origin, 10) === 1 ? null : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                      className: "brand_tt_shu",
                      children: "|"
                    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                      className: "brand_tt_tips",
                      style: {
                        color: '#d02114',
                        fontWeight: 'bold'
                      },
                      children: "\u8D2D\u4E70\u987B\u77E5 \u5FC5\u8BFB"
                    })]
                  }), val.good.buy_time === '1' && parseInt(val.good.place_origin, 10) === 0 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                    style: {
                      width: '60%',
                      margin: '10rpx 0'
                    },
                    children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_components_ele_timer__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], {
                      end: val.good.end_time,
                      start: val.good.start_time
                    })
                  }) : null]
                })]
              }), parseInt(val.good.place_origin, 10) === 0 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                className: "at-col at-col-4",
                style: {
                  textAlign: 'right'
                },
                children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "brand_buy_price",
                  children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    className: "price_t",
                    children: "\xA5"
                  }), " ", _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getPrice(val.good.share_price), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    className: "brand_buy_showp",
                    children: ["\xA5", _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getPrice(val.good.show_price)]
                  })]
                }), val.good.buy_time === '1' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "brand_buy_btn_time ",
                  children: "\u62A2\u8D2D"
                }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "brand_buy_btn",
                  children: "\u8D2D\u4E70"
                })]
              }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                className: "at-col at-col-4",
                style: {
                  textAlign: 'right'
                },
                children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "brand_buy_price",
                  children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    className: "price_t",
                    children: "\xA5"
                  }), " ", _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getPrice(val.good.share_price), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                    className: "brand_buy_showqi",
                    children: "\u8D77"
                  })]
                }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  style: {
                    color: '#fff',
                    fontSize: '26rpx',
                    background: '#03A9F4',
                    width: '62%',
                    textAlign: 'center',
                    marginLeft: '43%',
                    borderRadius: '25rpx',
                    padding: '8rpx 0'
                  },
                  children: "\u7ACB\u5373\u9884\u5B9A"
                })]
              })]
            }, val.id), showList.map(function (rules) {
              return rules.goods_id == val.good.id ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                className: "at-row brand_tt_per",
                style: {
                  background: '#fbfbfb'
                },
                onClick: function onClick() {
                  setSelData(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])({}, val.good), {}, {
                    rulescode: rules.title_code,
                    show_price: rules.show_price,
                    buy_price: rules.buy_price,
                    share_price: rules.share_price,
                    name: val.name,
                    itemname: rules.title
                  }));
                  openmm(false);
                  setTipsBar(true);
                },
                children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "at-col at-col-8",
                  children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                    className: "at-col--wrap brand_tt_name",
                    children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                      className: "brand_tt_name",
                      style: {
                        fontSize: '26rpx',
                        color: '#636161'
                      },
                      children: rules.title
                    })
                  }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                    className: "brand_tt_tags",
                    children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                      style: {
                        color: 'red'
                      },
                      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                          className: "brand_tt_tips",
                          style: {
                            color: '#6fb2f9'
                          },
                          children: "\u8D2D\u4E70\u987B\u77E5"
                        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                          className: "brand_tt_shu",
                          children: "|"
                        })]
                      }), _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].dateInfo(rules.stime, rules.etime, rules.enable_week)]
                    })
                  })]
                }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                  className: "at-col at-col-4",
                  style: {
                    textAlign: 'right'
                  },
                  children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                    className: "brand_buy_price",
                    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                      className: "price_t",
                      children: "\xA5"
                    }), " ", _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getPrice(rules.share_price), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                      className: "brand_buy_showp",
                      children: ["\xA5", _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getPrice(rules.show_price)]
                    })]
                  }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                    className: "brand_buy_btn",
                    children: "\u8D2D\u4E70"
                  })]
                })]
              }, rules.code) : null;
            })]
          }) : null;
        })
      }), allT.length > _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showNum ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "brand_show_all",
        onClick: function onClick() {
          if (showNum === _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showNum) {
            setShowNum(99);
          } else {
            setShowNum(_utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showNum);
          }
        },
        children: showNum > _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showNum ? "\u6536\u8D77" : "\u67E5\u770B\u5168\u90E8".concat(allT.length, "\u7C7B\u4EA7\u54C1")
      }) : null]
    }), tipsBar ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtFloatLayout */ "d"], {
      isOpened: tipsBar,
      onClose: function onClose() {
        setTipsBar(false);
        openmm(true);
      },
      title: "\u8D2D\u4E70\u987B\u77E5",
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "brand_tips",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-row",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-3",
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
              src: selData.home_recommended_images,
              className: "brand_good_img",
              mode: 'aspectFill'
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "at-col at-col-9",
            style: {
              paddingLeft: '15rpx'
            },
            children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "at-col--wrap brand_tt_name",
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                className: "brand_tt_name",
                children: _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getSamll(selData.name, 15)
              })
            }), selData.itemname !== null ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              children: selData.itemname
            }) : null, /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              className: "brand_tt_tags",
              style: selData.itemname !== null ? {
                padding: 0
              } : {},
              children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                className: "brand_tt_tips",
                style: {
                  color: '#28bb65'
                },
                children: _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getlimt(selData.limittype, selData.limitnum)
              }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                className: "brand_tt_shu",
                children: "|"
              }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                className: "brand_tt_tips",
                style: {
                  color: '#e23b14'
                },
                children: ['随时退', '有效可退', '条件退'][parseInt(selData.if_refund)]
              }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                style: _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showUseDate(selData.is_book, selData.open_time, selData.close_time) !== -1 ? {
                  display: 'inline-block'
                } : {
                  display: 'none'
                },
                children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                  className: "brand_tt_shu",
                  children: "|"
                }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                  className: "brand_tt_tips",
                  style: {
                    color: '#ff9800'
                  },
                  children: showDate[_utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].showUseDate(selData.is_book, selData.open_time, selData.close_time)]
                })]
              }), selData.buy_time === '1' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                style: {
                  width: '60%',
                  margin: '10rpx 0'
                },
                children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_components_ele_timer__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], {
                  end: selData.end_time,
                  start: selData.start_time
                })
              }) : null]
            })]
          })]
        }), selData.tips.length === 0 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          children: selData.content_web
        }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          style: {
            paddingBottom: '50rpx'
          },
          children: [" ", selData.tips.map(function (txt) {
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
              children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                style: {
                  margin: '15rpx 0 5rpx 0',
                  fontWeight: 'bold'
                },
                children: txt.title
              }), txt.memo.includes('<span class="none"></span>') ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["RichText"], {
                nodes: _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].removeCss(txt.memo),
                style: {
                  color: '#585656'
                }
              }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
                style: {
                  color: '#585656'
                },
                children: txt.memo
              }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
                style: {
                  height: '30rpx'
                }
              })]
            }, selData.title);
          })]
        })]
      })
    }) : null, tipsBar ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      className: "brand_bar",
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "at-row",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "at-col at-col-8",
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
            className: "brand_buy_price",
            style: {
              fontSize: '40rpx'
            },
            children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
              className: "price_t",
              children: "\xA5"
            }), " ", _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getPrice(selData.share_price), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
              className: "brand_buy_showp",
              children: ["\xA5", _utils_tools__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].getPrice(selData.show_price)]
            })]
          })
        })
      })
    }) : null]
  });
};
Ticket.defaultProps = {
  allT: [],
  coupon: []
};
/* harmony default export */ __webpack_exports__["a"] = (Ticket);

/***/ }),

/***/ "./src/components/ele/timer.js":
/*!*************************************!*\
  !*** ./src/components/ele/timer.js ***!
  \*************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! moment */ "./node_modules/moment/dist/moment.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__);








var Timer = function Timer(props) {
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])(['--', '--', '--']),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState, 2),
    init = _useState2[0],
    setInit = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])('--'),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState3, 2),
    showTxt = _useState4[0],
    setShowTxt = _useState4[1];
  var _useState5 = Object(react__WEBPACK_IMPORTED_MODULE_1__["useState"])('m'),
    _useState6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(_useState5, 2),
    showStatus = _useState6[0],
    setShowStatus = _useState6[1];
  var timer = null;
  Object(react__WEBPACK_IMPORTED_MODULE_1__["useEffect"])(function () {
    var start = props.start,
      end = props.end;
    var now = Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])();
    var end_diff = Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(end).diff(now);
    var start_diff = Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(start).diff(now);
    var timer_status = start_diff > 0 ? 'will' : 'buy';
    setShowStatus(timer_status);
    var operTimer = timer_status === 'buy' ? {
      day: Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(end).diff(now, 'day'),
      ms: moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].duration(Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(end).diff(now), 'milliseconds')
    } : {
      day: Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(start).diff(now, 'day'),
      ms: moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].duration(Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(start).diff(now), 'milliseconds')
    };
    //if (timer_status === 'buy') {
    setShowTxt(timer_status === 'buy' ? '结束于' : '开始于');
    //if ( operTimer.day > 2) {
    if (timer_status === 'buy') {
      setInit(Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(end).format('MM-DD-HH').split('-'));
    } else {
      setInit(Object(moment__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(start).format('MM-DD-HH').split('-'));
    }
    setShowStatus('m');
    //} else {
    // timer = setTimeout(() => {
    //   const timer_all = operTimer.ms;
    //   const timer_hh = (parseInt(timer_all.days(), 10) * 24) + timer_all.hours();
    //   setInit([timer_hh, 
    //         (parseInt(timer_all.minutes(), 10) < 10 ? `0${timer_all.minutes()}` : timer_all.minutes()), 
    //         (parseInt(timer_all.seconds(), 10) < 10 ? `0${timer_all.seconds()}` : timer_all.seconds())
    //         ]);
    // }, 1000);
    // const timer_all = operTimer.ms;
    // const timer_hh = (parseInt(timer_all.days(), 10) * 24) + timer_all.hours();
    // setInit([timer_hh, 
    //       (parseInt(timer_all.minutes(), 10) < 10 ? `0${timer_all.minutes()}` : timer_all.minutes()), 
    //       (parseInt(timer_all.seconds(), 10) < 10 ? `0${timer_all.seconds()}` : timer_all.seconds())
    //       ]);
    // setShowStatus('s');
    //}
    //}
  }, []);
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
    children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
      className: "at-row",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
        className: "at-col at-col-1",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_4__[/* AtIcon */ "f"], {
          value: "bell",
          size: "16",
          color: "#fa6f14"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
        className: "at-col at-col-4",
        style: {
          textAlign: 'center'
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["Text"], {
          style: {
            fontSize: '24rpx'
          },
          children: showTxt
        })
      }), showStatus === 's' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
        className: "at-col at-col-7",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "time_item time_month",
          children: init[0]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["Text"], {
          style: {
            fontSize: '20rpx'
          },
          children: "\u65F6"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "time_item time_month",
          children: init[1]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["Text"], {
          style: {
            fontSize: '20rpx'
          },
          children: "\u5206"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "time_item time_month",
          children: init[2]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["Text"], {
          style: {
            fontSize: '20rpx'
          },
          children: "\u79D2"
        })]
      }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
        className: "at-col at-col-7",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "time_item time_month",
          children: init[0]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["Text"], {
          style: {
            fontSize: '20rpx'
          },
          children: "\u6708"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "time_item time_month",
          children: init[1]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["Text"], {
          style: {
            fontSize: '20rpx'
          },
          children: "\u65E5"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["View"], {
          className: "time_item time_month",
          children: init[2]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__["Text"], {
          style: {
            fontSize: '20rpx'
          },
          children: "\u65F6"
        })]
      })]
    })
  });
};
Timer.options = {
  addGlobalClass: true
};
Timer.defaultProps = {
  start: '2020-12-12',
  end: '2020-12-12'
};
/* harmony default export */ __webpack_exports__["a"] = (Timer);

/***/ }),

/***/ "./src/pages/brand/buy.jsx":
/*!*********************************!*\
  !*** ./src/pages/brand/buy.jsx ***!
  \*********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_buy_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./buy.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/brand/buy.jsx");


var config = {"navigationBarTitleText":"风景名胜年票","navigationStyle":"custom","enableShareAppMessage":true,"transparentTitle":"always","titlePenetrate":"YES"};

_node_modules_babel_loader_lib_index_js_buy_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].enableShareAppMessage = true
var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_buy_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/brand/buy', {root:{cn:[]}}, config || {}))



/***/ })

},[["./src/pages/brand/buy.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=buy.js.map
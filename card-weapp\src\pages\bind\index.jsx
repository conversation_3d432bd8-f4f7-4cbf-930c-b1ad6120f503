/*
 * @Author: 高超
 * @Date: 2021-11-04 17:05:46
 * @LastEditTime: 2022-02-16 17:37:22
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/bind/index.jsx
 * love jiajia
 */
import { Component, useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { View, Text, Button, Image } from '@tarojs/components'
import { AtIcon, AtButton } from 'taro-ui'
import Bg from '@/components/tool/bg'
import tools from "@/utils/tools"
import "./index.scss"

function Index (props) {
  const params =  tools.getQuery()
  const good_key = Boolean(params.data.key) ? params.data.key : ""
  console.warn("good_key -------------", good_key, Boolean(params.data.key), params.data, params.data.key)
  const [check, setCheck] = useState(false)
  const [tel, setTel] = useState(false)
  useEffect( async () => {
    const userInfo = tools.getDataSafe("userInfo")
    if (userInfo !== null) {
      setTel(Boolean(userInfo.user_tel))
    } else {
      setTel(false)
    }
  }, [tools.getDataSafe("userInfo")])
  return (
    <View className='pageAdd'>
       <Bg/>
       <Image src={`${tools.picUrl}/bind/code.jpg`} style={{width: "100%"}} mode="widthFix"/>
       <View style={{textAlign:"center"}}>
         <View className="title">北京风景名胜年票</View>
         <View className="info">开通年票全年便捷入园</View>
         <Image src={`${tools.picUrl}/bind/link.jpg`} mode="widthFix" style={{marginTop : "30px"}}/>
       </View>
      <View style={{marginLeft: "15px", marginTop: "15px"}}>
        <View className='at-row at-row--wrap'>
        <View className='at-col at-col-1'>
          <View className="agree" onClick={() => {
            setCheck(!check)
          }}>
            <View className="ok">
              {check ? <AtIcon value='check' size='17' color='#02b921'/> : null}
            </View>
          </View>
        </View>
        <View className='at-col at-col-10 at-col--wrap'>
          <View className="txt">
            开通并同意<Text style={{color: "#03A9F4"}} onClick={() => {
              Taro.navigateTo({
                url: '/pages/bind/txt',
              });
            }}>《用户授权协议》</Text>，并授权<Text style={{fontWeight:"bold"}}>北京风景名胜协会</Text>使用您的姓名、证件号、手机号进行实名开卡，以便为您提供更好的电子卡服务
          </View>
        </View>
        </View>
      </View>
      {check ?
         (tel || Taro.getEnv() === Taro.ENV_TYPE.ALIPAY) ? 
          <View className={`btn ${(check) ? '' : 'disable'}`} onClick={() => {
            if (!check) {
              Taro.showToast({
                title: '请您同意《用户授权协议》后开通',
                icon: 'none',
                duration: 2000
              })
            } else {
              Taro.redirectTo({
                url: '/pages/bind/rel?key=' + good_key,
              });
            }
          }}>立即开通</View>
         :
         <AtButton className="btn" openType="getPhoneNumber"  onGetPhoneNumber={tools.getPhoneNumber.bind(tools)}>立即开通</AtButton> :
         <AtButton className="btn disable" onClick={() => {
          Taro.showToast({
            title: '请您同意《用户授权协议》后开通',
            icon: 'none',
            duration: 2000
          })
        }}>立即开通</AtButton>
      }
      {<View style={{textAlign:'center' , paddingTop: '20px', fontSize: '14px', color: '#03A9F4'}} onClick={() => {
          Taro.navigateTo({url: '/pages/buy/index'})
      }}>如尚未持有年票，可点击此处立即购买</View>}
    </View>
  )
}
export default Index;

/*
 * @Author: 高超
 * @Date: 2022-07-21 10:41:16
 * @LastEditTime: 2022-07-22 14:14:30
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/pay/index.js
 * love jiajia
 */
import { useState, useEffect } from 'react'
import Taro from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';
import tools from '@/utils/tools';
import './index.scss';

function Pay(props) {
    const { params: {id} } = Taro.useRouter();
    useEffect( async () => {
      Taro.showLoading({
        title: '发起中',
      })
      const payData = await tools.Api.pay({code: id, openid: tools.getData('userInfo').code, appid: "wx99dcb5f0415eb002"});
        Taro.hideLoading();
        console.log(payData.code)
        if (payData.code === 0) {
          Taro.requestPayment({
            timeStamp: payData.data.timeStamp,
            nonceStr: payData.data.nonceStr,
            package: payData.data.package,
            signType: payData.data.signType,
            paySign: payData.data.sign,
            success: function () {
              Taro.redirectTo({
                url: '/pages/tips/index?type=pay100',
              });
            },
            fail: function () {
              Taro.redirectTo({
                url: '/pages/tips/index?type=pay105',
              });
            }
          })
        } else {
          Taro.redirectTo({
            url: '/pages/tips/index?type=pay106',
          });
        }
    }, [])

    return (
        <View style="text-align: center;padding-top: 100px;">
          <Image src={`https://test.qqyhmmwg.com/res/wbg/w.png`} className='pay_img' />
          <View className='pay_memo'>支付中请稍等</View>
          <View className='pay_txt'>支付跳转中, 支付完成, 页面自动跳转</View>
        </View>
    )
}

export default Pay
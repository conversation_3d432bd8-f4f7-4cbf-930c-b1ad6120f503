page {
  background-color: #3CB980;
}

.user_img {
  width: 60rpx;
  height: 60rpx;
  border-radius: 60rpx;
  background-color: #fff;
}

.user_good_id {
  padding-top: 11rpx;
  padding-left: 20rpx;
  color: #fff;
  font-weight: bold;
}

.qr {
  background-color: #fff;
  border-radius: 18rpx;
  height: 1060rpx;
  margin-top: 25rpx;
}

.qr .title {
  width: 100%;
  text-align: center;
  color: #000;
  font-size: 32rpx;
  font-weight: bold;
  padding: 30rpx 0;
}

.qr .note {
  width: 80%;
  margin: 10rpx auto;
  border-radius: 10rpx;
}

.reload {
  margin-top: 8rpx;
}

.reload .txt {
  font-size: 28rpx;
  color: #49aa7f;
  padding-left: 10rpx;
}

.at-grid__flex .content-inner__img {
  width: 55rpx;
  height: 55rpx;
}

.at-grid__flex .content-inner__text {
  font-size: 25rpx;
  color: rgb(78, 79, 92);
}

.near {
  margin-top: 13rpx;
  height: 40rpx;
  background-color: #bff1dd;
  border-radius: 18rpx;
  padding: 0 0 50rpx 0;
  font-size: 30rpx;
  color: #373E47;
  font-weight: bold;
  height: 60rpx;
}

.near .list {
  height: 280rpx;
  background-color: #fff;
  border-radius: 18rpx;
  margin-top: -1rpx;
  margin-bottom: 30rpx;
  text-align: center;
  color: rgb(124, 121, 121);
  font-weight: normal;
}

.near .none {
  width: 120rpx;
  height: 120rpx;
  margin: 30rpx auto;
  text-align: center;
}

.help {
  border-radius: 18rpx;
  margin-top: 20rpx;
  text-align: center;
}

.at-noticebar {
  border-radius: 10rpx;
}

.at-icon {
  margin-top: 0;
}

.user_img_show {
  width: 180rpx;
  height: 220rpx;
  border: 1rpx solid #bff1dd;
  border-radius: 10rpx;
}

.cardBtn {
  display: inline-block;
  border: 1rpx solid #fff;
  padding: 4rpx 10rpx;
  border-radius: 9rpx;
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #fff;
}

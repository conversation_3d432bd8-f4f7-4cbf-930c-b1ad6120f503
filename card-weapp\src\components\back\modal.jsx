import { View, Text, Button } from '@tarojs/components'
import { AtModal, AtModalHeader, AtModalContent, AtModalAction } from "taro-ui"
import "./index.scss"

const Modal = (props) => {
    const {open, title, ok, close} = props;
    return (
        <AtModal isOpened={open}>
        <AtModalHeader>{title}</AtModalHeader>
        <AtModalContent>
          {props.children}
        </AtModalContent>
        <AtModalAction> 
            {Boolean(close) ? <Button onClick={() => close.fun()}>{close.name}</Button> : null}
            <Button onClick={() => ok.fun()}>{ok.name}</Button> 
        </AtModalAction>
      </AtModal>
    )
}
export default Modal 
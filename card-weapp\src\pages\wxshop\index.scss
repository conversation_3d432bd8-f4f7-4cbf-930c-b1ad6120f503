page {
    overflow: auto !important;
    background-color: #f3f3f3;
}
.good_live_top {
    width: 100%;
}
.good_live_title {
    padding: 20px;
    background-color: #ffffff;
}
.good_live_title {
    font-size: 50px;
    font-weight: bold;
    color: #ec7413;
}
.buy_icon {
    font-size: 40px;
    padding-right: 10px;
}

.show_icon {
    font-size: 30px;
    padding-left: 20px;
    font-weight: normal;
    color:#aaaaaa;
    text-decoration:line-through;
}

.good_title{
    font-weight: normal;
    color: #000;
    font-size: 35px;
    margin-bottom: 8px;
}
.good_memo {
    font-size: 30px;
    color:#aaaaaa;
    font-weight: normal;
}
.good_live_brand {
    padding: 20px;
    border-top: 1px solid #e6e6e6;
    background-color: #ffffff;
    font-size: 30px;
    color: #000000;
}
.brand_map {
    margin-top: 10px;
    width: 160px;
    height: 80px;
    margin-left: 15px;
}
.good_live_tips {
  margin-top: 15px;
    padding: 15px 30px;
    line-height: 55px;
    color: #000;
    font-size: 30px;
    background-color: #ffffff;
}
.good_live_buybtn {
    background-color: #ffffff;
    padding: 10px;
    position: fixed;
    z-index: 100;
    bottom: 0;
    width: 100%;
}
.go_btn {
    font-size: 32px;
    font-weight: bold;
    border-radius: 45px;
    color: #ffffff;
    text-align: center;
    padding: 14px 0;
    margin-top: 10px;
}
.go_buy {
    border: 0;
    background-color: #ec7413;
    color: #ffffff;
    
}
.go_out {
    border: 0;
    background-color: #aaaaaa;
}
.video_hideBtn{
    position: absolute;
    top: 0;
    width: 100%;
    height: 130px;
    left: 0;
    border: 0;
    opacity: 0;
}
.btn_share {
    position: relative;
    margin-top: 25px;
}
.topicView {
    width: 100%;
}
.topicTop {
    width: 100%;
}
.timeTab {
    height: 70px;
    border-bottom: 1px solid #E3E3E3;
    background: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 100;
}
.timeTab .tabp {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 49%;
    font-size: 30px;
    color: #4E4D4D;
    font-weight: bold;
    height: 70px;
    
}
.timeTab .tabs {
    padding: 0 0 18px 0;
}
.timeTab .cur {
    border-bottom: 6px solid #FD5702;
}
.timeTab .shax {
    box-shadow: 8px 0px 8px 0px rgba(0, 0, 0, 0.04);
}
.open_t {
    font-size: 26px;
    margin-top: 10px;
}
.open_s {
    text-align: center;
    padding: 10px;
    background: #f3f2f8;
    font-size: 22px;
    width: 98%;
    color: #75747a;
    margin: 10px auto;
}
.open_d {
    text-align: center;
    font-size: 22px;
    color: #75747a;
    width: 98%;
    margin: 20px auto;
    line-height: 40px;
}
.at-input {
    padding: 0;
    margin-left: 0;
    font-size: 14px;
    border-bottom: 0;
    margin-bottom: 0;
    position: static;
}
.at-input input {
    font-size: 28px;
}
.pay_img {
    width: 220px;height: 220px;
}
.pay_memo {
    display: block;margin: 10px;font-size: 35px;color: #3e3838;
}
.pay_txt {
    font-size: 25px;color: #666;
}
.order_list {
    font-size: 28px;
    border-top: 1px solid #eeeeee;
    color: #949393;
    margin-top: 20px;
    padding: 20px 0 4px 0;
}
.order_list .title {
    padding-right: 12px;
}
.order_list .memo {
    color: #000;
    padding-left: 5px;
}
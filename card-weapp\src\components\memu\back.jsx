/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-15 17:31:09
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/memu/back.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View, Text, Image, Swiper, SwiperItem } from '@tarojs/components'
import Taro from '@tarojs/taro'
import tools from '@/utils/tools'

const Brand = () => {
    return (
        <View className="topNav" style={{position: 'absolute'}}>
            <View style={{height:  `${tools.reTopH(6)}rpx`}}></View>
            <View className='at-row'>
                <View className='at-col at-col-1' onClick={() => {
                        Taro.navigateBack({
                            delta: 1,
                            fail: () => {
                                Taro.navigateTo({
                                    url: '/pages/index/index'
                                })
                            }
                        })
                }}>
                    <Image src={`${tools.picUrl}/memu/back.png`} mode='widthFix' className="topIcon_b" />
                </View>
                <View className='at-col at-col-9'></View>
            </View>
        </View>
    )
}
export default Brand 
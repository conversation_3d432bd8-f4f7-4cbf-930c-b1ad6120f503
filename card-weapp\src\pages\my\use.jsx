import { Component, useState, useEffect } from 'react'
import Taro, { useReachBottom } from '@tarojs/taro';
import { View, Text } from '@tarojs/components'
import tools from '@/utils/tools'
import moment from 'moment'
import { AtIcon, AtCard } from "taro-ui"
import './index.scss'

function Index (props) { 
  const [init, setInit] = useState([])
  const [initPage, setInitPage] = useState()
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(-1)
  useEffect( async () => {
    Taro.showLoading({
      mask: true,
      title: '读取中'
    })
    const data = await tools.Api.myUseList({
      code : "ALL",
      page : {
          current: page,
          pageSize : 20
      }
    })
    Taro.hideLoading()
    if (data.code === 200) {
      setInit([
        ...init,
        ...data.data.data
      ])
      setInitPage(data.data.page)
      setTotal(data.data.page.total)
    }
  }, [ page ])

  useReachBottom(() => {
    const nextPage = initPage.current + 1
    if (nextPage <= initPage.totalPage) {
      setPage(nextPage)
    } else {
      Taro.showToast({
        title: '暂无更多内容',
        icon: 'none',
        duration: 2000
      })
    }
  })

  return (
    <View className='index'>
      {(total === 0) ? <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "16px"}}>
          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>
          <View style={{marginTop: "10px" , color: "#848181"}}>暂无入园记录</View>
        </View> :
        <View style={{marginTop: '20px'}}>
          {init.map(v => <View style={{marginBottom: '10px'}}> <AtCard
            note= {`${v.real_name} ${v.card_name} No.${v.user_card_code}`}
            extra= {v.check_way_str}
            title= {`${moment(v.add_time_unix * 1000).format('YYYY年MM月DD日 HH:MM:ss')}`}
          >
            <Text style={{fontSize: '30px'}}>{v.brand_name}</Text>
          </AtCard></View>)}   
        </View>
        }
      <View style={{height: '30px'}}></View>  
    </View>
  )
}
export default Index;

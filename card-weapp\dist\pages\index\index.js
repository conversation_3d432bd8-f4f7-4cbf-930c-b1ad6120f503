(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/index/index"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/index/index.jsx":
/*!*******************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/index/index.jsx ***!
  \*******************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _components_brand__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/brand */ "./src/components/brand/index.jsx");
/* harmony import */ var _components_memu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/memu */ "./src/components/memu/index.jsx");
/* harmony import */ var _components_memu_top__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/memu/top */ "./src/components/memu/top.jsx");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./index.scss */ "./src/pages/index/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__);















function Index(props) {
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    isOpened = _useState2[0],
    setIsOpened = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(0),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState3, 2),
    blurVal = _useState4[0],
    setBlurVal = _useState4[1];
  var login = Object(react_redux__WEBPACK_IMPORTED_MODULE_5__[/* useSelector */ "c"])(function (state) {
    return state.login;
  });
  var _useState5 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(null),
    _useState6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState5, 2),
    init = _useState6[0],
    setInit = _useState6[1];
  var _useState7 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(true),
    _useState8 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState7, 2),
    loading = _useState8[0],
    setLoading = _useState8[1];
  var _useState9 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])('LOAD'),
    _useState10 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState9, 2),
    btnStatus = _useState10[0],
    setBtnStatus = _useState10[1];
  var _useState11 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])({
      list: [],
      title: "年票福利",
      code: 0,
      type: 'buy'
    }),
    _useState12 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState11, 2),
    buyList = _useState12[0],
    setBuyList = _useState12[1];
  var loadData = /*#__PURE__*/function () {
    var _ref = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
      var data;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].Api.index({
              line_id: _utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].line_code,
              order: "km",
              point: true,
              page: {
                "current": 1,
                "pageSize": 5
              }
            });
          case 2:
            data = _context.sent;
            return _context.abrupt("return", data);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function loadData() {
      return _ref.apply(this, arguments);
    };
  }();
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee2() {
    var userInfo, point, buyItem, data, cardStatus;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!login) {
            _context2.next = 12;
            break;
          }
          userInfo = _utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].getDataSafe('userInfo');
          point = _utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].getDataSafe('mypoint');
          buyItem = {
            "admin": "buyer",
            "showlist": 1,
            "from": "fe",
            "page": 0,
            "enter_user_id": 0,
            "agent_id": 1,
            "pageSize": 8,
            "currentPage": 1,
            "buyer": userInfo.mem_id,
            "order": "a.ordernum",
            "ordertype": "desc",
            "range": {
              "latitude": point.api.latitude,
              "longitude": point.api.longitude,
              "val": 5000
            }
          }; // const buyData = await tools.Api.brandBuyList(buyItem)
          // if (buyData.code === 0) {
          //   setBuyList({
          //     ...buyList,
          //     list: buyData.list
          //   })
          // }
          _context2.next = 6;
          return loadData();
        case 6:
          data = _context2.sent;
          _context2.next = 9;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].getCardList();
        case 9:
          cardStatus = _context2.sent;
          setBtnStatus(cardStatus.status);
          if (data.code === 200) {
            setInit(data.data);
            setLoading(false);
          }
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [login]);
  Object(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__["useShareAppMessage"])(function () {
    return {
      title: '北京风景名胜年票',
      path: '/pages/index/index',
      imageUrl: 'https://cardall.oss-cn-beijing.aliyuncs.com/card/2021/12/1640067294_4f8NtU3L.jpg'
    };
  });
  Object(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__["usePageScroll"])(function (res) {
    if (res.scrollTop >= 100) {
      setBlurVal(25);
    }
    if (res.scrollTop < 100) {
      setBlurVal(0);
    }
  });
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
    children: init === null ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_10__[/* AtToast */ "t"], {
      isOpened: true,
      text: "\u8BF7\u7A0D\u7B49",
      status: "loading",
      duration: 0
    }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_components_memu_top__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], {
        bg: blurVal
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "top_pic",
        style: {
          filter: "blur(".concat(blurVal, "px)")
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Swiper"], {
          className: "index_swiper",
          indicatorColor: "#999",
          indicatorActiveColor: "#333",
          circular: true,
          indicatorDots: true,
          autoplay: true,
          children: init.imgList.trunList.map(function (val) {
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["SwiperItem"], {
              onClick: function onClick() {
                _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
                  url: "/pages/brand/index?v=".concat(val.link_code)
                });
              },
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
                src: val.image,
                mode: "aspectFill",
                className: "images"
              })
            });
          })
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "index_center",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "nav",
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_10__[/* AtGrid */ "e"], {
            hasBorder: false,
            columnNum: 4,
            data: [{
              image: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].picUrl, "/index/card.png"),
              value: '购买年票',
              page: 'buy/index'
            }, {
              image: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].picUrl, "/index/plan.png"),
              value: '入园记录',
              page: 'my/use'
            }, {
              image: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].picUrl, "/index/book.png"),
              value: '景区预约',
              page: 'sreach/book'
            }, {
              image: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].picUrl, "/index/news.png"),
              value: '最新通知',
              page: 'news/index'
            }],
            onClick: ( /*#__PURE__*/function () {
              var _ref3 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee3(item) {
                return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee3$(_context3) {
                  while (1) switch (_context3.prev = _context3.next) {
                    case 0:
                      if (item.page === 'tel') {
                        _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.makePhoneCall({
                          phoneNumber: _utils_tools__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"].phone
                        });
                      } else if (item.page === 'card') {
                        // await tools.appConfig('buy')
                      } else {
                        _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
                          url: "/pages/".concat(item.page)
                        });
                      }
                    case 1:
                    case "end":
                      return _context3.stop();
                  }
                }, _callee3);
              }));
              return function (_x) {
                return _ref3.apply(this, arguments);
              };
            }())
          })
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "btn",
          style: {
            fontWeight: 'bold'
          },
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
              url: btnStatus === 'BOOK' ? '/pages/qr/index' : '/pages/bind/index'
            });
          },
          children: btnStatus === 'BOOK' ? '我的入园码' : '激活年票'
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Swiper"], {
          className: "swiper",
          indicatorColor: "#999",
          indicatorActiveColor: "#333",
          circular: true,
          interval: 8000,
          autoplay: true,
          children: init.imgList.topicList.map(function (val) {
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["SwiperItem"], {
              children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
                src: val.image,
                mode: "aspectFill",
                className: "images",
                onClick: function onClick() {
                  _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
                    url: "/pages/sreach/topic?v=".concat(val.link_code)
                  });
                }
              })
            });
          })
        })]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "content_index",
        children: init.brandList.map(function (val) {
          val.type = "book";
          return val.list.length > 1 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_components_brand__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"], {
            data: val
          }) : null;
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_13__["jsx"])(_components_memu__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], {
        now: 0
      })]
    })
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Index);

/***/ }),

/***/ "./src/components/brand/index.jsx":
/*!****************************************!*\
  !*** ./src/components/brand/index.jsx ***!
  \****************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _item__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./item */ "./src/components/brand/item.jsx");
/* harmony import */ var _itembuy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./itembuy */ "./src/components/brand/itembuy.jsx");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./index.scss */ "./src/components/brand/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__);
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-07-21 23:34:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/brand/index.jsx
 */









var BrandIndex = function BrandIndex(props) {
  var _props$data = props.data,
    list = _props$data.list,
    title = _props$data.title,
    code = _props$data.code,
    type = _props$data.type;
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
      className: "index_brand_top",
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
        className: "at-row",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
          className: "at-col at-col-6 title",
          children: title
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
          className: "at-col at-col-6 more",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default.a.navigateTo({
              url: type === 'buy' ? '/pages/sreach/listbuy' : "/pages/sreach/type?v=".concat(code)
            });
          },
          children: ["\u5168\u90E8", /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_5__[/* AtIcon */ "f"], {
            value: "chevron-right",
            size: "18",
            color: "#FFF"
          })]
        })]
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["Swiper"], {
      className: "swiper",
      circular: false,
      indicatorDots: false,
      autoplay: false,
      displayMultipleItems: 1.05,
      children: list.map(function (val, index) {
        return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["SwiperItem"], {
          className: "swiper-item-with-margin",
          children: type === 'buy' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_itembuy__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], {
            data: val,
            cname: "images"
          }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__["jsx"])(_item__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
            data: val,
            cname: "images"
          })
        }, index);
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (BrandIndex);

/***/ }),

/***/ "./src/components/memu/top.jsx":
/*!*************************************!*\
  !*** ./src/components/memu/top.jsx ***!
  \*************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-24 21:57:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/memu/top.jsx
 */






var Top = function Top(props) {
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
    className: "topNav",
    style: {
      display: "none",
      backgroundColor: props.bg === 0 ? "#c5071100" : '#c50711'
    },
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
      style: {
        height: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].reTopH(6), "rpx")
      }
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
      className: "at-row",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
        className: "at-col at-col-1 left",
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default.a.navigateTo({
            url: '/pages/sreach/index'
          });
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["Image"], {
          src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].picUrl, "/memu/find.png"),
          mode: "widthFix",
          className: "topIcon"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
        className: "at-col at-col-2",
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default.a.showToast({
            title: '快速入园，敬请期待',
            icon: 'none',
            duration: 2000
          });
        },
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["Image"], {
          src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].picUrl, "/memu/scan.png"),
          mode: "widthFix",
          className: "topIcon",
          style: {
            marginLeft: '15px'
          }
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_1__["View"], {
        className: "at-col at-col-8"
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (Top);

/***/ }),

/***/ "./src/pages/index/index.jsx":
/*!***********************************!*\
  !*** ./src/pages/index/index.jsx ***!
  \***********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./index.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/index/index.jsx");


var config = {"navigationBarTitleText":"","navigationStyle":"custom","enableShareAppMessage":true,"transparentTitle":"always","titlePenetrate":"YES"};

_node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].enableShareAppMessage = true
var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/index/index', {root:{cn:[]}}, config || {}))



/***/ }),

/***/ "./src/pages/index/index.scss":
/*!************************************!*\
  !*** ./src/pages/index/index.scss ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ })

},[["./src/pages/index/index.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=index.js.map
(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/tips/index"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/tips/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/tips/index.js ***!
  \*****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.scss */ "./src/pages/tips/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);
/*
 * @Author: 高超
 * @Date: 2021-12-17 14:44:03
 * @LastEditTime: 2022-07-21 10:47:08
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/tips/index.js
 * love jiajia
 */







function Tips() {
  var _Taro$useRouter = _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.useRouter(),
    type = _Taro$useRouter.params.type;
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
    children: [type === 'pay100' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
      className: "tips_wrap",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtIcon */ "f"], {
        value: "check",
        size: "40",
        color: "#5cc323",
        className: "tips_t"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_m",
        children: "\u652F\u4ED8\u6210\u529F"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_s",
        children: "\u611F\u8C22\u60A8\u7684\u4F7F\u7528\uFF0C\u6211\u4EEC\u5C06\u7AED\u8BDA\u4E3A\u60A8\u670D\u52A1"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "primary",
          className: "tips_btn_bg",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/sreach/listbuy'
            });
          },
          children: "\u518D\u53BB\u770B\u770B"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "secondary",
          className: "tips_btn_line",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/my/index'
            });
          },
          children: "\u6211\u7684\u8BA2\u5355"
        })
      })]
    }) : null, type === 'pay105' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
      className: "tips_wrap",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtIcon */ "f"], {
        value: "alert-circle",
        size: "60",
        color: "#b80e0e"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_m",
        children: "\u652F\u4ED8\u5931\u8D25"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_s",
        children: "\u60A8\u53D6\u6D88\u4E86\u652F\u4ED8\u8BF7\u6C42\uFF5E"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "primary",
          className: "tips_btn_bg",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/sreach/listbuy'
            });
          },
          children: "\u518D\u53BB\u770B\u770B"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "secondary",
          className: "tips_btn_line",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/my/index'
            });
          },
          children: "\u6211\u7684\u8BA2\u5355"
        })
      })]
    }) : null, type === 'pay106' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
      className: "tips_wrap",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtIcon */ "f"], {
        value: "alert-circle",
        size: "60",
        color: "#b80e0e"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_m",
        children: "\u652F\u4ED8\u5931\u8D25"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_s",
        children: "\u8BA2\u5355\u72B6\u6001\u9519\u8BEF\uFF5E\u60A8\u53EF\u4EE5\u62E8\u6253\u5BA2\u670D\u7535\u8BDD\uFF1A4006091798"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "primary",
          className: "tips_btn_bg",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/sreach/listbuy'
            });
          },
          children: "\u518D\u53BB\u770B\u770B"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "secondary",
          className: "tips_btn_line",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/my/index'
            });
          },
          children: "\u6211\u7684\u8BA2\u5355"
        })
      })]
    }) : null, type === '100' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
      className: "tips_wrap",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtIcon */ "f"], {
        value: "check",
        size: "40",
        color: "#5cc323",
        className: "tips_t"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_m",
        children: "\u6FC0\u6D3B\u6210\u529F"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_s",
        children: "\u611F\u8C22\u60A8\u7684\u4F7F\u7528\uFF0C\u6211\u4EEC\u5C06\u7AED\u8BDA\u4E3A\u60A8\u670D\u52A1"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "primary",
          className: "tips_btn_bg",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/index/index'
            });
          },
          children: "\u7ACB\u5373\u9884\u7EA6\u666F\u533A"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "secondary",
          className: "tips_btn_line",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/my/index'
            });
          },
          children: "\u6211\u7684\u5E74\u7968"
        })
      })]
    }) : null, type === '101' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
      className: "tips_wrap",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtIcon */ "f"], {
        value: "alert-circle",
        size: "60",
        color: "#b80e0e"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_m",
        children: "\u6682\u65E0\u53EF\u7528"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_s",
        children: "\u8BF7\u8D2D\u4E70\u666F\u533A\u5E74\u7968\u540E\u6309\u8BF4\u660E\u7ED1\u5B9A\u6FC0\u6D3B\u5361\u7247"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "primary",
          className: "tips_btn_bg",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/bind/index'
            });
          },
          children: "\u7ACB\u5373\u7ED1\u5361"
        })
      })]
    }) : null, type === '102' ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
      className: "tips_wrap",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtIcon */ "f"], {
        value: "alert-circle",
        size: "60",
        color: "#b80e0e"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_m",
        children: "\u7A0B\u5E8F\u5F00\u5C0F\u5DEE"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tips_s",
        children: "\u8BF7\u60A8\u7A0D\u540E\u518D\u8BD5"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_2__["View"], {
        className: "tip_btn",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtButton */ "a"], {
          type: "primary",
          className: "tips_btn_bg",
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default.a.redirectTo({
              url: '/pages/index/index'
            });
          },
          children: "\u8FD4\u56DE\u9996\u9875"
        })
      })]
    }) : null]
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Tips);

/***/ }),

/***/ "./src/pages/tips/index.js":
/*!*********************************!*\
  !*** ./src/pages/tips/index.js ***!
  \*********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./index.js */ "./node_modules/babel-loader/lib/index.js!./src/pages/tips/index.js");


var config = {"navigationBarTitleText":"提示"};


var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_index_js__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/tips/index', {root:{cn:[]}}, config || {}))



/***/ }),

/***/ "./src/pages/tips/index.scss":
/*!***********************************!*\
  !*** ./src/pages/tips/index.scss ***!
  \***********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ })

},[["./src/pages/tips/index.js","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=index.js.map
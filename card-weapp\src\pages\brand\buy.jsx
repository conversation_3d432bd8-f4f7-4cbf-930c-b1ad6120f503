import { useState, useEffect } from 'react'
import Taro, { usePageScroll, useShareAppMessage } from '@tarojs/taro';
import { useSelector } from 'react-redux'
import { View, Text, Image, Swiper, SwiperItem, Video, RichText } from '@tarojs/components'
import { AtIcon , AtFloatLayout, AtNoticebar} from "taro-ui"
import Skeleton from 'taro-skeleton'
import Ticket from '@/components/brand/ticket'
import tools from '@/utils/tools'
import './index.scss'


function Index (props) { 
  const query = tools.getQuery()
  const login = useSelector(state => state.login)
  const [loading, setLoading] = useState(true)
  const [init, setInit] = useState(null)
  const [goodList, setGoodList] = useState([])
  const [card, setCard] = useState({
    num: 0,
    good_id: '--',
    list: [],
    name: '--',
  })

  const brandCode = query.data.v
  useEffect( async () => {
    if (login && Boolean(brandCode)) {
      const data = await tools.Api.brandInfoZh(brandCode)
      const goods = await tools.Api.brandGoodZh(brandCode)
      if (data.code === 200) {
        const cardList = await tools.getCardList({
          list: data.data.brand.templet_list_enable
        })
        if (cardList.enable_card_num > 0) {
          const enableCard = cardList.card_list.filter(v => v.enable === true)
          setCard({
            num : enableCard.length,
            good_id: enableCard[0].good_id,
            list: enableCard
          })
        }
        setInit(data.data)
        setLoading(false)
      }
      if (goods.code === 0) {
        setGoodList(goods.data.ticket)
      }
    }
  }, [login])

  useShareAppMessage(() => {
    return {
      title: init.brand.name,
      path: `/pages/brand/buy?v=${brandCode}`,
      imageUrl: `${init.brand.logo}?x-oss-process=image/resize,m_fill,h_400,w_400`
    }
  })

  return (
    <View>
       {(init === null && Boolean(brandCode) && goodList.length === 0 ) ?  <Skeleton title row={30} loading={loading}></Skeleton> :
    <View>
      <View className="topNav" style={{position: 'absolute'}}>
            <View style={{height:  `${tools.reTopH(6)}rpx`}}></View>
            <View className='at-row'>
                <View className='at-col at-col-1 left' onClick={() => {
                        Taro.navigateBack({
                            delta: 1,
                            fail: () => {
                                Taro.navigateTo({
                                    url: '/pages/index/index'
                                })
                            }
                        })
                }}>
                    <Image src={`${tools.picUrl}/memu/back.png`} mode='widthFix' className="topIcon_b" />
                </View>
                <View className='at-col at-col-9'></View>
            </View>
        </View>
      <View className="brand_top_pic">
        <Swiper
          className='brand_swiper'
          indicatorColor='#999'
          indicatorActiveColor='#fff'
          circular
          indicatorDots
          autoplay={true}
        >
          {init.brand.images.map(v => <SwiperItem>
            <Image src={v.path} mode="heightfix" className="images"/>
          </SwiperItem>)}
        </Swiper>
      </View>
      {/* {
        (init.brand.brand_note !== 'none' && init.brand.brand_note.length > 4) ? <AtNoticebar>{init.brand.brand_note}</AtNoticebar> : null
      } */}
     
      <View className="brand_content">
        <View className='at-row'>
          <View className='at-col at-col-9'>
            <View className="title">{init.brand.name}</View>
            <View className="memo" style={{padding: 0}}>
            {
                init.brand.tagsname.map( (v, index) => {
                    return (index <= 3) ? <View className='tips_three' style={(index === 0) ? {marginLeft: '0'} : {}}>{v.tagname}</View> : null
                } )
            }
            </View>
          </View>
          <View className='at-col at-col-3'>
            <View style={{marginTop: '44px', color: '#777575', fontSize: '14px', textAlign:'right'}}>已售: {init.brand.ordernum + init.brand.ordernum_show}</View>
          </View>
        </View>
        
       
        <View className='at-row list_show' onClick={() => {
          Taro.makePhoneCall({
            phoneNumber: init.brand.tel
          })
        }}>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/time.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text'>
            营业时间 {(init.brandTime === null) ? '全天' : init.brandTime[0]}
          </View>
          <View className='at-col at-col-1'>
            <AtIcon value='phone' size='18' color='#afafaf'></AtIcon>
          </View>
        </View>

        <View className='at-row list_show' onClick={() => {
          const wxMap = tools.bdMap_to_txMap(init.brand.latitude, init.brand.longitude);
          Taro.openLocation({
            latitude: wxMap.lat,
            longitude: wxMap.lng,
            scale: 15
          })
        }}>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/map.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text'>
            {`${init.brand.province}${init.brand.city}${init.brand.address}`}
          </View>
          <View className='at-col at-col-1'>
            <AtIcon value='chevron-right' size='18' color='#afafaf'></AtIcon>
          </View>
        </View>
        
        <View className='at-row list_show'>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/shop.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text' style={{fontWeight: 'bold'}}>
            产品预订
          </View>
          <View className='at-col at-col-1'>
            <AtIcon value='chevron-down' size='18' color='#afafaf'></AtIcon>
          </View>
          <View></View>
        </View>
        {(goodList.length > 0) ?  <Ticket 
                                    allT={goodList} 
                                    vid={0} 
                                    rank={1} 
                                    openmm={() => {}} 
                                    weixinadinfo={false}
                                    coupon = {[]}
                                    card= {card.num}
                                  /> : null}
        
      </View>


      {/* {init.brand.rule_list !== null && init.brand.rule_list.map(v =>  <View>
        <View className="brand_title">
            <View className="t_icon"></View>
            <View className="t_name">{v.val.title}</View>
        </View>
        <View className="brand_html">
          <RichText className='htmlformat' style={{lineHeight: '30px'}} nodes={tools.removeCss(v.val.memo)}/>
        </View>
      </View>)} */}
     

      <View>
        <View className="brand_title">
            <View className="t_icon"></View>
            <View className="t_name">特色介绍</View>
        </View>
        <View className="brand_html">
          <RichText  nodes={tools.removeCss(init.brand.memo)}/>
        </View>
      </View>

    
      <View style={{height: '80px'}}></View>
     
    </View>}
    </View>
  )
}
export default Index;

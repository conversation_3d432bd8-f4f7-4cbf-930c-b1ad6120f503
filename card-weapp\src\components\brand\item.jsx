/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-04-27 12:19:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/brand/item.jsx
 */
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import "./index.scss"

const BrandItem = (props) => {
    const { data } = props
    const css = (props.className) ? props.className : 'noraml'
    return (
        <View className={`brandIndex ${css}`} onClick={() => {
            Taro.navigateTo({
                url : `/pages/brand/index?v=${data.code}`
            })
        }}>
            
            <View className="top_tags" style={{ backgroundColor : `${data.is_open_config.is_open ? '#ED8502' : '#bdbdbd'}`}}>{data.is_open_config.is_open_tips.replace('|', '')}</View>
            <View className="status open">
                <View>
                    <Text>距我</Text>
                    <View className="num">{data.km}</View>
                    <Text>公里</Text>
                </View>
            </View>
           <View style={{position: 'relative'}}>
            <Image src={data.image} className={props.cname} mode="widthFix"/>
            {
                (data.brand_note !== 'none' && data.brand_note.length > 4) ? <View style={{position:'absolute', bottom: '5px', background: '#0000007a', padding: '5px 0', color:'#fff', fontSize: '14px', width: '100%', zIndex: 1, textIndent: '5px'}}>{data.brand_note}</View> : null
            }
            
           </View>
           
           <View className="title">
               {data.brand_name}
            </View>
           <View className="info">{data.tips}{data.address}</View>
           <View>
               <View className='tips_one' style={{backgroundColor: (data.is_book === 0) ? '#FF5722' : '#8BC34A'}}>{data.is_book_str}</View>
               <View className='tips_two'>{data.user_tips}</View>
               {
                   (data.today_num > -1) ? <View className='tips_one' style={{backgroundColor: "#ff5722", width: '70px', padding: '4px 0'}}>{data.today_num_str}</View> : null
               }
               
           </View>
        </View>
    )
}
export default BrandItem 
{"version": 3, "file": "pages/bind/rel.js", "sources": ["webpack:///./src/pages/bind/rel.jsx", "webpack:///./src/pages/bind/rel.jsx?e51f"], "sourcesContent": ["import { Component, useEffect, useState } from 'react'\r\nimport { View, Image } from '@tarojs/components'\r\nimport Taro from '@tarojs/taro'\r\nimport { AtRadio, AtNoticebar } from 'taro-ui'\r\nimport tools from '@/utils/tools'\r\nimport Bg from '@/components/tool/bg'\r\nimport \"./index.scss\"\r\n\r\nfunction Index (props) { \r\n  const params =  tools.getQuery()\r\n  const good_key = Boolean(params.data.key) ? params.data.key : \"\"\r\n  const [who, setWho] = useState(0)\r\n  useEffect( async () => {}, [])\r\n  const handleChange = value => {\r\n    setWho(value)\r\n  }\r\n  return (\r\n    <View>\r\n      <Bg/>\r\n      <AtNoticebar icon='volume-plus'>为方便使用您可为您的家人开通卡片，尤其是老人与孩子呦～祝您游玩愉快！</AtNoticebar>\r\n      <View style={{padding: '30px 20px'}}>\r\n        <AtRadio\r\n          options={tools.relation}\r\n          value={who}\r\n          onClick={handleChange.bind(this)}\r\n        />\r\n      </View>\r\n      <View className=\"jbtn\" onClick={() => {\r\n        Taro.navigateTo({\r\n          url: `/pages/bind/add?rel=${who}&key=${good_key}`,\r\n        });\r\n      }}>下一步</View>\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./rel.jsx\"\nvar config = {\"navigationBarTitleText\":\"为谁开通\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/bind/rel', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
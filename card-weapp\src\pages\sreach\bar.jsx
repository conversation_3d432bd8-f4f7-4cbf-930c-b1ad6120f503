import { useEffect, useState } from 'react'
import { View, Text } from '@tarojs/components'
import { AtSearchBar, AtIcon, AtButton } from 'taro-ui'
import Taro from '@tarojs/taro'
import tools from '@/utils/tools'
import Tags from './tags'
import "./index.scss"

const SreachTools = (props) => {
    const { config } = props
    const query = tools.getQuery()
    const [toolVal, setToolVal] = useState(null)
    const [word, setWord] = useState((props.init) ? props.init : undefined)
    const callBack = () => {
        const result = {
            ...toolVal,
            show: false,
            active: null
        }
        setToolVal(result)
        const setData = () => {
            const data = {}
            Object.keys(result.data).forEach(v => {
                if (result.data[v].length > 0) {
                    data[v] = (result.data[v].length === 1 && v === 'order') ? result.data[v][0] : result.data[v]
                }
            })
            return data
        }
        props.callBack(setData())
    }

    useEffect( () => {
        const init = {
            show : false,
            active : null,
            data : {}
        }
        if (Boolean(config)) {
            config.forEach(val => {
                init.data[val.name] = (Boolean(val?.default)) ? val?.default : []
            })
        }
        setToolVal(init)
    }, [])

    const setData = (name , val, type, data, multiple) => {
        if (Array.isArray(data.data[name])) {
            if (val === null) {
                data.data[name] = []
            } else {
                if (multiple) {
                    if (type && !data.data[name].includes(val)) {
                        data.data[name].push(val)
                    } else {
                        const index = data.data[name].indexOf(val); 
                        if (index > -1) data.data[name].splice(index, 1); 
                    }
                } else {
                    data.data[name] = [val]
                }
               
            }
            setToolVal({
                ...data
            })
        }
    }

    if (toolVal === null) {
        return null
    }

    return (
        <View className={`sreach_tools ${(config.length === 0) ? 'h_only' : 'h_all'}`}>
            <View className="sreach_content">
                {(props.top !== 'hide') ? <AtSearchBar
                    placeholder={'请输入景区名称/地区/特色'}
                    value={word}
                    onChange={(v) => {
                        setWord(v)
                    }}
                    onClear={() => {
                        if (Boolean(props.setkey)) {
                            props.setkey(undefined)
                            setWord(undefined)
                        }
                    }}
                    onActionClick = {() => {
                        let nowKey = tools.getDataSafe('keyWords')
                        if (nowKey === null) {
                            tools.setData('keyWords', [word])
                        } else {
                            if (nowKey.length <= 10) {
                                nowKey.unshift(word)
                                tools.setData('keyWords', [...new Set(nowKey)])
                            }
                        }
                        if (Boolean(props.setkey)) {
                            props.setkey(word)
                        } else {
                            Taro.navigateTo({
                                url : `/pages/sreach/list?v=${word}`
                            })
                        }
                       
                    }}
                /> : null}
                <View className='at-row sreach_sel' style={{display: (config.length === 0 ? 'none' : 'flex') }}>
                {
                    config.map(val => <View 
                        className={`at-col md ${(toolVal.active === val.name || toolVal.data[val.name].length > 0) && val.type !== 'type' ? 'selected' : 'init'}`}
                        onClick ={() => {
                            const { active } = toolVal
                            if (val.name === active) {
                                setToolVal({
                                    ...toolVal,
                                    active: null,
                                    show : false
                                })
                            } else {
                                setToolVal({
                                    ...toolVal,
                                    show: true,
                                    active : val.name
                                })
                            }
                        }}
                    >{`${val.title}${(toolVal.data[val.name].length > 0 && val.type !== 'type' && val.multiple) ? ` [${toolVal.data[val.name].length}]` : ''}`} <AtIcon value={`chevron-${toolVal.active === val.name ? 'down' : 'up'}`} size='15' color={(toolVal.active === val.name || toolVal.data[val.name].length > 0) ? "#6190E8" : '#666'}></AtIcon></View>)
                }
                </View>
                {
                    config.map(val => {
                        switch (val.type) {
                            case 'tags':
                                return (
                                    <View className={`sel_area ${toolVal.active === val.name ? 'show' : 'hide'}`}>
                                        <View className="sel_item" style={{paddingTop: '15px'}}>
                                            {val.list.map(item => {
                                                if (Boolean(item.chlidren)) {
                                                    return <View style={{marginBottom: '20px'}}>
                                                        <View style={{paddingLeft: '15px', color: '#8c8686', fontWeight: 'bold'}}>{item.title}</View>
                                                        {
                                                            item.chlidren.map(cc => {
                                                                return <Tags val={{
                                                                    name : val.name,
                                                                    val: cc.val,
                                                                    multiple: val.multiple
                                                                }} data={toolVal}  onClick={(v) => {
                                                                    setData(val.name, cc.val, v, toolVal, val.multiple)
                                                                    if (val.multiple === false){
                                                                        callBack()
                                                                    }
                                                                 }} >{cc.title}</Tags>
                                                            })
                                                        }
                                                    </View>
                                                } else {
                                                   return <Tags val={{
                                                        name : val.name,
                                                        val: item.val,
                                                        multiple: val.multiple
                                                    }} data={toolVal}  onClick={(v) => {
                                                        setData(val.name, item.val, v, toolVal, val.multiple)
                                                        if (val.multiple === false){
                                                            callBack()
                                                        }
                                                     }} >{item.title}</Tags>
                                                }
                                            })}
                                        </View>
                                        {
                                            (val.multiple) ? <View className='at-row sel_btn'>
                                            <View className='at-col at-col-1'></View>
                                            <View className='at-col at-col-4'><AtButton onClick={() => {
                                                setData(val.name, null, false, toolVal)
                                                callBack()
                                            }}>不限条件</AtButton></View>
                                            <View className='at-col at-col-1'></View>
                                            <View className='at-col at-col-5'><AtButton type='primary' onClick={() =>  callBack()}>确 认</AtButton></View>
                                            <View className='at-col at-col-1'></View>
                                        </View> : null
                                        }
                                        
                                    </View>
                                )
                            case 'list':
                                return (
                                    <View className={`sel_list ${toolVal.active === val.name ? 'show' : 'hide'}`}>
                                        {val.list.map((item, index) =>  <View className={`sel_list_item ${(toolVal.data[val.name].includes(item.val) || (toolVal.data[val.name].length === 0  && index === 0)) ? 'active' : ''}`} onClick={() => {
                                            const data = toolVal
                                            data.data[val.name] = [item.val]
                                            setToolVal({
                                                ...data
                                            })
                                            callBack()
                                        }}>{item.title}</View>)}
                                    </View>    
                                )
                            default:
                                return null
                            
                        }
                    })
                }
            </View>
                {toolVal.show ? <View className='mask' onClick={() => {
                    setToolVal({
                        ...toolVal,
                        show: false,
                        active: ''
                    })
                }}></View> : null}
          </View> 
    )
}
export default SreachTools

          
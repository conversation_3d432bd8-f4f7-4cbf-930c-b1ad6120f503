/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-24 11:16:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/card/list.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { AtIcon } from "taro-ui"
import Skeleton from 'taro-skeleton'
import Card from './index'
import tools from '@/utils/tools'
import "./index.scss"

const CardList = (props) => {
    const max = (Boolean(props.max)) ? props.max : 0
    const [init, setInit] = useState([])
    const [loading, setLoading] = useState(false)
    useEffect( async () => {
        Taro.showLoading({
            mask: true,
            title: '读取中'
          })
        const myCard = await tools.getCardList()
        Taro.hideLoading()
        setInit(myCard.card_list)
        setLoading(false)
    }, [])
    return (
        <View>
            {init.map((val, index) => {
                return (index < max || max === 0) ? <Card data={val}/> : null
            })}
            {<View className="bindBtn" onClick={() => {
                if (Taro.getEnv() === Taro.ENV_TYPE.ALIPAY) { 
                    Taro.navigateTo({
                        url: '/pages/bind/index',
                    });
                } else {
                    tools.getUserInfo(() => {
                        if (props.callback) props.callback()
                        Taro.navigateTo({
                            url: '/pages/bind/index',
                        });
                    })
                }

            }}>
                <AtIcon value='add-circle' size='20' color='#fff'></AtIcon>
                <Text style={{paddingLeft: '5px', paddingTop: '5px'}}>
                    激活年票
                </Text>
            </View>}
        </View>
    )
}
export default CardList 
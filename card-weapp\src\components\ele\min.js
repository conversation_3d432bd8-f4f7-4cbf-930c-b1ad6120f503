/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-09-07 13:41:51
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /dfx-umi/Users/<USER>/dfx-video/src/components/ele/price.js
 */

const MinCoupon = (props) => {
    const {txt, cash, click} = props;
    return (
        <Text style={{display: 'inline-block', marginLeft: '10px' , border: '1px solid #f84151', fontSize: '12px', textAlign: 'center', borderRadius: '2px'}} onClick={() => {
            if (<PERSON><PERSON><PERSON>(click)) {
                click();
            }
        }}>
            <Text style={{display: 'inline-block', background: '#f84151', color: '#fff', padding: ' 0 3px'}}>{txt}</Text><Text style={{display: 'inline-block', fontWeight: 'bold', textAlign: 'center', padding: '0 3px', color: '#f84151'}}>
            <Text style={{fontSize: '10px', padding: '0 1px 0 1px'}}>¥</Text>{cash}</Text>
        </Text>
    )
}
export default MinCoupon;
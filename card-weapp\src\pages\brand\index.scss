page {
    background-color: #EDEDED;
    overflow-x: hidden;
}
.brand_top_pic {
    width: 100%;
}
.brand_swiper{
    width: 100%;
    height: 480px;
    .images{
        min-width: 100%;
        height: 480px;
    }
}
.brand_content {
    width: 90%;
    background-color: #fff;
    border-radius:  0 0 30px 30px;
    position: relative;
    z-index: 5;
    padding: 40px 5% 40px 5%;
    .title {
        color: #333333;
        font-size: 46px;
        font-weight: bold;
    }
    .memo {
        font-size: 28px;
        padding: 15px 0;
        color: #5A5A5C;
    }
    .open {
        margin-left: 20px;
        width: 110px;
        height: 100px;
        background-color: #0367D9;
        color: #fff;
        font-size: 20px;
        top: 45%;
        text-align: center;
        border-radius: 120px;
        padding-top: 10px;
        .num {
            font-size: 26px;
            font-weight: 600;
        }
    }
    .close {
        background-color: #bfc1c3;
    }
    .list_show {
        border-bottom: 1px solid #F0F2F1;
        margin-top: 30px;
        padding: 5px 0;
        .text {
            font-size: 26px;
            color: #333335;
            padding-top: 5px;
        }
        .icon {
            width: 40px;
            height: 40px;
        }
    }
}

.brand_title {
    position: relative;
    margin-top: 40px;
    .t_name {
        font-size: 32px;
        padding-left: 5%;
    }
    .t_icon {
        position: absolute;
        left: -18px;
        top: 8px;
        width: 30px;
        height: 30px;
        border-radius: 30px;
        background-color: #0565DF;
    }
}
.brand_html {
    padding:3% 3% !important;
    font-size: 28px;
}
.htmlformat {
    line-height: 30px;
    img {
        max-width: 100%;
    }
}
.nocard{
    padding: 1px 25px 0 20px;
    color: #5f5b5bcc;
    display: inline-block;
    min-width: 70px;
    line-height: 22px;
}
.hadcard{
    margin-top: -4px;
    display: inline-block;
}
rich-text .richImg{
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
    height: auto!important;
    width: auto!important;
    margin: 10px 0;
  }
.brand_memu {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 150px;
    background-color: #fff;
    .c_icon {
        width: 40px;
        height: 40px;
    }
    .book {
        background-color: #0565DF;
        width: 85%;
        height: 50px;
        text-align: center;
        color: #fff;
        padding: 10px;
        border-radius: 15px;
        margin-top: 18px;
        line-height: 50px;
        border: 0;
        font-weight: 600;
    }
    .book_close {
        background-color: #a2a6aa;
        width: 90%;
        height: 50px;
        text-align: center;
        color: #fff;
        padding: 10px;
        border-radius: 15px;
        margin-top: 18px;
        margin-left: 15%;
        line-height: 50px;
        border: 0;
    }
    .card_status {
        padding-top: 30px;
        font-size: 24px;
        padding-left: 4%;
        .ok {
            display: inline-block;
            border: 1px solid #04965a;
            color: #04965a;
            padding: 4px 10px;
            border-radius: 9px;
            margin-left: 8px;
        }
        .no {
            border: 1px solid #a5a5a5;
            color: #a5a5a5;
        }
    }
}
.custom-calendar .calendar-body .calendar-day .calendar-extra-info {
    text-align: left;
    padding-left: 18px;
    padding-top: 2px;
}
.custom-calendar .calendar-head > view {
    text-align: left;
    text-indent: 30px;
    color: #4c4c52; 
    padding-bottom:20px;
    font-weight: bold;
}

.custom-calendar .calendar-picker {
    padding: 20px 0;
    font-weight: bold;
}
.custom-calendar .calendar-body .calendar-day {
    color: #30333e;
}
.custom-calendar .calendar-body .not-this-month > .calendar-date {
    color:#b5bcc3;
}


.brand_pic_list {
    width: 100%;
    height: 500px;
}
.brand_tips_list{
    width: 100%;
    height: 300px;
}
.greenb .at-badge__num{
    background: #4CAF50
}
.greenk .at-badge__num{
    background: #8BC34A
}
.brand_pic_tips {
    border-radius: 10px;
    width: 100%;
    height: 300px;
}
.brand_pic {
    width: 100%;
    height: 500px;
}
.brand_top {
    background-color: #fff;
    padding: 30px 0 5px 10px;
}
.brand_name {
    padding-left: 10px;
}
.brand_city {
    padding-left: 5px;
    display: block;
    font-size: 25px;
    color: #ac7228;
}
.brand_name_txt {
    font-weight: bold;
    font-size: 40px;
}
.brand_address {
    padding-top: 8px;
    font-size: 25px;
    color: #646465;
}
.brand_map {
    margin-top: 10px;
    width: 160px;
    height: 80px;
    margin-left: 15px;
}
.brand_vlog_list {
  padding: 20px 10px 20px 10px;
}
.brand_line {
    background-color: #f6f7fb;
    height: 10px;
}
.at-tabs__header {
    background-color: #f6f7fb !important;
}
.brand_tab {
    background-color: #f6f7fb !important;
}
.brand_wer {
    display: inline-block;
    font-size: 25px;
    color: #383838;
    padding-left: 20px;
}
.brand_wer_pic {
    width: 35px;
    height: 30px;
    margin-right: 5px;
}
.brand_tt{
    background-color: #fff;
    padding: 20px 20px 0 20px;
}
.brand_tt_per {
    border-bottom: 1px solid #f6f7fb;
    padding: 20px 5px 15px 5px;
}
.brand_tt_tags {
    padding: 10px 0;
    font-size: 25px;
}
.brand_tt_tips {
    color: #6ff9a8
}
.brand_tt_sale {
    color: #666;
    padding-top: 5px;
}
.brand_bar_btn {
    padding: 8px 5px;
    text-align: center;
    background: linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    background: -o-linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    background: -ms-linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    background: -moz-linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    background: -webkit-linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    border-radius: 40px;
    font-size: 30px;
    color: #fff;
    width: 80%;
    margin-top: 4px;
    font-weight: 200;
}
.brand_buy_btn_time {
    padding: 6px;
    width: 50%;
    text-align: center;
    background: linear-gradient(left, #e80000 20%, #e83434 50%, #f51a1a 87%);
    background: -o-linear-gradient(left, #e80000 20%, #e83434 50%, #f51a1a 87%);
    background: -ms-linear-gradient(left, #e80000 20%, #e83434 50%, #f51a1a 87%);
    background: -moz-linear-gradient(left, #e80000 20%, #e83434 50%, #f51a1a 87%);
    background: -webkit-linear-gradient(left, #e80000 20%, #e83434 50%, #f51a1a 87%);
    border-radius: 40px;
    font-size: 30px;
    color: #fff;
    margin-top: 5px;
    float: right;
    font-weight: 300;
}
.brand_buy_btn {
    padding: 6px;
    width: 50%;
    text-align: center;
    background: linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    background: -o-linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    background: -ms-linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    background: -moz-linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    background: -webkit-linear-gradient(left , rgb(255, 137, 2) 20% , rgb(255, 114, 3) 50% , rgb(255, 94, 3) 87%);
    border-radius: 40px;
    font-size: 30px;
    color: #fff;
    margin-top: 5px;
    float: right;
    font-weight: 300;
}
.brand_buy_btn_qi {
    padding: 6px;
    width: 50%;
    text-align: center;
    background: linear-gradient(left , rgb(232, 90, 138) 20%, rgb(253, 27, 104) 50%, rgb(185, 0, 63) 87%);
    background: -o-linear-gradient(left , rgb(232, 90, 138) 20%, rgb(253, 27, 104) 50%, rgb(185, 0, 63) 87%);
    background: -ms-linear-gradient(left , rgb(232, 90, 138) 20%, rgb(253, 27, 104) 50%, rgb(185, 0, 63) 87%);
    background: -moz-linear-gradient(left , rgb(232, 90, 138) 20%, rgb(253, 27, 104) 50%, rgb(185, 0, 63) 87%);
    background: -webkit-linear-gradient(left, rgb(232, 90, 138) 20%, rgb(253, 27, 104) 50%, rgb(185, 0, 63) 87%);
    border-radius: 40px;
    font-size: 30px;
    color: #fff;
    margin-top: 5px;
    float: right;
    font-weight: 300;
}
.brand_tt_shu {
    padding: 0 10px;
}
.brand_show_all {
    background-color: #fff;
    text-align: center;
    color:#6fb2f9;
    padding-bottom: 18px;
}
.brand_buy_showp {
    color: #cccccc;
    font-size: 25px;
    text-decoration:line-through;
    padding-left: 15px;
}
.brand_buy_showqi {
    color: #a28e8e;
    font-size: 25px;
    padding-left: 8px;
}
.brand_phone {
    color: #6fb2f9;
    padding-left: 5px;
}
.brand_buy_price{
    color: #ec7413;
    font-size: 35px;
    padding-left: 35px;
}
.brand_tt_name {
    font-weight: bold;
    width: 90%;
    font-size: 28px;
    color: #383838;
}
.brand_tips {
    position: relative;
    font-size: 26px;
    line-height: 50px;
    padding: 10px 15px;
}
.brand_bar {
    padding: 10px 0;
    position: fixed !important;
    height: 60px;
    background-color: #fff;
    bottom: 0;
    width: 98%;
    z-index: 999;
    left:0;
}
.brand_good_img {
    height: 125px;
    width: 100%;
    border-radius: 5px;
}
.brand_room_date {
    background-color: #fff;
    padding: 10px 15px;
    text-align: center;
}
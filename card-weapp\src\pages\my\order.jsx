import { Component, useState, useEffect } from 'react'
import Taro from '@tarojs/taro';
import { View } from '@tarojs/components'
import tools from '@/utils/tools'
import OrderItem from '@/components/my/order';
import { AtList, AtIcon } from "taro-ui"
import './index.scss'

function Index (props) { 
  const [init, setInit] = useState([])
  useEffect( async () => {
    Taro.showLoading({
      mask: true,
      title: '读取中'
    })
    const myOrders = await tools.Api.myOrders()
    Taro.hideLoading()
    if (myOrders.code === 200) {
      setInit(myOrders.data)
    }
  }, [])

  return (
    <View className='index'>
      {(init.length === 0) ? <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "16px"}}>
          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>
          <View style={{marginTop: "10px" , color: "#848181"}}>暂无订单记录</View>
        </View> :
        <View>
          {init.map(v => {
            return <OrderItem data={v}/>
          })}   
        </View>
        }
      <View style={{height: '30px'}}></View>  
    </View>
  )
}
export default Index;

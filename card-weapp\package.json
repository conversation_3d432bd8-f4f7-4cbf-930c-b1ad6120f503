{"name": "card-weapp", "version": "1.0.0", "private": true, "description": "onecard", "templateInfo": {"name": "taro-ui", "typescript": false, "css": "sass"}, "scripts": {"build:weapp": "cross-env NODE_ENV=production node ./config/setProjectConfig.js && taro build --type weapp", "build-dev:weapp": "cross-env NODE_ENV=development node ./config/setProjectConfig.js && taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "cross-env NODE_ENV=production node ./config/setProjectConfig.js && taro build --type alipay", "build-dev:alipay": "cross-env NODE_ENV=development node ./config/setProjectConfig.js && taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "prod:weapp": "npm run build:weapp -- --watch", "dev:weapp": "npm run build-dev:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "prod:alipay": "npm run build:alipay -- --watch", "dev:alipay": "npm run build-dev:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@tarojs/cli": "3.3.18", "@tarojs/components": "3.3.18", "@tarojs/react": "3.3.18", "@tarojs/redux": "^2.2.10", "@tarojs/runtime": "3.3.18", "@tarojs/taro": "3.3.18", "custom-calendar-taro": "^1.0.12", "dva-core": "^2.0.4", "dva-loading": "^3.0.22", "lodash": "4.17.15", "moment": "^2.29.1", "ms": "^2.1.3", "qrcode": "^1.4.4", "react": "^17.0.0", "react-dom": "^17.0.0", "react-redux": "^7.2.6", "redux": "^4.1.2", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.0", "taro-cropper": "^1.2.4", "taro-skeleton": "^2.0.4", "taro-ui": "^3.0.0-alpha.3"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/mini-runner": "3.3.18", "@tarojs/webpack-runner": "3.3.18", "@types/jest": "^27.0.2", "@types/react": "^17.0.2", "@types/webpack-env": "^1.13.6", "babel-preset-taro": "3.3.18", "cross-env": "^7.0.3", "eslint": "^6.8.0", "eslint-config-taro": "3.3.18", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "stylelint": "9.3.0", "ts-jest": "^27.0.7"}, "resolutions": {"sass": "1.62.0"}}
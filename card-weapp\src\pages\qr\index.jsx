import { useEffect, useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import { AtIcon, AtGrid, AtNoticebar, AtCurtain, AtFloatLayout } from "taro-ui"
import Taro from '@tarojs/taro';
import Skeleton from 'taro-skeleton'
import Qr from '@/components/qr'
import Memu from '@/components/memu'
import CardItem from "@/components/card";
import tools from '@/utils/tools'
import moment from 'moment'
import { useSelector } from 'react-redux'
import ms from 'ms'
import "./index.scss"

function Index (props) { 
  const query =  tools.getQuery()
  const [ good_id, setGood_id] = useState(null)
  const [ card, setCard ] = useState([])
  const [changeCard, setChangeCard] = useState(false)
  const [ loading, setLoading ] = useState(true)
  const [ init, setInit ] = useState(null)
  const [ qrTxt, setQrTxt] = useState(false)
  const [ isOpened, setIsOpened] = useState(false)
  const [ isName, setIsName ] = useState(false)
  let timer = null
  const login = useSelector(state => state.login)
  const qrContent = txt => {
    return {
      txt,
      end: moment(moment().add(10 ,'m')).unix()
    }
  }
  const iconList =  [
    {
      image: `${tools.picUrl}/bind/qr_1.png`,
      value: '入园记录'
    },
    {
      image: `${tools.picUrl}/bind/qr_2.png`,
      value: '立即预约'
    },
    {
      image: `${tools.picUrl}/bind/qr_3.png`,
      value: '实名信息'
    },
    {
      image: `${tools.picUrl}/bind/qr_4.png`,
      value: '联系客服'
    }
  ]
  useEffect( async () => {
    Taro.showLoading({
      mask: true,
      title: '读取中'
    })
    if (!login) {
      setLoading(false)
      return false
    }
    const cardList = await tools.getCardList()
    Taro.hideLoading()
    if (cardList.enable_card_num === 0) {
      Taro.redirectTo({
        url: '/pages/tips/index?type=101',
      })
      return false
    }
    const cardEnableNow = tools.cardCheck(cardList.card_list, 'now')
    const cardEnableAll = tools.cardCheck(cardList.card_list, 'all')
    setCard(cardEnableAll)
    let real_id = query.data.code
    if (Boolean(real_id) === false) {
      real_id = (cardEnableNow.length > 0) ? cardEnableNow[0].good_id : cardEnableAll[0].good_id
    }
    setGood_id(real_id)
    setIsName(true)
  }, [ login ])

  useEffect( async () => {
    if (good_id !== null) {
      Taro.showLoading({
        mask: true,
        title: '读取中'
      })
      const data = await tools.Api.cardInfo({code : good_id})
      if (data.code === 200) {
        setLoading(false)
        setInit(data.data)
        setQrTxt(qrContent(data.data.good_key))
      } else {
        setGood_id(null)
        setLoading(false)
      }
      Taro.hideLoading()
      timer = setInterval(() => {
        setQrTxt(qrContent(data.data.good_key))
      }, ms('2m'))
    }
  } ,[ good_id ])

  const iconClick = index => {
    switch (index) {
      case 0:
        Taro.navigateTo({
          url: '/pages/my/use',
        });
      break
      case 1:
        Taro.navigateTo({
          url: '/pages/sreach/book',
        });
      break
      case 2:
        setIsName(true)
      break
      case 3:
        Taro.makePhoneCall({
          phoneNumber: tools.phone
        })
      break
    }
  }

  return (
    <View className='index'>
      {init ? <View>
          <Skeleton title row={20} loading={loading}>
            {(good_id === null) ? <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "16px"}}>
              <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>
              <View style={{marginTop: "10px" , color: "#848181"}}>无效卡</View>
            </View> :
              <View style={{marginTop: `${tools.reTopH(10)}rpx`, padding: "0 20px"}}>
                <View className='at-row'>
                  <View className='at-col at-col-1'>
                    <Image src={init.user_img} mode="aspectFill" style={{width: "70rpx", height: "70rpx", borderRadius: "70rpx", marginRight: '20rpx'}}/>
                  </View>
                  <View className='at-col at-col-9' className="user_good_id" style={{marginLeft: '10rpx'}} onClick={() => setChangeCard(true)}> No.{good_id} <Text className="cardBtn">换卡</Text></View>
                </View>
                <View className="qr">
                    <View className="title">{init.card_name}</View>
                    <View style={{fontSize: '14px', fontWeight: 'bold', paddingBottom: '10px', color: '#06a506', textAlign: 'center'}}>{init.enableData}</View>
                    <View>
                      <Skeleton title row={11} loading={!Boolean(qrTxt)}>
                        <Qr txt={qrTxt}/>
                      </Skeleton>
                      <View className="at-row reload" onClick={() => {
                        setQrTxt(qrContent(init.good_key))
                      }}>
                        <View className='at-col at-col-6' style={{textAlign: 'right'}}>
                          <Image src={`${tools.picUrl}/bind/reload.png`} mode="widthFix" style={{width: "20px", height: "20px"}}/>
                        </View>
                        <View className='at-col at-col-6 txt'>
                          <Text >刷新</Text>
                        </View>
                      </View>
                      <View className="note"><AtNoticebar icon='volume-plus'>请工作人员核验二维码和实名信息后入园</AtNoticebar></View>
                      <View style={{ borderTop: "1px solid #EDEDED", width: "85%", margin: '10px auto'}}></View>
                      <AtGrid  hasBorder={true} data={iconList} columnNum={4} onClick={(val, index) => {
                        iconClick(index)
                      }}/>
                    </View>
                </View>
               
               
              </View>
            }
          </Skeleton>
          <AtCurtain
          isOpened={isOpened}
          onClose={()=>{
            setIsOpened(false)
          }}
        >
          <Image
            style={{width: "100%", borderRadius: "15rpx"}}
            mode='widthFix'
            src={`${tools.ip}/go.jpg`}
          />
        </AtCurtain>
        <AtFloatLayout isOpened={isName} title="请景区工作人员核验实名信息后入园" onClose={() => {
          setIsName(false)
        }}>
          <View style={{paddding: '25rpx'}}>
            <View className="at-row" style={{width: "90%", margin: "20px auto"}}>
              <View className='at-col at-col-4'>
                <Image src={init.user_img} mode="aspectFit" className="user_img_show"/>
              </View>
              <View className='at-col at-col-9' style={{paddingLeft: "15rpx", fontSize: "30rpx", color: "#b3b1b1"}}>
                  <View style={{marginBottom: "20rpx"}}>姓<View style={{width: "30rpx", display: 'inline-block'}}></View>名 : <Text style={{color: "#673ab7"}}>{init.real_name}</Text></View>
                  <View style={{marginBottom: "20rpx"}}>电<View style={{width: "30rpx", display: 'inline-block'}}></View>话 : <Text style={{color: "#673ab7"}}>{init.user_tel}</Text></View>
                  <View style={{marginBottom: "20rpx"}}>类<View style={{width: "30rpx", display: 'inline-block'}}></View>型 : <Text style={{color: "#673ab7"}}>{init.id_card_type_str}</Text></View>
                  <View>证件号 : <Text style={{color: "#673ab7"}}>{init.id_card_no}</Text></View>
              </View>
            </View>
          </View>
        </AtFloatLayout>
      </View> : null}
      <Memu now={2} />
      <AtFloatLayout isOpened={changeCard} title="请您选择年票" onClose={() => {
          setChangeCard(false)
        }}>
          <View style={{backgroundColor:'#fff', width: '93%', margin: '0 auto 20px auto'}}>
            {card.map(v => <CardItem data={v} oper={(id) => {
              setGood_id(id)
              setChangeCard(false)
            }}/>)}
          </View>
      </AtFloatLayout>
    </View>
  )
}
export default Index;

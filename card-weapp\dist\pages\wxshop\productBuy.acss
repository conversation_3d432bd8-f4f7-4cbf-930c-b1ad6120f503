page {
  overflow: auto !important;
  background-color: #f3f3f3;
}

.good_live_top {
  width: 100%;
}

.good_live_title {
  padding: 20rpx;
  background-color: #ffffff;
}

.good_live_title {
  font-size: 50rpx;
  font-weight: bold;
  color: #ec7413;
}

.buy_icon {
  font-size: 40rpx;
  padding-right: 10rpx;
}

.show_icon {
  font-size: 30rpx;
  padding-left: 20rpx;
  font-weight: normal;
  color: #aaaaaa;
  text-decoration: line-through;
}

.good_title {
  font-weight: normal;
  color: #000;
  font-size: 35rpx;
  margin-bottom: 8rpx;
}

.good_memo {
  font-size: 30rpx;
  color: #aaaaaa;
  font-weight: normal;
}

.good_live_brand {
  padding: 20rpx;
  border-top: 1rpx solid #e6e6e6;
  background-color: #ffffff;
  font-size: 30rpx;
  color: #000000;
}

.brand_map {
  margin-top: 10rpx;
  width: 160rpx;
  height: 80rpx;
  margin-left: 15rpx;
}

.good_live_tips {
  margin-top: 15rpx;
  padding: 15rpx 30rpx;
  line-height: 55rpx;
  color: #000;
  font-size: 30rpx;
  background-color: #ffffff;
}

.good_live_buybtn {
  background-color: #ffffff;
  padding: 10rpx;
  position: fixed;
  z-index: 100;
  bottom: 0;
  width: 100%;
}

.go_btn {
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  color: #ffffff;
  text-align: center;
  padding: 14rpx 0;
  margin-top: 10rpx;
}

.go_buy {
  border: 0;
  background-color: #ec7413;
  color: #ffffff;
}

.go_out {
  border: 0;
  background-color: #aaaaaa;
}

.video_hideBtn {
  position: absolute;
  top: 0;
  width: 100%;
  height: 130rpx;
  left: 0;
  border: 0;
  opacity: 0;
}

.btn_share {
  position: relative;
  margin-top: 25rpx;
}

.topicView {
  width: 100%;
}

.topicTop {
  width: 100%;
}

.timeTab {
  height: 70rpx;
  border-bottom: 1rpx solid #E3E3E3;
  background: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 100;
}

.timeTab .tabp {
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  width: 49%;
  font-size: 30rpx;
  color: #4E4D4D;
  font-weight: bold;
  height: 70rpx;
}

.timeTab .tabs {
  padding: 0 0 18rpx 0;
}

.timeTab .cur {
  border-bottom: 6rpx solid #FD5702;
}

.timeTab .shax {
  -webkit-box-shadow: 8rpx 0 8rpx 0 rgba(0, 0, 0, 0.04);
          box-shadow: 8rpx 0 8rpx 0 rgba(0, 0, 0, 0.04);
}

.open_t {
  font-size: 26rpx;
  margin-top: 10rpx;
}

.open_s {
  text-align: center;
  padding: 10rpx;
  background: #f3f2f8;
  font-size: 22rpx;
  width: 98%;
  color: #75747a;
  margin: 10rpx auto;
}

.open_d {
  text-align: center;
  font-size: 22rpx;
  color: #75747a;
  width: 98%;
  margin: 20rpx auto;
  line-height: 40rpx;
}

.at-input {
  padding: 0;
  margin-left: 0;
  font-size: 14rpx;
  border-bottom: 0;
  margin-bottom: 0;
  position: static;
}

.at-input input {
  font-size: 28rpx;
}

.pay_img {
  width: 220rpx;
  height: 220rpx;
}

.pay_memo {
  display: block;
  margin: 10rpx;
  font-size: 35rpx;
  color: #3e3838;
}

.pay_txt {
  font-size: 25rpx;
  color: #666;
}

.order_list {
  font-size: 28rpx;
  border-top: 1rpx solid #eeeeee;
  color: #949393;
  margin-top: 20rpx;
  padding: 20rpx 0 4rpx 0;
}

.order_list .title {
  padding-right: 12rpx;
}

.order_list .memo {
  color: #000;
  padding-left: 5rpx;
}

{"version": 3, "file": "pages/my/love.js", "sources": ["webpack:///./src/pages/my/love.jsx", "webpack:///./src/pages/my/love.jsx?6199"], "sourcesContent": ["import { Component, useState, useEffect } from 'react'\r\nimport Taro, { useReachBottom } from '@tarojs/taro';\r\nimport { View } from '@tarojs/components'\r\nimport tools from '@/utils/tools'\r\nimport Item from '@/components/brand/item'\r\nimport { AtIcon } from \"taro-ui\"\r\nimport './index.scss'\r\n\r\nfunction Index (props) { \r\n  const [init, setInit] = useState([])\r\n  const [initPage, setInitPage] = useState()\r\n  const [page, setPage] = useState(1)\r\n  const [total, setTotal] = useState(-1)\r\n  useEffect( async () => {\r\n    Taro.showLoading({\r\n      mask: true,\r\n      title: '读取中'\r\n    })\r\n    const data = await tools.Api.myCollect({\r\n      point: true,\r\n      page : {\r\n          current: page,\r\n          pageSize : 20\r\n      }\r\n    })\r\n    Taro.hideLoading()\r\n    if (data.code === 200) {\r\n      setInit([\r\n        ...init,\r\n        ...data.data.data\r\n      ])\r\n      setInitPage(data.data.page)\r\n      setTotal(data.data.page.total)\r\n    }\r\n  }, [ page ])\r\n\r\n  useReachBottom(() => {\r\n    const nextPage = initPage.current + 1\r\n    if (nextPage <= initPage.totalPage) {\r\n      setPage(nextPage)\r\n    } else {\r\n      Taro.showToast({\r\n        title: '暂无更多内容',\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n    }\r\n  })\r\n\r\n  return (\r\n    <View className='index'>\r\n      {(total === 0) ? <View style={{textAlign: 'center', marginTop: \"60px\", color: \"#333\", fontSize: \"16px\"}}>\r\n          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>\r\n          <View style={{marginTop: \"10px\" , color: \"#848181\"}}>暂无收藏记录</View>\r\n        </View> :\r\n        <View style={{marginTop: '20px'}}>\r\n          {init.map(v => <Item data={v} className=\"list\" data={v} cname=\"images_l\"/>)}   \r\n        </View>\r\n        }\r\n      <View style={{height: '30px'}}></View>  \r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./love.jsx\"\nvar config = {\"navigationBarTitleText\":\"我的收藏\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/my/love', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AANA;AAOA;AACA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
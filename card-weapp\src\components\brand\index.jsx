/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-07-21 23:34:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/brand/index.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View, Text, Image, Swiper, SwiperItem } from '@tarojs/components'
import Taro, { getEnv, ENV_TYPE } from '@tarojs/taro'
import BrandItem from './item'
import BrandBuyItem from './itembuy'
import { AtIcon } from "taro-ui"
import "./index.scss"

const BrandIndex = (props) => {
    const { data : { list, title, code, type} } = props

    // 根据平台设置不同的 displayMultipleItems 值
    const getDisplayMultipleItems = () => {
        const env = getEnv()
        if (env === ENV_TYPE.ALIPAY) {
            // 支付宝小程序使用整数值避免重叠问题
            return 1
        }
        // 微信小程序等其他平台使用 1.1 显示预览效果
        return 1.1
    }

    return (
        <View>
            <View className='index_brand_top'>
                <View className='at-row'>
                    <View className='at-col at-col-6 title'>{title}</View>
                    <View className='at-col at-col-6 more' onClick={() => {
                        Taro.navigateTo({
                            url: (type === 'buy') ? '/pages/sreach/listbuy' : `/pages/sreach/type?v=${code}`
                        })
                    }}>全部<AtIcon value='chevron-right' size='18' color='#FFF'></AtIcon></View>
                </View>
            </View>
            <Swiper
              className='swiper'
              circular={false}
              indicatorDots={getEnv() === ENV_TYPE.ALIPAY && list.length > 1}
              autoplay={false}
              displayMultipleItems={getDisplayMultipleItems()}
            >
              {list.map((val, index) =>
                <SwiperItem
                  key={index}
                  className={getEnv() === ENV_TYPE.ALIPAY ? '' : 'swiper-item-with-margin'}
                >
                  {type === 'buy' ? <BrandBuyItem data={val} cname='images' /> : <BrandItem data={val} cname='images' />}
                </SwiperItem>
              )}
            </Swiper>

        </View>
    )
}
export default BrandIndex 
import { View, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import Bg from '@/components/tool/bg'
import tools from "@/utils/tools"
import "./index.scss"

function Index (props) { 
  return (
    <View className='index'>
        <Bg/>
        <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "18px"}}>
          <View><Image src={`${tools.picUrl}/bind/ok.png`} mode= "widthFix" style={{width : '20%'}}/></View>
          <View style={{marginTop: "10px" , color: "#848181"}}>激活绑定成功</View>
          <View className="okbtn" onClick={() => {
            Taro.redirectTo({
              url: '/pages/index/index',
            });
          }}>开始预约</View>
        </View>
    </View>
  )
}
export default Index;

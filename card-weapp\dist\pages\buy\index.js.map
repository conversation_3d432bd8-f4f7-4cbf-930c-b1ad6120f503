{"version": 3, "file": "pages/buy/index.js", "sources": ["webpack:///./src/pages/buy/index.jsx", "webpack:///./src/pages/buy/index.jsx?92f1", "webpack:///./src/static/card-online-2024.jpg"], "sourcesContent": ["/*\r\n * @Author: 高超\r\n * @Date: 2021-11-04 17:05:46\r\n * @LastEditTime: 2022-02-16 17:37:22\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/bind/index.jsx\r\n * love jiajia\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport Taro, { useShareAppMessage } from '@tarojs/taro'\r\nimport { View, Text, Button, Image } from '@tarojs/components'\r\nimport { AtIcon, AtButton } from 'taro-ui'\r\nimport Bg from '@/components/tool/bg'\r\nimport tools from \"@/utils/tools\"\r\nimport \"./index.scss\"\r\nimport cardJpg from '@/static/card-online-2024.jpg'\r\n\r\nfunction Index (props) {\r\n  const [check, setCheck] = useState(true)\r\n  const [tel, setTel] = useState(false)\r\n\r\n  useEffect( async () => {\r\n    const userInfo = tools.getDataSafe(\"userInfo\")\r\n    if (userInfo !== null) {\r\n      setTel(Boolean(userInfo.user_tel))\r\n    } else {\r\n      setTel(false)\r\n    }\r\n  }, [tools.getDataSafe(\"userInfo\")])\r\n\r\n  useShareAppMessage(() => {\r\n    return {\r\n      title: \"购买北京风景名胜区年票\",\r\n      path: `/pages/buy/index`,\r\n      imageUrl: `?x-oss-process=image/resize,m_fill,h_400,w_400`\r\n    }\r\n  })\r\n\r\n  return (\r\n    <View className='pageAdd'>\r\n      <Bg/>\r\n      \r\n       <View style={{textAlign:\"center\"}}>\r\n         <Image src={'https://cardall.oss-cn-beijing.aliyuncs.com/card_online.jpg'} mode=\"widthFix\" style={{marginTop : \"30px\", borderRadius: '12px'}}/>\r\n         <View className=\"title\">北京风景名胜年票</View>\r\n         <View className=\"info\">购买并开通年票，全年便捷入园</View>\r\n         <Image src={`${tools.picUrl}/bind/link.jpg`} mode=\"widthFix\" style={{marginTop : \"30px\"}}/>\r\n       </View>\r\n\r\n       <View style={{marginLeft: \"15px\", marginTop: \"15px\"}}>\r\n        <View className='at-row at-row--wrap'>\r\n        <View className='at-col at-col-1'>\r\n        </View>\r\n        <View className='at-col at-col-10 at-col--wrap'>\r\n          <View className=\"txt\">\r\n            购买即表示同意<Text style={{color: \"#03A9F4\"}} onClick={() => {\r\n              Taro.navigateTo({\r\n                url: '/pages/buy/txt',\r\n              });\r\n            }}>《用户授权协议》</Text>，并授权<Text style={{fontWeight:\"bold\"}}>北京风景名胜协会</Text>使用您的姓名、证件号、手机号进行实名开卡，以便为您提供更好的电子卡服务\r\n          </View>\r\n        </View>\r\n        </View>\r\n      </View>\r\n     \r\n      <View style={{height: '80px'}}></View>\r\n      <View className=\"at-row brand_memu\">\r\n        <View className='at-col at-col-6 card_status'>\r\n          <View className='at-row'>\r\n            <Text className='nocard' style={{fontSize: '18px', fontWeight: 'bold', color: '#FF5722', padding: '0 0 0 15px'}}>¥100</Text>\r\n            <Text className='nocard' style={{padding: '0', margin: '6px 0 0 7px'}}></Text>\r\n          </View>\r\n        </View> \r\n        <View className='at-col at-col-6'>\r\n          <View className=\"book buybtn\" onClick={async () => {\r\n            if (Taro.getEnv() === Taro.ENV_TYPE.ALIPAY) { \r\n              my.navigateTo({\r\n                url: `plugin://myPlugin/annual-card-detail?itemId=2024020822000912070045`,\r\n              });\r\n            } else {\r\n              Taro.showLoading({title: '请求中'});\r\n              const data = await tools.Api.buy()\r\n              Taro.hideLoading()\r\n              if (data.code === 200) {\r\n                wx.requestPayment({\r\n                  'timeStamp': '' + data.data.timestamp_unix,\r\n                  'nonceStr': data.data.nonceStr,\r\n                  'package': data.data.package,\r\n                  'signType': data.data.signType,\r\n                  'paySign': data.data.paySign, // 支付签名\r\n                  success: function(res) {\r\n                    Taro.navigateTo({\r\n                      url: '/pages/buy/ok'\r\n                    });\r\n                  }\r\n                })\r\n              } else {\r\n                Taro.showToast({\r\n                  title: data.message,\r\n                  icon: 'none',\r\n                  duration: 2000\r\n                })\r\n              }\r\n            }\r\n          }}>立即购买</View>\r\n        </View> \r\n      </View>\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.jsx\"\nvar config = {\"navigationBarTitleText\":\"\",\"navigationBarBackgroundColor\":\"#fff\",\"enableShareAppMessage\":true};\n\ncomponent.enableShareAppMessage = true\nvar inst = Page(createPageConfig(component, 'pages/buy/index', {root:{cn:[]}}, config || {}))\n\n", "module.exports = __webpack_public_path__ + \"static/card-online-2024.jpg\";"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAGA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;AC7GA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA;;;;A", "sourceRoot": ""}
{"version": 3, "file": "pages/my/use.js", "sources": ["webpack:///./src/pages/my/use.jsx", "webpack:///./src/pages/my/use.jsx?a195"], "sourcesContent": ["import { Component, useState, useEffect } from 'react'\r\nimport Taro, { useReachBottom } from '@tarojs/taro';\r\nimport { View, Text } from '@tarojs/components'\r\nimport tools from '@/utils/tools'\r\nimport moment from 'moment'\r\nimport { AtIcon, AtCard } from \"taro-ui\"\r\nimport './index.scss'\r\n\r\nfunction Index (props) { \r\n  const [init, setInit] = useState([])\r\n  const [initPage, setInitPage] = useState()\r\n  const [page, setPage] = useState(1)\r\n  const [total, setTotal] = useState(-1)\r\n  useEffect( async () => {\r\n    Taro.showLoading({\r\n      mask: true,\r\n      title: '读取中'\r\n    })\r\n    const data = await tools.Api.myUseList({\r\n      code : \"ALL\",\r\n      page : {\r\n          current: page,\r\n          pageSize : 20\r\n      }\r\n    })\r\n    Taro.hideLoading()\r\n    if (data.code === 200) {\r\n      setInit([\r\n        ...init,\r\n        ...data.data.data\r\n      ])\r\n      setInitPage(data.data.page)\r\n      setTotal(data.data.page.total)\r\n    }\r\n  }, [ page ])\r\n\r\n  useReachBottom(() => {\r\n    const nextPage = initPage.current + 1\r\n    if (nextPage <= initPage.totalPage) {\r\n      setPage(nextPage)\r\n    } else {\r\n      Taro.showToast({\r\n        title: '暂无更多内容',\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n    }\r\n  })\r\n\r\n  return (\r\n    <View className='index'>\r\n      {(total === 0) ? <View style={{textAlign: 'center', marginTop: \"60px\", color: \"#333\", fontSize: \"16px\"}}>\r\n          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>\r\n          <View style={{marginTop: \"10px\" , color: \"#848181\"}}>暂无入园记录</View>\r\n        </View> :\r\n        <View style={{marginTop: '20px'}}>\r\n          {init.map(v => <View style={{marginBottom: '10px'}}> <AtCard\r\n            note= {`${v.real_name} ${v.card_name} No.${v.user_card_code}`}\r\n            extra= {v.check_way_str}\r\n            title= {`${moment(v.add_time_unix * 1000).format('YYYY年MM月DD日 HH:MM:ss')}`}\r\n          >\r\n            <Text style={{fontSize: '30px'}}>{v.brand_name}</Text>\r\n          </AtCard></View>)}   \r\n        </View>\r\n        }\r\n      <View style={{height: '30px'}}></View>  \r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./use.jsx\"\nvar config = {\"navigationBarTitleText\":\"入园记录\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/my/use', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AANA;AAOA;AACA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
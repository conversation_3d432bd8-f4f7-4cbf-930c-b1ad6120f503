/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-12 19:19:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/map/index.jsx
 */
import { Component, useEffect, useState } from 'react'
import Taro from '@tarojs/taro';
import { useSelector } from 'react-redux'
import { View, CoverView, Map} from '@tarojs/components'
import Memu from '@/components/memu'
import tools from '@/utils/tools'
import "./index.scss"

const MapView = () => {
    const login = useSelector(state => state.login)
    const [mapData, setMapData] = useState([])
    useEffect( async () => {
      if (login) {
        const data = await tools.Api.brandMap()
        setMapData(data.data || [])
      }
    }, [login])
    // const normalCallout = {
    //     id: 1,
    //     latitude: 23.098994,
    //     longitude: 113.32252,
    //     iconPath:`${tools.picUrl}/memu/m2.png`,
    //     height: 40,
    //     width: 40,
    //     callout: {
    //         content: '北京故宫',
    //         color: '#ff0000',
    //         fontSize: 14,
    //         borderWidth: 2,
    //         borderRadius: 10,
    //         borderColor: 'blue',
    //         bgColor: '#fff',
    //         padding: 5,
    //         display: 'ALWAYS',
    //         textAlign: 'center',
    //       }
    //   }
      
    //   const customCallout1 = {
    //     id: 2,
    //     latitude: 23.097994,
    //     longitude: 113.32352,
    //     iconPath:`${tools.picUrl}/memu/m2.png`,
    //     height: 40,
    //     width: 40,
    //     customCallout: {
    //       anchorY: 0,
    //       anchorX: 0,
    //       display: 'ALWAYS',
    //     },
    //   }
      
    //   const customCallout2 = {
    //     id: 3,
    //     latitude: 23.096994,
    //     longitude: 113.32452,
    //     iconPath:`${tools.picUrl}/memu/m2.png`,
    //     height: 40,
    //     width: 40,
    //     customCallout: {
    //       anchorY: 0,
    //       anchorX: 0,
    //       display: 'ALWAYS',
    //     },
    //   }
      
    //   const customCallout3 = {
    //     id: 4,
    //     latitude: 23.095994,
    //     longitude: 113.32552,
    //     iconPath:`${tools.picUrl}/memu/m2.png`,
    //     height: 40,
    //     width: 40,
    //     customCallout: {
    //       anchorY: 0,
    //       anchorX: 0,
    //       display: 'ALWAYS',
    //     },
    //   }
      
    //   const customMarkers = [
    //     customCallout1,
    //     customCallout2,
    //     customCallout3,
    //   ]
      
    //   const mapMarkers = [
    //     normalCallout,
    //     ...customMarkers
    //   ]

      const goBrand = id => {
        let code = null
        mapData.forEach(val => {
          if (val.id === id) {
            code = val.code
          }
        })
        if(code !== null) {
          Taro.navigateTo({
            url : `/pages/brand/index?v=${code}`
          })
        }
      }

      return (
        <Map
          setting={{}}
          scale={8}
          show-compass
          enable-overlooking
          markers={mapData}
          latitude={39.925218}
          longitude={116.404425}
          style={{ height: '100vh', width: '100vw' }}
          onCalloutTap={(v) => {
            console.warn("v -------- v", v)
            goBrand(v.markerId)
          }}
          onMarkerTap={(v) => {
            console.warn("v -------- v", v.markerId)
            goBrand(v.markerId)
          }}
        >
          <CoverView slot='callout'>
            {
              mapData.map(item => (
                /** 自定义样式的 callout */
                <CoverView 
                    marker-id={item.code} 
                    key={item.code}
                    height={10}
                    iconPath={`${tools.picUrl}/memu/love.png`}
                >
                </CoverView>
              ))
            }
          </CoverView>
          <Memu now={1}/>
        </Map>
      )
}
export default MapView 
/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-27 17:13:21
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/news/index.jsx
 */
import {  useState, useEffect } from 'react'
import { View, Text } from '@tarojs/components'
import { AtList, AtListItem, AtIcon } from "taro-ui"
import Taro from '@tarojs/taro'
import tools from '@/utils/tools'
import "./index.scss"

const SreachIndex = () => {
    const [list, setList] = useState([])
    useEffect( async () => {
      Taro.showLoading({
        mask: true,
        title: '读取中'
      })
      const data = await tools.Api.noteList()
      if (data.code === 200) {
        setList((data.data.length === 0) ? null : data.data)
      }
      Taro.hideLoading()
    }, [])
    
    return (
        <View>
          {
            (list === null) ? <View className='index'>
            <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "16px"}}>
              <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>
              <View style={{marginTop: "10px" , color: "#848181"}}>暂无通知</View>
            </View>
        </View> : <AtList>
            {
              (list !== null) && list.map(v =>  <AtListItem title={v.title} note= {v.add_time} arrow='right' onClick={() => Taro.navigateTo({ url : `/pages/news/info?v=${v.code}` })}/>)
            }
            </AtList>
          }
          
        </View>
    )
}
export default SreachIndex 
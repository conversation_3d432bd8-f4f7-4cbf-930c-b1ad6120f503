(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/bind/add"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/bind/add.jsx":
/*!****************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/bind/add.jsx ***!
  \****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _components_back_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/back/modal */ "./src/components/back/modal.jsx");
/* harmony import */ var _components_tool_bg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/tool/bg */ "./src/components/tool/bg.jsx");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./index.scss */ "./src/pages/bind/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__);













function Index(props) {
  var _this = this;
  var params = _utils_tools__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].getQuery();
  var who = Boolean(params.data.rel) ? parseInt(params.data.rel, 10) : 0;
  var good_key = Boolean(params.data.key) ? params.data.key : null;
  console.warn("good_key -------------", good_key, who);
  var prve = who === 0 ? '' : _utils_tools__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].relation[who].label;
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(null),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    myphoto = _useState2[0],
    setMyphoto = _useState2[1];
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState3, 2),
    isCheck = _useState4[0],
    setIsCheck = _useState4[1];
  var _useState5 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState6 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState5, 2),
    isClose = _useState6[0],
    setIsClose = _useState6[1];
  var _useState7 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState8 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState7, 2),
    isOk = _useState8[0],
    setIsOk = _useState8[1];
  var _useState9 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(0),
    _useState10 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState9, 2),
    cardType = _useState10[0],
    setCardType = _useState10[1];
  var _useState11 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(''),
    _useState12 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState11, 2),
    cardVal = _useState12[0],
    setCardVal = _useState12[1];
  var _useState13 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState14 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState13, 2),
    cut = _useState14[0],
    setCut = _useState14[1];
  var _useState15 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])({
      user_img: "",
      user_img_check: "请上传持卡人照片",
      real_name: null,
      real_name_check: "请填写姓名",
      id_card_no: "fake",
      id_card_no_check: "请填写证件号码",
      good_key: good_key,
      good_key_check: "请填写卡片激活码"
    }),
    _useState16 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState15, 2),
    info = _useState16[0],
    setInfo = _useState16[1];
  var imageCropper = Object(react__WEBPACK_IMPORTED_MODULE_3__["useRef"])(null);
  var id_card_list_str = ['身份证', '护照', '港澳居民来往内地通行证', '台湾省居民来往大陆通行证', '军官证'];
  var id_card_list = [{
    len: 10,
    title: '身份证',
    val: 0,
    type: 'card'
  }, {
    len: 5,
    title: '护照',
    val: 1,
    type: 'hz'
  }, {
    len: 5,
    title: '港澳居民来往内地通行证',
    val: 2,
    type: 'gh'
  }, {
    len: 5,
    val: 3,
    title: '台湾省居民来往大陆通行证',
    type: 'tai'
  }, {
    len: 5,
    val: 4,
    title: '军官证',
    type: 'jun'
  }];
  var uploadOk = function uploadOk(data) {
    setMyphoto(data[0]);
    info.user_img = data[0];
    setInfo(info);
  };
  var checkAll = function checkAll(val, len, title, name) {
    var type = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : "null";
    if (val.detail.value.length > 0) {
      var check = _utils_tools__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].checkData(val.detail.value, len, title, type);
      if (check.check !== true) {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showToast({
          title: check.check,
          icon: 'none',
          mask: true,
          duration: 2000
        });
        info["".concat(name, "_check")] = check.check;
        info[name] = null;
      } else {
        info[name] = check.data;
      }
      setInfo(info);
    }
  };
  var reData = function reData() {
    var tips = [];
    Object.keys(info).forEach(function (val) {
      if (!val.includes("_check")) {
        if (info[val] === null) {
          tips.push(info["".concat(val, "_check")]);
        }
      }
    });
    if (tips.length > 0) {
      setIsCheck(true);
    } else {
      setIsOk(true);
    }
  };
  var bindCard = /*#__PURE__*/function () {
    var _ref = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
      var userYear, data, oper;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setIsOk(false);
            if (!isClose) {
              _context.next = 3;
              break;
            }
            return _context.abrupt("return", false);
          case 3:
            setIsClose(true);
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showLoading({
              mask: true,
              title: '提交中'
            });
            userYear = cardType === 0 ? _utils_tools__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].checkData(info.id_card_no, 17, "身份证", "card") : {
              year: 1984,
              sex: 1
            };
            data = {
              good_key: info.good_key.toUpperCase(),
              info: {
                id_card_no: info.id_card_no,
                id_card_type: cardType,
                real_name: info.real_name,
                user_tel: info.user_tel,
                user_img: info.user_img,
                user_year: userYear.year,
                user_gender: userYear.sex,
                user_rel: who
              }
            };
            _context.next = 9;
            return _utils_tools__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].Api.bindCard(data);
          case 9:
            oper = _context.sent;
            if (oper.code === 200) {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.redirectTo({
                url: "/pages/tips/index?type=100"
              });
            } else {
              setIsClose(false);
            }
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function bindCard() {
      return _ref.apply(this, arguments);
    };
  }();
  var isAuthorize = /*#__PURE__*/function () {
    var _ref2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee2(authorize) {
      var authSetting;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.getSetting();
          case 2:
            authSetting = _context2.sent;
            if (authSetting[authorize]) {
              _context2.next = 5;
              break;
            }
            return _context2.abrupt("return", new Promise(function (resolve, reject) {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.authorize({
                scope: "scope.writePhotosAlbum",
                success: function success() {
                  resolve("yes");
                },
                fail: function fail() {
                  reject();
                }
              });
            }));
          case 5:
            return _context2.abrupt("return", Promise.resolve("yes"));
          case 6:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function isAuthorize(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleOk = /*#__PURE__*/function () {
    var _ref3 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee3() {
      var isAuthSetting, result;
      return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.next = 2;
            return isAuthorize("scope.writePhotosAlbum");
          case 2:
            isAuthSetting = _context3.sent;
            if (!(isAuthSetting === "yes")) {
              _context3.next = 10;
              break;
            }
            _context3.next = 6;
            return _this.imageCropper.current._getImg();
          case 6:
            result = _context3.sent;
            console.log(result, "result");
            // this.setState({
            // 	previewUrl: result.tempFilePath,
            // });
            // Taro.saveImageToPhotosAlbum({
            // 	filePath: result.tempFilePath,
            // 	success: function(res) {
            // 		Taro.showToast({
            // 			title: "保存图片成功",
            // 		});
            // 	},
            // 	fail() {
            // 		Taro.showToast({
            // 			title: "保存图片失败",
            // 			icon: "fail",
            // 		});
            // 	},
            // });
            _context3.next = 11;
            break;
          case 10:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showToast({
              title: "请在右上角授权"
            });
          case 11:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleOk() {
      return _ref3.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_components_tool_bg__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"], {}), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      className: "addPage",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        class: "item",
        style: {
          marginTop: "18px"
        },
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
          style: {
            width: "80px"
          },
          children: [prve, "\u59D3\u540D"]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Input"], {
          type: "text",
          placeholder: "\u8BF7\u8F93\u5165\u59D3\u540D",
          className: "input",
          maxlength: 10,
          onBlur: function onBlur(val) {
            checkAll(val, 2, "姓名", "real_name");
          }
        })]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        class: "item",
        style: {
          marginTop: "18px"
        },
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
          style: {
            width: "80px"
          },
          children: "\u5361\u6FC0\u6D3B\u7801"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Input"], {
          type: "text",
          placeholder: "\u8BF7\u8F93\u5165\u5361\u6FC0\u6D3B\u7801",
          className: "input",
          maxlength: 15,
          value: good_key,
          onBlur: function onBlur(val) {
            checkAll(val, 8, "卡激活码", "good_key");
          }
        })]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        className: "card",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Image"], {
          src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].ip, "/card.jpg"),
          mode: "widthFix",
          style: {
            width: "70%",
            marginBottom: "12px"
          }
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          style: {
            color: "#3b883e",
            fontSize: '25rpx'
          },
          children: "\u5B9E\u4F53\u5361\u53F3\u4E0B\u89D2\u4E3A\u6FC0\u6D3B\u7801\uFF0C\u7535\u5B50\u7968\u8BF7\u5728\u5C0F\u7A0B\u5E8F\u6211\u7684\u8BA2\u5355\u4E2D\u67E5\u770B"
        })]
      })]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      className: "jbtn",
      onClick: function onClick() {
        reData();
      },
      children: "\u63D0 \u4EA4"
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_components_back_modal__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], {
      open: isCheck,
      title: "\u63D0\u793A",
      ok: {
        name: "重新填写",
        fun: function fun() {
          setIsCheck(false);
        }
      },
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        style: {
          fontSize: "16px"
        },
        children: Object.keys(info).map(function (val) {
          if (info[val] === null) {
            return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
              style: {
                padding: '4px'
              },
              children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
                style: {
                  color: "red"
                },
                children: "X"
              }), " ", info["".concat(val, "_check")]]
            });
          }
        })
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_components_back_modal__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], {
      open: isOk,
      title: "\u786E\u8BA4\u4FE1\u606F",
      ok: {
        name: "立即提交",
        fun: function fun() {
          bindCard();
        }
      },
      close: {
        name: "重新填写",
        fun: function fun() {
          setIsOk(false);
        }
      },
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        style: {
          fontSize: "14px",
          padding: "0 5px"
        },
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          style: {
            paddingBottom: "5px"
          },
          children: ["\u771F\u5B9E\u59D3\u540D: ", info.real_name]
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          style: {
            paddingBottom: "5px"
          },
          children: ["\u5361\u6FC0\u6D3B\u7801: ", info.good_key]
        })]
      })
    })]
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Index);

/***/ }),

/***/ "./src/components/back/index.scss":
/*!****************************************!*\
  !*** ./src/components/back/index.scss ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/components/back/modal.jsx":
/*!***************************************!*\
  !*** ./src/components/back/modal.jsx ***!
  \***************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.scss */ "./src/components/back/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);





var Modal = function Modal(props) {
  var open = props.open,
    title = props.title,
    ok = props.ok,
    close = props.close;
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsxs"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtModal */ "k"], {
    isOpened: open,
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtModalHeader */ "n"], {
      children: title
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtModalContent */ "m"], {
      children: props.children
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsxs"])(taro_ui__WEBPACK_IMPORTED_MODULE_1__[/* AtModalAction */ "l"], {
      children: [Boolean(close) ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Button"], {
        onClick: function onClick() {
          return close.fun();
        },
        children: close.name
      }) : null, /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_0__["Button"], {
        onClick: function onClick() {
          return ok.fun();
        },
        children: ok.name
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__["a"] = (Modal);

/***/ }),

/***/ "./src/pages/bind/add.jsx":
/*!********************************!*\
  !*** ./src/pages/bind/add.jsx ***!
  \********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_add_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./add.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/bind/add.jsx");


var config = {"navigationBarTitleText":"用卡信息提交"};


var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_add_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/bind/add', {root:{cn:[]}}, config || {}))



/***/ })

},[["./src/pages/bind/add.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=add.js.map
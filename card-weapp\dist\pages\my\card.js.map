{"version": 3, "file": "pages/my/card.js", "sources": ["webpack:///./src/pages/my/card.jsx", "webpack:///./src/pages/my/card.jsx?c725"], "sourcesContent": ["import { Component } from 'react'\r\nimport { View } from '@tarojs/components'\r\nimport MyCard from '@/components/card/list';\r\n\r\nfunction Index (props) { \r\n  return (\r\n    <View className='index'>\r\n       <View className=\"cardList\">\r\n        <MyCard /> \r\n      </View>\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./card.jsx\"\nvar config = {\"navigationBarTitleText\":\"我的卡包\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/my/card', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;ACbA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
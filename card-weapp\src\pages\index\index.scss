page {
    background-color: #c50711;
}
.top_pic {
    position: fixed;
    top: 0;
    height: 705px;
    width: 100%;
    z-index: 0;
    //filter: blur(24px);
}
.images_bottom {
    position: absolute;
    z-index: 20;
    bottom: 0;
    height: 240px;
    width: 100%;
    //background: linear-gradient(rgba(255,255,255,0), rgba(57,106,200,1) ); 
}
.index_swiper {
    width: 100%;
    height: 660px;
    .images {
        width: 100%;
        height: 660px;
    }
}
.index_center{
    width: 90%;
    margin: 580px auto 0 auto;
    background-color: #fff;
    border-radius: 20px;
    position: relative;
    .nav {
        padding: 40px 30px 30px 30px;
        font-size: 14px;
    }
    .btn {
        background-color: #0367D7;
        color: #fff;
        font-size: 40px;
        text-align: center;
        width: 90%;
        margin: 20px auto;
        border-radius: 40px;
        padding: 20px 0;
        font-size: 38px;
    }
    .swiper {
        margin-top: 40px;
        width: 100%;
        height: 200px;
    }
    .images {
        width: 100%;
        height: 200px;
        border-radius:  0 0 20px 20px;
    }
}
.at-grid__flex .content-inner__text {
    margin-top: 20px;
    color: #343434 !important;
    font-size: 30px !important;
}
.at-grid__flex .content-inner__img {
    width: 70px;
    height: 70px;
}
.content_index {
    width: 95%;
    margin-left: 5%;
}
.index_brand_top {
    color: #fff;
    margin-top: 40px;
    margin-bottom: 30px;
    .title {
        font-size: 45px;
    }
    .at-icon{
        margin-top: -7px;
    }
    .more {
        text-align: right;
        padding-right: 40px;
        padding-top: 15px;
    }
}
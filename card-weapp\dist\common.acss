.cardlayout {
  background-color: #015E92;
  padding: 0 30rpx;
  border-radius: 15rpx;
  color: #fff;
  margin-top: 20rpx;
}
.cardlayout .number {
  font-size: 25rpx;
  text-align: right;
  padding-top: 15rpx;
  margin-bottom: 6rpx;
}
.cardlayout .logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 100rpx;
  background-color: #fff;
}
.cardlayout .title {
  margin-top: 12rpx;
  font-weight: bold;
  font-size: 33rpx;
}
.cardlayout .date {
  color: #dad8d8;
  font-size: 22rpx;
}
.cardlayout .bottom {
  height: 35rpx;
}
.at-icon {
  margin-top: -5rpx;
}
.skeleton {
  padding: 50rpx 30rpx;
  background: #EDEDED;
  line-height: 20rpx;
  border-radius: 10rpx;
  margin-top: 10rpx;
}
.skeleton-row, .skeleton-title, .skeleton-action {
  background-color: #e3e4e6;
}
page {
  background-color: #EDEDED;
  overflow-x: hidden;
}

.brand_top_pic {
  width: 100%;
}

.brand_swiper {
  width: 100%;
  height: 480rpx;
}

.brand_swiper .images {
  min-width: 100%;
  height: 480rpx;
}

.brand_content {
  width: 90%;
  background-color: #fff;
  border-radius: 0 0 30rpx 30rpx;
  position: relative;
  z-index: 5;
  padding: 40rpx 5% 40rpx 5%;
}

.brand_content .title {
  color: #333333;
  font-size: 46rpx;
  font-weight: bold;
}

.brand_content .memo {
  font-size: 28rpx;
  padding: 15rpx 0;
  color: #5A5A5C;
}

.brand_content .open {
  margin-left: 20rpx;
  width: 110rpx;
  height: 100rpx;
  background-color: #0367D9;
  color: #fff;
  font-size: 20rpx;
  top: 45%;
  text-align: center;
  border-radius: 120rpx;
  padding-top: 10rpx;
}

.brand_content .open .num {
  font-size: 26rpx;
  font-weight: 600;
}

.brand_content .close {
  background-color: #bfc1c3;
}

.brand_content .list_show {
  border-bottom: 1rpx solid #F0F2F1;
  margin-top: 30rpx;
  padding: 5rpx 0;
}

.brand_content .list_show .text {
  font-size: 26rpx;
  color: #333335;
  padding-top: 5rpx;
}

.brand_content .list_show .icon {
  width: 40rpx;
  height: 40rpx;
}

.brand_title {
  position: relative;
  margin-top: 40rpx;
}

.brand_title .t_name {
  font-size: 32rpx;
  padding-left: 5%;
}

.brand_title .t_icon {
  position: absolute;
  left: -18rpx;
  top: 8rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 30rpx;
  background-color: #0565DF;
}

.brand_html {
  padding: 3% 3% !important;
  font-size: 28rpx;
}

.htmlformat {
  line-height: 30rpx;
}

.htmlformat img {
  max-width: 100%;
}

.nocard {
  padding: 1rpx 25rpx 0 20rpx;
  color: rgba(95, 91, 91, 0.8);
  display: inline-block;
  min-width: 70rpx;
  line-height: 22rpx;
}

.hadcard {
  margin-top: -4rpx;
  display: inline-block;
}

rich-text .richImg {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
  height: auto !important;
  width: auto !important;
  margin: 10rpx 0;
}

.brand_memu {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 150rpx;
  background-color: #fff;
}

.brand_memu .c_icon {
  width: 40rpx;
  height: 40rpx;
}

.brand_memu .book {
  background-color: #0565DF;
  width: 85%;
  height: 50rpx;
  text-align: center;
  color: #fff;
  padding: 10rpx;
  border-radius: 15rpx;
  margin-top: 18rpx;
  line-height: 50rpx;
  border: 0;
  font-weight: 600;
}

.brand_memu .book_close {
  background-color: #a2a6aa;
  width: 90%;
  height: 50rpx;
  text-align: center;
  color: #fff;
  padding: 10rpx;
  border-radius: 15rpx;
  margin-top: 18rpx;
  margin-left: 15%;
  line-height: 50rpx;
  border: 0;
}

.brand_memu .card_status {
  padding-top: 30rpx;
  font-size: 24rpx;
  padding-left: 4%;
}

.brand_memu .card_status .ok {
  display: inline-block;
  border: 1rpx solid #04965a;
  color: #04965a;
  padding: 4rpx 10rpx;
  border-radius: 9rpx;
  margin-left: 8rpx;
}

.brand_memu .card_status .no {
  border: 1rpx solid #a5a5a5;
  color: #a5a5a5;
}

.custom-calendar .calendar-body .calendar-day .calendar-extra-info {
  text-align: left;
  padding-left: 18rpx;
  padding-top: 2rpx;
}

.custom-calendar .calendar-head > view {
  text-align: left;
  text-indent: 30rpx;
  color: #4c4c52;
  padding-bottom: 20rpx;
  font-weight: bold;
}

.custom-calendar .calendar-picker {
  padding: 20rpx 0;
  font-weight: bold;
}

.custom-calendar .calendar-body .calendar-day {
  color: #30333e;
}

.custom-calendar .calendar-body .not-this-month > .calendar-date {
  color: #b5bcc3;
}

.brand_pic_list {
  width: 100%;
  height: 500rpx;
}

.brand_tips_list {
  width: 100%;
  height: 300rpx;
}

.greenb .at-badge__num {
  background: #4CAF50;
}

.greenk .at-badge__num {
  background: #8BC34A;
}

.brand_pic_tips {
  border-radius: 10rpx;
  width: 100%;
  height: 300rpx;
}

.brand_pic {
  width: 100%;
  height: 500rpx;
}

.brand_top {
  background-color: #fff;
  padding: 30rpx 0 5rpx 10rpx;
}

.brand_name {
  padding-left: 10rpx;
}

.brand_city {
  padding-left: 5rpx;
  display: block;
  font-size: 25rpx;
  color: #ac7228;
}

.brand_name_txt {
  font-weight: bold;
  font-size: 40rpx;
}

.brand_address {
  padding-top: 8rpx;
  font-size: 25rpx;
  color: #646465;
}

.brand_map {
  margin-top: 10rpx;
  width: 160rpx;
  height: 80rpx;
  margin-left: 15rpx;
}

.brand_vlog_list {
  padding: 20rpx 10rpx 20rpx 10rpx;
}

.brand_line {
  background-color: #f6f7fb;
  height: 10rpx;
}

.at-tabs__header {
  background-color: #f6f7fb !important;
}

.brand_tab {
  background-color: #f6f7fb !important;
}

.brand_wer {
  display: inline-block;
  font-size: 25rpx;
  color: #383838;
  padding-left: 20rpx;
}

.brand_wer_pic {
  width: 35rpx;
  height: 30rpx;
  margin-right: 5rpx;
}

.brand_tt {
  background-color: #fff;
  padding: 20rpx 20rpx 0 20rpx;
}

.brand_tt_per {
  border-bottom: 1rpx solid #f6f7fb;
  padding: 20rpx 5rpx 15rpx 5rpx;
}

.brand_tt_tags {
  padding: 10rpx 0;
  font-size: 25rpx;
}

.brand_tt_tips {
  color: #6ff9a8;
}

.brand_tt_sale {
  color: #666;
  padding-top: 5rpx;
}

.brand_bar_btn {
  padding: 8rpx 5rpx;
  text-align: center;
  background: -webkit-gradient(linear, left top, right top, color-stop(20%, rgb(255, 137, 2)), color-stop(50%, rgb(255, 114, 3)), color-stop(87%, rgb(255, 94, 3)));
  background: linear-gradient(left, rgb(255, 137, 2) 20%, rgb(255, 114, 3) 50%, rgb(255, 94, 3) 87%);
  background: -webkit-linear-gradient(left, rgb(255, 137, 2) 20%, rgb(255, 114, 3) 50%, rgb(255, 94, 3) 87%);
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #fff;
  width: 80%;
  margin-top: 4rpx;
  font-weight: 200;
}

.brand_buy_btn_time {
  padding: 6rpx;
  width: 50%;
  text-align: center;
  background: -webkit-gradient(linear, left top, right top, color-stop(20%, #e80000), color-stop(50%, #e83434), color-stop(87%, #f51a1a));
  background: linear-gradient(left, #e80000 20%, #e83434 50%, #f51a1a 87%);
  background: -webkit-linear-gradient(left, #e80000 20%, #e83434 50%, #f51a1a 87%);
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #fff;
  margin-top: 5rpx;
  float: right;
  font-weight: 300;
}

.brand_buy_btn {
  padding: 6rpx;
  width: 50%;
  text-align: center;
  background: -webkit-gradient(linear, left top, right top, color-stop(20%, rgb(255, 137, 2)), color-stop(50%, rgb(255, 114, 3)), color-stop(87%, rgb(255, 94, 3)));
  background: linear-gradient(left, rgb(255, 137, 2) 20%, rgb(255, 114, 3) 50%, rgb(255, 94, 3) 87%);
  background: -webkit-linear-gradient(left, rgb(255, 137, 2) 20%, rgb(255, 114, 3) 50%, rgb(255, 94, 3) 87%);
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #fff;
  margin-top: 5rpx;
  float: right;
  font-weight: 300;
}

.brand_buy_btn_qi {
  padding: 6rpx;
  width: 50%;
  text-align: center;
  background: -webkit-gradient(linear, left top, right top, color-stop(20%, rgb(232, 90, 138)), color-stop(50%, rgb(253, 27, 104)), color-stop(87%, rgb(185, 0, 63)));
  background: linear-gradient(left, rgb(232, 90, 138) 20%, rgb(253, 27, 104) 50%, rgb(185, 0, 63) 87%);
  background: -webkit-linear-gradient(left, rgb(232, 90, 138) 20%, rgb(253, 27, 104) 50%, rgb(185, 0, 63) 87%);
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #fff;
  margin-top: 5rpx;
  float: right;
  font-weight: 300;
}

.brand_tt_shu {
  padding: 0 10rpx;
}

.brand_show_all {
  background-color: #fff;
  text-align: center;
  color: #6fb2f9;
  padding-bottom: 18rpx;
}

.brand_buy_showp {
  color: #cccccc;
  font-size: 25rpx;
  text-decoration: line-through;
  padding-left: 15rpx;
}

.brand_buy_showqi {
  color: #a28e8e;
  font-size: 25rpx;
  padding-left: 8rpx;
}

.brand_phone {
  color: #6fb2f9;
  padding-left: 5rpx;
}

.brand_buy_price {
  color: #ec7413;
  font-size: 35rpx;
  padding-left: 35rpx;
}

.brand_tt_name {
  font-weight: bold;
  width: 90%;
  font-size: 28rpx;
  color: #383838;
}

.brand_tips {
  position: relative;
  font-size: 26rpx;
  line-height: 50rpx;
  padding: 10rpx 15rpx;
}

.brand_bar {
  padding: 10rpx 0;
  position: fixed !important;
  height: 60rpx;
  background-color: #fff;
  bottom: 0;
  width: 98%;
  z-index: 999;
  left: 0;
}

.brand_good_img {
  height: 125rpx;
  width: 100%;
  border-radius: 5rpx;
}

.brand_room_date {
  background-color: #fff;
  padding: 10rpx 15rpx;
  text-align: center;
}
page {
  background-color: #fff;
}

.pageAdd .title {
  font-weight: bold;
  font-size: 35rpx;
  text-align: center;
  margin-top: 40rpx;
}

.pageAdd .info {
  font-size: 32rpx;
  text-align: center;
  margin-top: 20rpx;
}

.pageAdd .txt {
  font-size: 25rpx;
  color: rgb(160, 157, 157);
}

.pageAdd .btn {
  width: 90%;
  height: 80rpx;
  background-color: #02b921;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  border-radius: 10rpx;
  line-height: 80rpx;
  margin-top: 25rpx;
}

.pageAdd .disable {
  opacity: 0.4;
}

.addPage {
  color: #9D9D9D;
  padding: 50rpx 40rpx;
}

.addPage .item {
  margin-top: 18rpx;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.addPage .input {
  margin-top: 10rpx;
  border-bottom: 1rpx solid #E3E3E3;
  padding: 10rpx 7rpx;
  color: #000;
}

.addPage .card {
  text-align: center;
  margin-top: 60rpx;
}

.addPage .image {
  width: 180rpx;
  height: 200rpx;
  border: 1rpx solid #ccc;
  margin: 0 auto;
  border-radius: 10rpx;
  text-align: center;
  font-size: 25rpx;
  color: #2196F3;
}

.addPage .photo {
  width: 180rpx;
  height: 200rpx;
}

.addPage .photoSamll {
  width: 100rpx;
  height: 120rpx;
  margin: 0 auto;
}

.jbtn {
  position: fixed;
  bottom: 0;
  font-size: 35rpx;
  width: 100%;
  height: 90rpx;
  background-color: #02b921;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  line-height: 90rpx;
}

.okbtn {
  width: 60%;
  height: 70rpx;
  background-color: #02b921;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  border-radius: 10rpx;
  line-height: 70rpx;
  margin-top: 80rpx;
}

.at-icon {
  margin-top: 0;
}
.clear {
  width: 300rpx;
}

.clear_v {
  text-align: center;
  margin-top: 200rpx;
}
.tags {
  background-color: #F8F8FA;
  text-align: center;
  padding: 12rpx 20rpx;
  color: #191D29;
  margin: 25rpx 0 0 20rpx;
  display: inline-block;
  border-radius: 10rpx;
  min-width: 120rpx;
}

.active {
  background-color: #F1F5FF;
  color: #5075CD;
  font-weight: bold;
}
.bookItem {
  width: 90%;
  background-color: #fff;
  border-radius: 20rpx;
  margin: 25rpx auto;
  font-size: 24rpx;
  position: relative;
}
.bookItem .mask {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  top: 0;
  left: 0;
  background-color: rgba(187, 186, 186, 0.5294117647);
}
.bookItem .cardNum {
  border-bottom: 1rpx solid #f1eded;
  padding: 20rpx 20rpx;
  color: #696969;
}
.bookItem .week {
  font-size: 32rpx;
  margin-bottom: 5rpx;
}
.bookItem .day {
  font-size: 32rpx;
  color: #000;
}
.bookItem .line {
  width: 90rpx;
}
.bookItem .linetext {
  text-align: left;
  padding-left: 15rpx;
  color: #8479dc;
}
.bookItem .brand {
  margin-top: 3rpx;
  font-size: 35rpx;
}
.bookItem .address {
  font-size: 28rpx;
  color: #807f7f;
}
.bookItem .oper {
  background-color: #edf9ff;
  padding: 20rpx 0;
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  color: #2196F3;
  font-size: 26rpx;
}
.copy_btn {
  background-color: #FF5722;
  font-size: 24rpx;
  padding: 2rpx 6rpx;
  display: inline-block;
  border-radius: 10rpx;
  margin: -4rpx 0 0 12rpx;
  color: #fff;
  text-align: center;
}
.htmlformat {
  line-height: 30rpx;
  width: 95%;
  margin: 0 auto;
}
.htmlformat img {
  max-width: 100%;
}
.brand_html {
  padding: 3% 4% !important;
  font-size: 30rpx;
}
rich-text .richImg {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
  height: auto !important;
  width: auto !important;
  margin: 10rpx 0;
}
page {
  background-color: #fff;
}

.pageAdd .title {
  font-weight: bold;
  font-size: 35rpx;
  text-align: center;
  margin-top: 40rpx;
}

.pageAdd .info {
  font-size: 32rpx;
  text-align: center;
  margin-top: 20rpx;
}

.pageAdd .txt {
  font-size: 25rpx;
  color: rgb(160, 157, 157);
}

.pageAdd .btn {
  width: 90%;
  height: 80rpx;
  background-color: #02b921;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  border-radius: 10rpx;
  line-height: 80rpx;
  margin-top: 25rpx;
}

.pageAdd .disable {
  opacity: 0.4;
}

.pageAdd .buybtn {
  background-color: #FF5722;
}

.addPage {
  color: #9D9D9D;
  padding: 50rpx 40rpx;
}

.addPage .input {
  margin-top: 10rpx;
  border-bottom: 1rpx solid #E3E3E3;
  padding: 10rpx 7rpx;
  color: #000;
}

.addPage .card {
  text-align: center;
  margin-top: 60rpx;
}

.addPage .image {
  width: 180rpx;
  height: 200rpx;
  border: 1rpx solid #ccc;
  margin: 0 auto;
  border-radius: 10rpx;
  text-align: center;
  font-size: 25rpx;
  color: #2196F3;
}

.addPage .photo {
  width: 180rpx;
  height: 200rpx;
}

.addPage .photoSamll {
  width: 100rpx;
  height: 120rpx;
  margin: 0 auto;
}

.jbtn {
  position: fixed;
  bottom: 0;
  font-size: 35rpx;
  width: 100%;
  height: 90rpx;
  background-color: #02b921;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  line-height: 90rpx;
}

.okbtn {
  width: 60%;
  height: 70rpx;
  background-color: #02b921;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  border-radius: 10rpx;
  line-height: 70rpx;
  margin-top: 80rpx;
}

.checkbtn {
  width: 60%;
  height: 70rpx;
  background-color: #fff;
  border: solid 1rpx #ddd;
  color: #333;
  margin: 0 auto;
  text-align: center;
  border-radius: 10rpx;
  line-height: 70rpx;
  margin-top: 20rpx;
}

.at-icon {
  margin-top: 0;
}
.noraml {
  width: 620rpx;
  height: 500rpx;
}

.list {
  width: 680rpx;
  height: 515rpx;
  margin: 0 auto 40rpx auto;
}

.listz {
  width: 680rpx;
  height: 615rpx;
  margin: 0 auto 40rpx auto;
}

.listl {
  width: 350rpx;
  height: 615rpx;
  margin: 10rpx;
  float: left;
}

.brandIndex {
  background-color: #fff;
  border-radius: 20rpx;
  position: relative;
}

.brandIndex .images {
  width: 100%;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 275rpx;
  min-height: 275rpx;
}

.brandIndex .images_l {
  width: 100%;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 300rpx;
}

.brandIndex .images_lz {
  width: 100%;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 380rpx;
}

.brandIndex .title {
  color: #313234;
  font-size: 35rpx;
  font-weight: bold;
  padding: 15rpx 20rpx;
}

.brandIndex .info {
  font-size: 26rpx;
  color: #777777;
  padding-left: 20rpx;
}

.brandIndex .top_tags {
  position: absolute;
  height: 40rpx;
  background-color: #ED8502;
  color: #fff;
  top: 0;
  left: 0;
  border-radius: 20rpx 0;
  font-size: 20rpx;
  z-index: 1;
  text-align: center;
  line-height: 40rpx;
  padding: 10rpx;
}

.brandIndex .status {
  position: absolute;
  right: 20rpx;
}

.brandIndex .open {
  width: 130rpx;
  height: 120rpx;
  background-color: #0367D9;
  color: #fff;
  font-size: 20rpx;
  top: 42%;
  text-align: center;
  border-radius: 120rpx;
  padding-top: 10rpx;
  border: 5rpx solid #fff;
  z-index: 100;
}

.brandIndex .open .num {
  font-weight: bold;
  font-size: 40rpx;
}

.brandIndex .close {
  width: 80rpx;
  height: 80rpx;
  border-radius: 80rpx;
  text-align: center;
  border: 5rpx solid #fff;
  top: 55%;
  line-height: 80rpx;
  background-color: #bfc1c3;
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
  line-height: 10rpx;
}

.swiper {
  height: 500rpx;
  width: 100%;
}

.swiper-item-with-margin {
  margin-right: 30rpx;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: calc(100% - 30rpx) !important;
}

.at-float-layout__container {
  max-height: 1100rpx;
  overflow: hidden;
}

.time_item {
  background-color: #8C8C8C;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  width: 40rpx;
  height: 40rpx;
  background: #8C8C8C;
  color: #fff;
  font-size: 25rpx;
  border-radius: 10rpx;
  margin: 0 5rpx;
}

.time_month {
  width: 30rpx;
  height: 30rpx;
  font-size: 20rpx;
}

.tips_one {
  background-color: #8BC34A;
  font-size: 24rpx;
  padding: 8rpx;
  display: inline-block;
  width: 100rpx;
  border-radius: 10rpx;
  margin: 20rpx;
  color: #fff;
  text-align: center;
}

.tips_two {
  background-color: #FF9800;
  color: "#fff";
  font-size: 24rpx;
  padding: 8rpx;
  display: inline-block;
  width: 250rpx;
  border-radius: 10rpx;
  margin: 20rpx 5rpx;
  color: #fff;
  text-align: center;
}

.tips_three {
  background-color: #FF9800;
  color: "#fff";
  font-size: 24rpx;
  padding: 8rpx 10rpx;
  display: inline-block;
  border-radius: 10rpx;
  margin: 20rpx 8rpx;
  color: #fff;
  text-align: center;
}
page {
  background-color: #EDEDED;
}

.sreach_index {
  padding: 20rpx;
  color: rgb(61, 60, 60);
  font-size: 32rpx;
}

.sreach_tags {
  background-color: #e8e7e7;
  padding: 8rpx 15rpx;
  display: inline-block;
  margin-right: 14rpx;
  margin-top: 18rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.sreach_tools .show {
  display: block;
}

.sreach_tools .hide {
  display: none;
}

.h_only {
  height: 100rpx;
}

.h_all {
  height: 180rpx;
}

.sreach_content {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 200;
}

.sreach_content .md {
  padding-top: 20rpx;
}

.sreach_content .init {
  color: #666;
}

.sreach_content .selected {
  color: #6190E8;
}

.sreach_content .sreach_sel {
  text-align: center;
  border-bottom: 1rpx solid #e6e6e6;
  height: 80rpx;
  vertical-align: middle;
  background-color: #fff;
}

.sreach_content .sel_area {
  width: 100%;
  background-color: #fff;
}

.sreach_content .sel_area .sel_item {
  padding: 0 10rpx;
  min-height: 250rpx;
  margin-bottom: 20rpx;
}

.sreach_content .sel_list {
  padding-bottom: 35rpx;
  background-color: #fff;
}

.sreach_content .sel_list .sel_list_item {
  background-color: #fff;
  padding: 30rpx 0 0 30rpx;
  font-size: 32rpx;
}

.sreach_content .sel_list .active {
  color: #5075CD;
}

.sel_btn {
  border-top: 1rpx solid #e6e6e6;
  padding: 20rpx 0;
  font-size: 32rpx;
}

.at-icon {
  margin-top: -7rpx;
}
.lastuse {
  font-size: 28rpx;
  color: #A5A5A5;
}

.at-icon {
  margin-top: -6rpx;
}

.my_avatr {
  padding: 50rpx 30rpx 20rpx 30rpx;
  background-color: #fff;
  color: #333333;
  width: 92%;
}

.my_avatr .myhead {
  width: 140rpx;
  height: 140rpx;
  border-radius: 140rpx;
}

.my_avatr .nickname {
  font-size: 40rpx;
  margin-top: 18rpx;
}

.my_avatr .nickinfo {
  margin-top: 8rpx;
  text-indent: 4rpx;
}

.useItem {
  width: 90%;
  margin: 10rpx auto;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}

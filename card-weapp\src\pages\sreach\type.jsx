/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-18 00:11:31
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/type.jsx
 */
import { Component, useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import { useSelector } from 'react-redux'
import Taro, { useReachBottom } from '@tarojs/taro'
import Memu from '@/components/memu'
import None from '@/components/tips/none'
import SreachTools from './bar'
import Item from '@/components/brand/item'
import tools from '@/utils/tools'
import "./index.scss"

const SreachIndex = (props) => {
    const query = tools.getQuery()
    const login = useSelector(state => state.login)
    const [init, setInit] = useState(null)
    const [page , setPage] = useState(1)
    const [brandList , setBrandList] = useState([])  
    const [loaded, setLoaded] = useState(false)
    const [title, setTitle] = useState('')
    useEffect( async () => {
        setLoaded(false)
        Taro.showLoading({
          mask: true,
          title: '请稍等'
        })
        if (login) {
          const apiParams = {
            line_id: tools.line_code,
            order : "km",
            point : true,
            action : "type",
            type_list : [query.data.v],
            page : {
                current: page,
                pageSize : 20
            }
          }
          const data = await tools.Api.brandList(apiParams)
          if (data.code === 200) {
            setInit(data.data.page)
            setBrandList([
                ...brandList,
                ...data.data.list
            ])
            setTitle(data.data.typeShow.title)
            Taro.setNavigationBarTitle({
              title: `${data.data.typeShow.title}相关`
            })
          }
          setLoaded(true)
          Taro.hideLoading()
        }
    }, [login, page])

    useReachBottom(() => {
        const nextPage = init.current + 1
        if (nextPage <= init.totalPage) {
          setPage(nextPage)
        } else {
          Taro.showToast({
            title: '暂无更多内容',
            icon: 'none',
            duration: 2000
          })
        }
    })

    return (
        <View>
          <View style={{fontSize: '36px', padding: '20px', fontWeight: 'bold'}}>{title}</View>
          <View>
            {
                (brandList.length > 0) ?  brandList.map(v => <Item className="list" data={v} cname="images_l"/>) : <None loaded={loaded}/>
            }
          </View>
          <Memu now={3} />
        </View>
    )
}
export default SreachIndex 
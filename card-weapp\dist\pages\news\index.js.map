{"version": 3, "file": "pages/news/index.js", "sources": ["webpack:///./src/pages/news/index.jsx", "webpack:///./src/pages/news/index.jsx?a958"], "sourcesContent": ["/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2021-12-27 17:13:21\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/news/index.jsx\r\n */\r\nimport {  useState, useEffect } from 'react'\r\nimport { View, Text } from '@tarojs/components'\r\nimport { AtList, AtListItem, AtIcon } from \"taro-ui\"\r\nimport Taro from '@tarojs/taro'\r\nimport tools from '@/utils/tools'\r\nimport \"./index.scss\"\r\n\r\nconst SreachIndex = () => {\r\n    const [list, setList] = useState([])\r\n    useEffect( async () => {\r\n      Taro.showLoading({\r\n        mask: true,\r\n        title: '读取中'\r\n      })\r\n      const data = await tools.Api.noteList()\r\n      if (data.code === 200) {\r\n        setList((data.data.length === 0) ? null : data.data)\r\n      }\r\n      Taro.hideLoading()\r\n    }, [])\r\n    \r\n    return (\r\n        <View>\r\n          {\r\n            (list === null) ? <View className='index'>\r\n            <View style={{textAlign: 'center', marginTop: \"60px\", color: \"#333\", fontSize: \"16px\"}}>\r\n              <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>\r\n              <View style={{marginTop: \"10px\" , color: \"#848181\"}}>暂无通知</View>\r\n            </View>\r\n        </View> : <AtList>\r\n            {\r\n              (list !== null) && list.map(v =>  <AtListItem title={v.title} note= {v.add_time} arrow='right' onClick={() => Taro.navigateTo({ url : `/pages/news/info?v=${v.code}` })}/>)\r\n            }\r\n            </AtList>\r\n          }\r\n          \r\n        </View>\r\n    )\r\n}\r\nexport default SreachIndex ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.jsx\"\nvar config = {\"navigationBarTitleText\":\"最新通知\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/news/index', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAKA;AACA;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
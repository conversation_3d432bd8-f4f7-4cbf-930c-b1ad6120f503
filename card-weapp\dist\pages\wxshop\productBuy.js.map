{"version": 3, "file": "pages/wxshop/productBuy.js", "sources": ["webpack:///./src/pages/wxshop/productBuy.js", "webpack:///./src/pages/wxshop/index.scss", "webpack:///./src/pages/wxshop/productBuy.js?6b3a"], "sourcesContent": ["import { useState, useEffect } from 'react'\r\nimport Taro from '@tarojs/taro';\r\nimport { AtInput, AtIcon, AtInputNumber, AtFloatLayout, AtList, AtListItem  } from 'taro-ui';\r\nimport { View, Swiper, SwiperItem, Text, Image } from '@tarojs/components';\r\nimport moment from 'moment';\r\nimport Skeleton from 'taro-skeleton'\r\nimport tools from '@/utils/tools';\r\nimport './index.scss';\r\n\r\nfunction ProductBuy(props) {\r\n    const paramOper = Taro.useRouter();\r\n    let params = {};\r\n    if (paramOper.params.scene) {\r\n      params = tools.urlToObj(decodeURIComponent(paramOper.params.scene));\r\n    } else {\r\n      params = paramOper.params;\r\n    }\r\n    const weekStr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\r\n    const { productId, rulescode }  = params;\r\n    const [initData, setInitData] =useState({code: -1, data:{\r\n      buy_price: 0,\r\n      show_price: 0,\r\n      tips: []\r\n    }, content_web: {\r\n      brand: { memo : \"\", tagsname: []},\r\n      hotelList: {\r\n        facilities: []\r\n      },\r\n    }});\r\n    const [dateShow, setDateShow] = useState(false)\r\n    const [buyNum, setBuyNum] = useState(1)\r\n    const [showPrice, setShowPrice] = useState(0)\r\n    const [userInfo, setUserInfo] = useState({\r\n      receive_name: '',\r\n      receive_tel: '',\r\n      link_credit_no: -1,\r\n      user_note: '',\r\n      bookdate:'',\r\n      bookweek:''\r\n    })\r\n    const [goodConfig, setGoodConfig] = useState(\r\n      {\r\n        tel: 1,\r\n        max: 10,\r\n        limitnum: 1,\r\n        limittype: 2,\r\n        open_time_i: 0,\r\n        close_time_i: 0,\r\n        is_book: false,\r\n        any_book: false,\r\n        needAddress: false,\r\n        user_no_en: false\r\n      }\r\n    )\r\n    const [ruleData , setRuleData] = useState({\r\n      good: { name: ''},\r\n      rules: []\r\n    })\r\n\r\n    const [ selRule , setSelRule] = useState(null)\r\n\r\n    useEffect( async () => {\r\n      ableDate()\r\n      if(productId) {\r\n        const ruleList = await tools.Api.getShopGoodRule(productId);\r\n        const data = await tools.Api.getGoodWxshopLive(productId);\r\n        const goodData = await tools.Api.getShopGood({\r\n          goods_id: productId,\r\n          shopid: 1\r\n        })\r\n        setInitData(data)\r\n        setRuleData(ruleList.data)\r\n        if (rulescode === 'none') {\r\n          setShowPrice(tools.getPrice(data.data.share_price))\r\n        } else {\r\n          setSelRule(ruleList.data.rules.find( v => v.title_code === rulescode))\r\n        }\r\n        setGoodConfig({\r\n            category_id: goodData.data.goods.category_id,\r\n            path: (goodData.data.goods.category_id === '900') ? `/pages/brand/good?id=${productId}` : `/pages/brand/index?id=${goodData.data.brand.id}`,\r\n            tel: goodData.data.goods.good_tel,\r\n            max: goodData.data.goods.buyerlimit_num,\r\n            limitnum: goodData.data.goods.limitnum,\r\n            limittype: goodData.data.goods.limittype,\r\n            open_time_i: goodData.data.goods.open_time_i,\r\n            close_time_i: goodData.data.goods.close_time_i,\r\n            is_book: (goodData.data.goods.is_book === '1'),\r\n            any_book: (goodData.data.goods.is_book === '0'),\r\n            needAddress: (goodData.data.goods.category_id === '900'),\r\n            user_no_en: (goodData.data.goods.zwyneed.includes('link_credit_no') || goodData.data.goods.buyerlimit === '2')\r\n        })\r\n        if (moment().unix() > parseInt(goodData.data.goods.close_time_i, 10)) {\r\n          Taro.redirectTo({\r\n            url: '/pages/tips/index?type=w401'\r\n          })\r\n          return false\r\n        }\r\n      }\r\n    }, [])\r\n\r\n    useEffect(() => {\r\n      if (selRule !== null) {\r\n        setShowPrice(tools.getPrice(selRule.share_price))\r\n        setShowPrice(tools.getPrice((buyNum * parseFloat(selRule.share_price)).toString()))\r\n      }\r\n    }, [selRule])\r\n    \r\n    const addOrder = async () => {\r\n      const partten = /^[1][3456789]\\d{9}$/;\r\n      const userLoginInfo = tools.getData('userInfo');     \r\n      if (userLoginInfo === null || Boolean(userLoginInfo.mem_id) === false ) {\r\n        Taro.showToast({ icon: 'none', title: '登录信息错误' });\r\n        return false;\r\n      }\r\n      if (userInfo.receive_name === '') {\r\n        Taro.showToast({ icon: 'none', title: '请填写游客姓名' });\r\n        return false;\r\n      }\r\n      if (!partten.test(userInfo.receive_tel)) {\r\n        Taro.showToast({ icon: 'none', title: '请填写正确的手机号' });\r\n        return false;\r\n      }\r\n      if (goodConfig.user_no_en && !tools.IdentityCodeValid(userInfo.link_credit_no)) {\r\n        Taro.showToast({ icon: 'none', title: '请填写正确的身份证号' });\r\n        return false;\r\n      }\r\n      if (goodConfig.is_book && userInfo.bookdate === '') {\r\n        Taro.showToast({ icon: 'none', title: '请选择游玩时间' });\r\n        return false;\r\n      }\r\n      const obj = {\r\n        goods_id: parseInt(productId, 10),\r\n        stock: buyNum,\r\n        spec: [],\r\n        payment_id: 1,\r\n        in_time: 0,\r\n        out_time: 0,\r\n        re_userid: 0,\r\n        vlog_id: 0,\r\n        from_agent_id: 0,\r\n        shop_id: 1,\r\n        pay_for: 1,\r\n        origin: 'NK',\r\n        day: 1,\r\n        category_id: goodConfig.category_id,\r\n        order_name: initData.data.title,\r\n        user: {\r\n          id: userLoginInfo.mem_id,\r\n          brand_id: 0\r\n        },\r\n        ...userInfo\r\n      }\r\n      if (Boolean(obj.bookdate)) {\r\n        obj.bookdate = obj.bookdateval\r\n      }\r\n      if (selRule !== null) {\r\n        obj.rules_id = selRule.title_code;\r\n        obj.rules_name = selRule.title;\r\n      }\r\n      Taro.showLoading({\r\n        title: '提交中',\r\n        mask: true\r\n      })\r\n      const orderAdd = await tools.Api.addShopOrder(obj)\r\n      Taro.hideLoading();\r\n      if (orderAdd.code !== 0) {\r\n        Taro.showToast({ icon: 'none', title: orderAdd.msg });\r\n        return false\r\n      } else {\r\n        Taro.redirectTo({\r\n          url: `/pages/pay/index?id=${orderAdd.data.orderId}`,\r\n        });\r\n      }\r\n    }\r\n\r\n    const ableDate = () => {\r\n      const date = []\r\n      const hour = moment().hour()\r\n      for (let i= (hour > 17) ? 1 : 0; i <=8 ; i++) {\r\n        date.push({\r\n          day : moment().add(i, 'days').format('MM月DD日'),\r\n          week : weekStr[moment().add(i, 'days').weekday()],\r\n          val: moment().add(i, 'days').format('YYYY-MM-DD')\r\n        })\r\n      }\r\n      return date\r\n    }\r\n\r\n    return (initData.code === -1) ? ( <View><Skeleton title=\"\" row={30}></Skeleton></View>) :\r\n    (<View>\r\n        <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>\r\n          <View className='at-row'>\r\n            <View className='at-col at-col-3'>\r\n              <Image mode=\"widthFix\" src={initData.data.logo} style={{width: '100px', borderRadius: '8px'}}/>\r\n            </View>\r\n            <View className='at-col at-col-1 at-col--auto'></View>\r\n            <View className='at-col at-col-8 at-col--wrap' style={{fontSize: '16px',paddingLeft: '7px', marginTop: '0px'}}>\r\n              <View>{initData.data.name}{ruleData.good.name}</View>\r\n              <View style={{marginTop: '12px'}}>\r\n                <View className='at-row'>\r\n                  <View className='at-col at-col-4' style={{paddingTop: '4px', color: '#756e6e', fontSize:'14px'}}>购买数量:</View>\r\n                  <View className='at-col at-col-1 at-col--auto'></View>\r\n                  <View className='at-col at-col-7'>\r\n                    <AtInputNumber\r\n                    min={1}\r\n                    max={parseInt(goodConfig.max, 10)}\r\n                    step={1}\r\n                    value={buyNum}\r\n                    onChange={(value)=>{\r\n                      setBuyNum(value)\r\n                      const onePirce = (selRule === null) ? parseFloat(initData.data.share_price) : parseFloat(selRule.share_price)\r\n                      setShowPrice(tools.getPrice((value * onePirce).toString()))\r\n                    }}\r\n                  />\r\n                  </View>\r\n                </View>\r\n              </View>\r\n            </View>\r\n          </View>\r\n        </View>\r\n        {(selRule !== null) ? <View>\r\n          <View style={{borderLeft: '4px solid #2196F3', margin: '20px 10px', textIndent: '10px'}}>使用时间 <Text style={{paddingLeft: '5px', fontSize: '14px', color: '#585757'}}>(有效期:{moment(ruleData.good.open_time_i * 1000).format('YYYY-MM-DD')}至{moment(ruleData.good.close_time_i * 1000).format('YYYY-MM-DD')})</Text></View>\r\n          <View style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>\r\n            {ruleData.rules.map( (v, index) => <View className=\"at-row\" style={(ruleData.rules.length === index + 1) ? {} :{borderBottom: '1px solid #ece8e8', marginBottom: '7px'}} onClick={() => setSelRule(v)}>\r\n              <View className='at-col at-col-10'>\r\n                <View style={{fontSize: '14px', fontWeight: 'bold'}}>{v.title}</View>\r\n                <View style={{padding: '8px 0', color: '#F44336', fontSize: '13px'}}>有效期内{tools.dateInfo(v.stime, v.etime, v.enable_week)}</View>\r\n              </View>\r\n              {\r\n                (selRule.title_code === v.title_code) ? <View className='at-col at-col-2' style={{textAlign: 'center'}}>\r\n                <View style={{background: '#eae9e9', width: '20px', height: '20px', borderRadius: '30px', textAlign: 'center', padding: '5px', margin: '5px 15px'}}>\r\n                  <AtIcon value='check' size='22' color='#4CAF50'></AtIcon>\r\n                </View>\r\n              </View>   : null\r\n              }\r\n            </View>)}\r\n          </View>\r\n        </View> : null}\r\n        \r\n        <View style={{borderLeft: '4px solid #2196F3', margin: '20px 10px', textIndent: '10px'}}>预定人信息</View>\r\n        <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>\r\n          <View className=\"at-row\">\r\n            <View className='at-col at-col-2' style={{fontSize: '14px'}}>\r\n              游客姓名\r\n            </View>\r\n            <View className='at-col at-col-1'></View>    \r\n            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#8a8787'}}>\r\n              <AtInput placeholder=\"必填, 便于商家为您提供服务\" name=\"receive_name\" value={userInfo.receive_name} maxLength={6} onChange={(value) => {\r\n                setUserInfo({\r\n                  ...userInfo,\r\n                  receive_name: value\r\n                })\r\n              }}/>\r\n            </View> \r\n            <View className='at-col at-col-1' style={{position:'relative'}}>\r\n              <View style={{position: 'absolute', right: 0, top: '-1px'}}>\r\n                <AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>\r\n              </View>\r\n            </View>    \r\n          </View>\r\n        </View>\r\n        <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>\r\n          <View className=\"at-row\">\r\n            <View className='at-col at-col-2' style={{fontSize: '14px'}}>\r\n              手机号码\r\n            </View>\r\n            <View className='at-col at-col-1'></View>    \r\n            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#8a8787'}}>\r\n              <AtInput placeholder=\"必填, 用于接收使用验证码\" name=\"receive_tel\" type=\"number\" value={userInfo.receive_tel} maxLength={15} onChange={(value) => {\r\n                // console.log(value)\r\n                setUserInfo({\r\n                  ...userInfo,\r\n                  receive_tel: value\r\n                })\r\n              }}/>\r\n            </View> \r\n            <View className='at-col at-col-1' style={{position:'relative'}}>\r\n              <View style={{position: 'absolute', right: 0, top: '-1px'}}>\r\n                <AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>\r\n              </View>\r\n            </View>    \r\n          </View>\r\n        </View>\r\n        {(goodConfig.user_no_en) ? <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>\r\n          <View className=\"at-row\">\r\n            <View className='at-col at-col-2' style={{fontSize: '14px'}}>\r\n              身份证号\r\n            </View>\r\n            <View className='at-col at-col-1'></View>    \r\n            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#8a8787'}}>\r\n              <AtInput placeholder=\"必填, 此产品需实名使用\" type=\"idcard\" name=\"link_credit_no\" value={userInfo.link_credit_no} maxLength={20} onChange={(value) => {\r\n                setUserInfo({\r\n                  ...userInfo,\r\n                  link_credit_no: value\r\n                })\r\n              }}/>\r\n            </View> \r\n            <View className='at-col at-col-1' style={{position:'relative'}}>\r\n              <View style={{position: 'absolute', right: 0, top: '-1px'}}>\r\n                <AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>\r\n              </View>\r\n            </View>    \r\n          </View>\r\n        </View> : null}\r\n        {(goodConfig.is_book) ?    <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}} onClick={() => setDateShow(true)}>\r\n          <View className=\"at-row\">\r\n            <View className='at-col at-col-2' style={{fontSize: '14px'}}>\r\n              游玩时间\r\n            </View>\r\n            <View className='at-col at-col-1'></View>    \r\n            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#d0caca'}}>\r\n              {\r\n                (userInfo.bookdate === '') ? '必选, 请选择出游时间' : <Text style={{color: '#000'}}>{userInfo.bookweek} {userInfo.bookdate}</Text>\r\n              }\r\n            </View> \r\n            <View className='at-col at-col-1' style={{position:'relative'}}>\r\n              <View style={{position: 'absolute', right: 0, top: '-3px'}}>\r\n                <Text style={{color: '#2196F3', fontSize: '14px'}}>选择</Text><AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>\r\n              </View>\r\n            </View> \r\n          </View>\r\n        </View> : null}\r\n    \r\n        <View  style={{margin: '10px', padding: '15px 10px', background: '#fff', borderRadius: '10px'}}>\r\n          <View className=\"at-row\">\r\n            <View className='at-col at-col-2' style={{fontSize: '14px'}}>\r\n            备注信息\r\n            </View>\r\n            <View className='at-col at-col-1'></View>    \r\n            <View className='at-col at-col-8' style={{fontSize: '14px', color: '#8a8787'}}>\r\n              <AtInput placeholder=\"选填, 可填写需求和留言\" name=\"user_note\" value={userInfo.user_note} maxLength={40} onChange={(value) => {\r\n                console.log(value)\r\n                setUserInfo({\r\n                  ...userInfo,\r\n                  user_note: value\r\n                })\r\n              }}/>\r\n            </View> \r\n            <View className='at-col at-col-1' style={{position:'relative'}}>\r\n              <View style={{position: 'absolute', right: 0, top: '-1px'}}>\r\n                <AtIcon value='chevron-right' size='20' color='#2196F3'></AtIcon>\r\n              </View>\r\n            </View>    \r\n          </View>\r\n        </View>\r\n        <View className='at-row good_live_buybtn'>\r\n        <View className='at-col at-col-8' style={{paddingTop: '4px', color: '#ec7413', paddingLeft: '13px'}}>\r\n          <View><Text style={{ paddingRight: '8px', color: '#333'}}>共 <Text style={{fontWeight: 'bold'}}>{buyNum}</Text> 件，合计</Text><Text style={{fontSize: '14px', paddingRight: '2px'}}>¥</Text><Text style={{fontSize: '24px'}}>{showPrice}</Text></View> \r\n        </View>\r\n        <View className='at-col at-col-4' onClick={ async () => {\r\n          \r\n          await addOrder()\r\n        }}>            \r\n        <View className={(parseInt(initData.data.gc_inventory, 10) > 0) ? 'go_btn go_buy' : 'go_btn go_out'} style={{marginRight: '20px'}}>{(parseInt(initData.data.gc_inventory, 10) > 0) ? '立即支付' : '已售罄'}</View>\r\n      </View>\r\n    </View>\r\n    <View style={{height: '90px'}}></View>\r\n    <AtFloatLayout isOpened={dateShow} title=\"请选择游玩时间\" onClose={() => setDateShow(false)}>\r\n      <View className='at-row at-row--wrap' style={{margin: '6px 0'}}>\r\n        {\r\n          ableDate().map(v => <View className='at-col at-col-4'><View style={{border: '1px solid #03A9F4', width: '100px', padding: '5px', textAlign: 'center', margin: '6px 0', color: `${userInfo.bookdate === v.day ? '#fff' : '#03A9F4'}`, background: `${userInfo.bookdate === v.day ? '#03A9F4' : '#fff'}`, borderRadius: '4px'}} onClick={() => {\r\n            setUserInfo({\r\n              ...userInfo,\r\n              bookdate: v.day,\r\n              bookweek: v.week,\r\n              bookdateval: v.val\r\n            })\r\n            setDateShow(false)\r\n          }}>{v.week} {v.day}</View></View>)\r\n        }\r\n      </View>\r\n    </AtFloatLayout>\r\n  </View>)\r\n}\r\nexport default ProductBuy", "// extracted by mini-css-extract-plugin", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./productBuy.js\"\nvar config = {\"navigationBarTitleText\":\"下单支付\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/wxshop/productBuy', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AATA;AAAA;AAUA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAPA;AAAA;AAQA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;AAAA;AAcA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAHA;AAAA;AAKA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAHA;AAIA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAlEA;AAAA;AAAA;AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;AACA;;;;;;;;;;;ACtXA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
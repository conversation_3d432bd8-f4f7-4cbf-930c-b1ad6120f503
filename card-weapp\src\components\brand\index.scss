.noraml {
    width: 620px;
    height: 500px;
}
.list {
    width: 680px;
    height: 515px;
    margin: 0 auto 40px auto;
}

.listz {
    width: 680px;
    height: 615px;
    margin: 0 auto 40px auto;
}


.listl {
    width: 350px;
    height: 615px;
    margin: 10px;
    float: left;
}

.brandIndex {
    background-color: #fff;
    border-radius: 20px;
    position: relative;
    .images {
        width: 100%;
        border-radius: 20px 20px 0 0;
        max-height: 275px;
        min-height: 275px;
    }
    .images_l {
        width: 100%;
        border-radius: 20px 20px 0 0;
        max-height: 300px;
    }
    .images_lz {
        width: 100%;
        border-radius: 20px 20px 0 0;
        max-height: 380px;
    }
    .title {
        color: #313234;
        font-size: 35px;
        font-weight: bold;
        padding: 15px 20px;
    }
    .info {
        font-size: 26px;
        color:#777777;
        padding-left: 20px;
    }
    .top_tags {
        position: absolute;
        height: 40px;
        background-color: #ED8502;
        color: #fff;
        top:0;
        left: 0;
        border-radius: 20px 0;
        font-size: 20px;
        z-index: 1;
        text-align: center;
        line-height: 40px;
        padding: 10px;
    }
    .status {
        position: absolute;
        right: 20px;
    }
    .open {
        width: 130px;
        height: 120px;
        background-color: #0367D9;
        color: #fff;
        font-size: 20px;
        top: 42%;
        text-align: center;
        border-radius: 120px;
        padding-top: 10px;
        border: 5px solid #fff;
        z-index: 100;
        .num {
            font-weight: bold;
            font-size: 40px;
        }
    }
    .close {
        width: 80px;
        height: 80px;
        border-radius: 80px;
        text-align: center;
        border: 5px solid #fff;
        top:55%;
        line-height: 80px;
        background-color: #bfc1c3;
        color: #fff;
        font-size: 26px;
        font-weight: bold;
        line-height: 10px;
    }
}

.swiper {
    height: 500px;
    // width: 13rem;
    width: 100%;
}

.swiper-item-with-margin {
    margin-right: 30px;
    box-sizing: border-box;
    width: calc(100% - 30px) !important;
}

/* 支付宝小程序的样式优化 */
.swiper-alipay-style {
    position: relative;
}

.swiper-alipay-style::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.at-float-layout__container {
    max-height: 1100px;
    overflow: hidden;
}

.time_item {
    background-color: #8C8C8C;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: #8C8C8C;
    color: #fff;
    font-size: 25px;
    border-radius: 10px;
    margin:0 5px;
}

.time_month {
    width: 30px;
    height: 30px;
    font-size: 20px;
}

.tips_one {
    background-color: #8BC34A;
    font-size: 24px;
    padding: 8px;
    display: inline-block;
    width: 100px;
    border-radius: 10px;
    margin: 20px;
    color: #fff;
    text-align: center;
}

.tips_two {
    background-color: #FF9800;
    color: '#fff';
    font-size: 24px;
    padding: 8px;
    display: inline-block;
    width: 250px;
    border-radius: 10px;
    margin: 20px 5px;
    color: #fff;
    text-align: center;
}

.tips_three {
    background-color: #FF9800;
    color: '#fff';
    font-size: 24px;
    padding: 8px 10px;
    display: inline-block;
    border-radius: 10px;
    margin: 20px 8px;
    color: #fff;
    text-align: center;
}
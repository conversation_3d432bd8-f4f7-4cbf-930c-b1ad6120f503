(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/my/index"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/my/index.jsx":
/*!****************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/my/index.jsx ***!
  \****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _components_card_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/card/list */ "./src/components/card/list.jsx");
/* harmony import */ var _components_memu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/memu */ "./src/components/memu/index.jsx");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./index.scss */ "./src/pages/my/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__);



/*
 * @Author: 高超
 * @Date: 2021-11-27 11:01:05
 * @LastEditTime: 2022-08-03 18:05:51
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/my/index.jsx
 * love jiajia
 */











function Index(props) {
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    isOpened = _useState2[0],
    setIsOpened = _useState2[1];
  var login = Object(react_redux__WEBPACK_IMPORTED_MODULE_5__[/* useSelector */ "c"])(function (state) {
    return state.login;
  });
  var _useState3 = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(false),
    _useState4 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState3, 2),
    loginOk = _useState4[0],
    setLoginOk = _useState4[1];
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var userInfo;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          userInfo = _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].getDataSafe('userInfo');
          if (userInfo !== null) {
            if (userInfo.token !== null) setLoginOk(true);
          }
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), []);
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      style: {
        height: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].reTopH(6), "rpx"),
        backgroundColor: '#fff'
      }
    }), loginOk ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      className: "at-row my_avatr",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "at-col at-col-3",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
          src: _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].getDataSafe('userInfo').avatar ? _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].getDataSafe('userInfo').avatar : "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/index/head.png"),
          mode: "widthFix",
          className: "myhead"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "at-col at-col-9",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "nickname",
          children: _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].getDataSafe('userInfo').nickname ? _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].getDataSafe('userInfo').nickname : "已登录"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "nickinfo",
          children: "\u6B22\u8FCE\u4F7F\u7528\u5317\u4EAC\u98CE\u666F\u540D\u80DC\u5E74\u7968"
        })]
      })]
    }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      className: "at-row my_avatr",
      onClick: function onClick() {
        _utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].getUserInfo(function (res) {
          return setLoginOk(res);
        });
      },
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "at-col at-col-3",
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
          src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/index/head.png"),
          mode: "widthFix",
          className: "myhead"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
        className: "at-col at-col-9",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "nickname",
          children: "\u8BF7\u767B\u5F55"
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
          className: "nickinfo",
          children: "\u767B\u5F55\u67E5\u770B\u66F4\u591A\u5185\u5BB9"
        })]
      })]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtList */ "i"], {
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtListItem */ "j"], {
        title: "\u6211\u7684\u5361\u5305",
        arrow: "right",
        thumb: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/index/card.png"),
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
            url: '/pages/my/card'
          });
        }
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtListItem */ "j"], {
        title: "\u9884\u7EA6\u8BB0\u5F55",
        arrow: "right",
        thumb: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/index/book.png"),
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
            url: '/pages/my/book'
          });
        }
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtListItem */ "j"], {
        title: "\u5165\u56ED\u8BB0\u5F55",
        arrow: "right",
        thumb: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/index/plan.png"),
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
            url: '/pages/my/use'
          });
        }
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtListItem */ "j"], {
        title: "\u6211\u7684\u8BA2\u5355",
        arrow: "right",
        thumb: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/index/card.png"),
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
            url: '/pages/my/order'
          });
        }
      })]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      style: {
        height: "8px"
      }
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtList */ "i"], {
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtListItem */ "j"], {
        title: "\u6211\u7684\u6536\u85CF",
        arrow: "right",
        thumb: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/index/love.png"),
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
            url: '/pages/my/love'
          });
        }
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtListItem */ "j"], {
        title: "\u8054\u7CFB\u5BA2\u670D",
        arrow: "right",
        thumb: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].picUrl, "/index/help.png"),
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.makePhoneCall({
            phoneNumber: "4006091798"
          });
        }
      })]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["View"], {
      className: "cardList",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Text"], {
        className: "lastuse",
        children: "\u6700\u8FD1\u4F7F\u7528"
      }), login ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_components_card_list__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], {
        callback: function callback() {
          return setLoginOk(true);
        },
        max: 3
      }) : null]
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_7__[/* AtCurtain */ "c"], {
      isOpened: isOpened,
      onClose: function onClose() {
        setIsOpened(false);
      },
      children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__["Image"], {
        style: {
          width: "100%",
          borderRadius: "15rpx"
        },
        mode: "widthFix",
        src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"].ip, "/go.jpg")
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_12__["jsx"])(_components_memu__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], {
      now: 3
    })]
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Index);

/***/ }),

/***/ "./src/pages/my/index.jsx":
/*!********************************!*\
  !*** ./src/pages/my/index.jsx ***!
  \********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./index.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/my/index.jsx");


var config = {"navigationBarTitleText":"","navigationStyle":"custom","transparentTitle":"always","titlePenetrate":"YES"};


var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_index_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/my/index', {root:{cn:[]}}, config || {}))



/***/ })

},[["./src/pages/my/index.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=index.js.map
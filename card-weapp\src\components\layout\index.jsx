import { useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import { useDispatch } from 'react-redux'
import Taro from '@tarojs/taro';
import tools from '@/utils/tools'
function Index (props) {
  const dispatch = useDispatch();
  const [point, setPoint] = useState(false)
  const getPoint = res => {
    //console.log(res)
    const apiPoint = tools.txMap_to_bdMap(res.latitude, res.longitude)
    const myLocation = {
        wx: {
          latitude: res.latitude,
          longitude: res.longitude
        },
        api: {
          latitude: apiPoint.lat,
          longitude: apiPoint.lng
        }
      }
    tools.setData('mypoint', myLocation)
    setPoint(true)
  }

  const operLocation = async () => {
    tools.getLocation().then(res => {
      getPoint(res)
    }).catch(res => {
      getPoint({
        latitude: 39.90469,
        longitude: 116.40717
      })
    })
    // const _locationChangeFn = function (res) {
    //   console.log('location change', res)
    //  }
    //  Taro.onLocationChange(_locationChangeFn)
    // Taro.onLocationChange((res) => {
    //   console.log(res)
    // })
    // Taro.getLocation({
    //   type: 'gcj02',
    //   success: function (res) {
    //     getPoint(res)
    //   },
    //   fail: function (res) {
    //     Taro.showToast({
    //       title: '位置信息默认为北京市',
    //       icon: 'none',
    //       duration: 2000
    //     })
    //     getPoint({
    //       latitude: 39.90469,
    //       longitude: 116.40717
    //     })
    //   }
    // })
  }

  const login = async () => {
    const promise = new Promise( async (resolve, reject) => {
      let check = false
      try {
        if (!tools.getLogin()) {
          check = await tools.userLoginHide();
          resolve(check)
        } else {
          Taro.checkSession({
            success: async () => {
              resolve(true)
            },
            fail: async () => {
              check = await tools.userLoginHide();
              resolve(check)
            }
          })
        }
      } catch(e) {
        reject(false)
      }})
    return await promise.then(result => {
      return result
    }).catch((err) => {
      return false
    })
  }

  // const getCrad = async () => {
  //   const data = await tools.Api.myCardList()
  //   if (data.code === 200) {
  //     tools.setData('mycard', data.data)
  //   }
  //   return true
  // }

  useEffect( async () => {
   
    const loginStatus = await login()
    let myCard = true
    // if (tools.getDataSafe('mycard') === null) {
    //   myCard = await tools.getCardList()
    // }
    operLocation();
    if (!loginStatus) {
      Taro.showToast({
        title: '微信信息获取失败',
        icon: 'fail',
        duration: 2000,
      });
    } else {
      if (myCard === true && point === true)
      dispatch({ type: "login" ,data: true })
    }
  }, [ point ])
  return (
    <View>
      { props.children  }
    </View>
  )
}
export default Index;

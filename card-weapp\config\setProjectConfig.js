/*
 * @Author: 高超
 * @Date: 2021-10-30 10:51:29
 * @LastEditTime: 2022-07-20 10:13:04
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/config/setProjectConfig.js
 * love jiajia
 */
/**
 * 根据环境更新../project.config.json中的appid字段
 * @env NODE_ENV=development 将config/dev.js中的appid配置设置到../project.config.json中
 * @env NODE_ENV=production 将config/prod.js中的appid配置设置到../project.config.json中
 */
 const fs = require('fs');
 const path = require('path')
 const DEV_CONFIG = require('./dev');
 const PROD_CONFIG = require('./prod');
 
 const { readFileSync, writeFileSync } = fs;
 
 
 function updateProjectConfig (filePath, key) {
     const fileOption = { encoding: 'utf-8' }
     const fileContent = readFileSync(filePath, fileOption);
     let config = JSON.parse(fileContent.toString());
     if (process.env.NODE_ENV==='development') {
         config[key] = DEV_CONFIG[key];
         console.log(`[DEV] ${key} =  ${config[key]} `);
     } else {
         config[key] = PROD_CONFIG[key];
         console.log(`[PROD] ${key} =  ${config[key]} `);
     }
     let newStr = JSON.stringify(config,null,2)
     writeFileSync(filePath, newStr, fileOption);
 }
 
 updateProjectConfig(path.join(__dirname, '../project.config.json'), "appid")
 updateProjectConfig(path.join(__dirname, '../src/utils/api.config.json'), "baseUrl")
 updateProjectConfig(path.join(__dirname, '../src/utils/api.config.json'), "zhbaseUrl")
 updateProjectConfig(path.join(__dirname, '../src/utils/api.config.json'), "zhnodeUrl")
 
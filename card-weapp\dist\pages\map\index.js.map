{"version": 3, "file": "pages/map/index.js", "sources": ["webpack:///./src/pages/map/index.jsx", "webpack:///./src/pages/map/index.jsx?48c9", "webpack:///./src/pages/map/index.scss"], "sourcesContent": ["/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2021-12-12 19:19:41\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/map/index.jsx\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport Taro from '@tarojs/taro';\r\nimport { useSelector } from 'react-redux'\r\nimport { View, CoverView, Map} from '@tarojs/components'\r\nimport Memu from '@/components/memu'\r\nimport tools from '@/utils/tools'\r\nimport \"./index.scss\"\r\n\r\nconst MapView = () => {\r\n    const login = useSelector(state => state.login)\r\n    const [mapData, setMapData] = useState([])\r\n    useEffect( async () => {\r\n      if (login) {\r\n        const data = await tools.Api.brandMap()\r\n        setMapData(data.data || [])\r\n      }\r\n    }, [login])\r\n    // const normalCallout = {\r\n    //     id: 1,\r\n    //     latitude: 23.098994,\r\n    //     longitude: 113.32252,\r\n    //     iconPath:`${tools.picUrl}/memu/m2.png`,\r\n    //     height: 40,\r\n    //     width: 40,\r\n    //     callout: {\r\n    //         content: '北京故宫',\r\n    //         color: '#ff0000',\r\n    //         fontSize: 14,\r\n    //         borderWidth: 2,\r\n    //         borderRadius: 10,\r\n    //         borderColor: 'blue',\r\n    //         bgColor: '#fff',\r\n    //         padding: 5,\r\n    //         display: 'ALWAYS',\r\n    //         textAlign: 'center',\r\n    //       }\r\n    //   }\r\n      \r\n    //   const customCallout1 = {\r\n    //     id: 2,\r\n    //     latitude: 23.097994,\r\n    //     longitude: 113.32352,\r\n    //     iconPath:`${tools.picUrl}/memu/m2.png`,\r\n    //     height: 40,\r\n    //     width: 40,\r\n    //     customCallout: {\r\n    //       anchorY: 0,\r\n    //       anchorX: 0,\r\n    //       display: 'ALWAYS',\r\n    //     },\r\n    //   }\r\n      \r\n    //   const customCallout2 = {\r\n    //     id: 3,\r\n    //     latitude: 23.096994,\r\n    //     longitude: 113.32452,\r\n    //     iconPath:`${tools.picUrl}/memu/m2.png`,\r\n    //     height: 40,\r\n    //     width: 40,\r\n    //     customCallout: {\r\n    //       anchorY: 0,\r\n    //       anchorX: 0,\r\n    //       display: 'ALWAYS',\r\n    //     },\r\n    //   }\r\n      \r\n    //   const customCallout3 = {\r\n    //     id: 4,\r\n    //     latitude: 23.095994,\r\n    //     longitude: 113.32552,\r\n    //     iconPath:`${tools.picUrl}/memu/m2.png`,\r\n    //     height: 40,\r\n    //     width: 40,\r\n    //     customCallout: {\r\n    //       anchorY: 0,\r\n    //       anchorX: 0,\r\n    //       display: 'ALWAYS',\r\n    //     },\r\n    //   }\r\n      \r\n    //   const customMarkers = [\r\n    //     customCallout1,\r\n    //     customCallout2,\r\n    //     customCallout3,\r\n    //   ]\r\n      \r\n    //   const mapMarkers = [\r\n    //     normalCallout,\r\n    //     ...customMarkers\r\n    //   ]\r\n\r\n      const goBrand = id => {\r\n        let code = null\r\n        mapData.forEach(val => {\r\n          if (val.id === id) {\r\n            code = val.code\r\n          }\r\n        })\r\n        if(code !== null) {\r\n          Taro.navigateTo({\r\n            url : `/pages/brand/index?v=${code}`\r\n          })\r\n        }\r\n      }\r\n\r\n      return (\r\n        <Map\r\n          setting={{}}\r\n          scale={8}\r\n          show-compass\r\n          enable-overlooking\r\n          markers={mapData}\r\n          latitude={39.925218}\r\n          longitude={116.404425}\r\n          style={{ height: '100vh', width: '100vw' }}\r\n          onCalloutTap={(v) => {\r\n            console.warn(\"v -------- v\", v)\r\n            goBrand(v.markerId)\r\n          }}\r\n          onMarkerTap={(v) => {\r\n            console.warn(\"v -------- v\", v.markerId)\r\n            goBrand(v.markerId)\r\n          }}\r\n        >\r\n          <CoverView slot='callout'>\r\n            {\r\n              mapData.map(item => (\r\n                /** 自定义样式的 callout */\r\n                <CoverView \r\n                    marker-id={item.code} \r\n                    key={item.code}\r\n                    height={10}\r\n                    iconPath={`${tools.picUrl}/memu/love.png`}\r\n                >\r\n                </CoverView>\r\n              ))\r\n            }\r\n          </CoverView>\r\n          <Memu now={1}/>\r\n        </Map>\r\n      )\r\n}\r\nexport default MapView ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.jsx\"\nvar config = {\"navigationBarTitleText\":\"景区地图\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/map/index', {root:{cn:[]}}, config || {}))\n\n", "// extracted by mini-css-extract-plugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAEA;AAAA;AACA;AAGA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;ACtJA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA;;;;A", "sourceRoot": ""}
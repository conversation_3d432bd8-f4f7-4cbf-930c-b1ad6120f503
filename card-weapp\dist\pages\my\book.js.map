{"version": 3, "file": "pages/my/book.js", "sources": ["webpack:///./node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js", "webpack:///./src/pages/my/book.jsx", "webpack:///./src/components/my/book.jsx", "webpack:///./src/pages/my/book.jsx?9ed4"], "sourcesContent": ["import unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nexport default function _createForOfIteratorHelper(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (!it) {\n    if (Array.isArray(o) || (it = unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n      if (it) o = it;\n      var i = 0;\n      var F = function F() {};\n      return {\n        s: F,\n        n: function n() {\n          if (i >= o.length) return {\n            done: true\n          };\n          return {\n            done: false,\n            value: o[i++]\n          };\n        },\n        e: function e(_e) {\n          throw _e;\n        },\n        f: F\n      };\n    }\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var normalCompletion = true,\n    didErr = false,\n    err;\n  return {\n    s: function s() {\n      it = it.call(o);\n    },\n    n: function n() {\n      var step = it.next();\n      normalCompletion = step.done;\n      return step;\n    },\n    e: function e(_e2) {\n      didErr = true;\n      err = _e2;\n    },\n    f: function f() {\n      try {\n        if (!normalCompletion && it[\"return\"] != null) it[\"return\"]();\n      } finally {\n        if (didErr) throw err;\n      }\n    }\n  };\n}", "import { Component, useState, useEffect } from 'react'\r\nimport Taro, { useReachBottom } from '@tarojs/taro';\r\nimport { View } from '@tarojs/components'\r\nimport tools from '@/utils/tools'\r\nimport BookItem from '@/components/my/book';\r\nimport { AtList, AtTabs, AtIcon } from \"taro-ui\"\r\nimport './index.scss'\r\n\r\nfunction Index (props) { \r\n  const query = tools.getQuery()\r\n  const [init, setInit] = useState([])\r\n  const [initPage, setInitPage] = useState()\r\n  const [page, setPage] = useState(1)\r\n  const [total, setTotal] = useState(-1)\r\n  const [tabs, setTabs] = useState([])\r\n  const tabList = [{ title: '景区预约' }, { title: '预约记录' }]\r\n  useEffect( async () => {\r\n    const code = Boolean(query.data.code) ? query.data.code : 'ALL'\r\n    Taro.showLoading({\r\n      mask: true,\r\n      title: '读取中'\r\n    })\r\n    const data = await tools.Api.myBookList({\r\n      code,\r\n      page : {\r\n          current: page,\r\n          pageSize : 20\r\n      }\r\n    })\r\n    Taro.hideLoading()\r\n    if (data.code === 200) {\r\n      for (const v of data.data.data) {\r\n        if (tabs.includes(v.book_time_tab) === false) {\r\n          tabs.push(v.book_time_tab)\r\n          v.tabTop = true\r\n        } else {\r\n          v.tabTop = false\r\n        }\r\n      }\r\n      setInit([\r\n        ...init,\r\n        ...data.data.data\r\n      ])\r\n      setInitPage(data.data.page)\r\n      setTotal(data.data.page.total)\r\n    }\r\n  }, [ page ])\r\n\r\n  useReachBottom(() => {\r\n    const nextPage = initPage.current + 1\r\n    if (nextPage <= initPage.totalPage) {\r\n      setPage(nextPage)\r\n    } else {\r\n      Taro.showToast({\r\n        title: '暂无更多内容',\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n    }\r\n  })\r\n\r\n  return (\r\n    <View className='index'>\r\n      <AtTabs current={1} tabList={tabList} onClick={(v) => {\r\n        if (v === 0) {\r\n          Taro.navigateTo({\r\n            url: '/pages/sreach/book'\r\n          })\r\n        }\r\n      }}></AtTabs>\r\n      {(total === 0) ? <View style={{textAlign: 'center', marginTop: \"60px\", color: \"#333\", fontSize: \"16px\"}}>\r\n          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>\r\n          <View style={{marginTop: \"10px\" , color: \"#848181\"}}>暂无预约记录</View>\r\n        </View> :\r\n        <View>\r\n          {init.map(v => {\r\n            if (v.tabTop) {\r\n              return <View>\r\n                <View style={{backgroundColor: '#ffd65b', fontSize: '14px', color: '#af6c09', padding: '4px 7px', width: '80px', textAlign: 'center', margin: '8px', borderRadius: '20px', marginTop: '13px'}}>\r\n                  {v.book_time_tab}\r\n                </View>\r\n                <BookItem data={v}/>\r\n              </View>\r\n            } else {\r\n              return <BookItem data={v}/>\r\n            }\r\n          })}   \r\n        </View>\r\n        }\r\n      <View style={{height: '30px'}}></View>  \r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "/*\r\n * @Author: 高超\r\n * @Date: 2021-12-17 22:35:13\r\n * @LastEditTime: 2022-08-11 20:02:56\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/my/book.jsx\r\n * love jiajia\r\n */\r\nimport { Component,useState, useEffect, Fragment } from 'react'\r\nimport Taro from '@tarojs/taro';\r\nimport { View, Text, Button, Image } from '@tarojs/components'\r\nimport tools from '@/utils/tools'\r\nimport moment from \"moment\"\r\nimport './index.scss'\r\n\r\nconst Item = props => {\r\n    const { data } = props\r\n    const [show, setShow] = useState(true)\r\n    const csscolor = data.enable ? {} : {color: '#ccc'}\r\n    const weekDay = [\"周日\",\"周一\",\"周二\",\"周三\",\"周四\",\"周五\",\"周六\"];\r\n    return (\r\n        <Fragment>\r\n        {show ? <View className='bookItem'>\r\n        <View className='at-row cardNum'>\r\n            <View className='at-col at-col-6' style={csscolor}>\r\n            {data.card_name}\r\n            </View>\r\n            <View className='at-col at-col-5' style={{textAlign: 'right'}}>\r\n                <Text style={csscolor}>No.{data.user_card_code}</Text>\r\n            </View>\r\n        </View>\r\n        <View>\r\n        <View className='at-row' style={{ padding: '0 14px', width: '90%', marginTop: '14px'}}>\r\n            <View className='at-col at-col-3' style={{textAlign: 'center'}}>\r\n            <View className='week'  style={csscolor}>{weekDay[moment(data.book_time_unix * 1000).weekday()]}</View>\r\n            <View className='day'  style={csscolor}>{data.book_time_show}</View>\r\n            </View>\r\n            <View className='at-col at-col-2' style={{margin: '0 15px'}}>\r\n                <View className='linetext'  style={csscolor}>游玩</View>\r\n                <Image src={`${tools.picUrl}/bind/${data.enable ? 'right' : 'right_cc'}.png`} className='line' mode='widthFix' />\r\n            </View>\r\n            <View className='at-col at-col-8 brand'>\r\n            <View  style={csscolor}>{data.brand_name}</View>\r\n            <View className='address'>\r\n            <Text style={csscolor}>{tools.getSamll(`${data.province}${data.city}${data.address}`, 10)}</Text></View>\r\n            </View>\r\n        </View>\r\n        <View className='at-row' style={{ padding: '0 14px',width: '90%', marginBottom: '14px', marginTop: '6px', color:'#8e8c8c'}}>\r\n            <View className='at-col at-col-3' style={{textAlign: 'center'}}>\r\n            <Text style={csscolor}>预约人:  {data.real_name}</Text>\r\n            </View>\r\n            <View className='at-col at-col-3' style={{textAlign: 'left'}}></View>\r\n            <View className='at-col at-col-6' style={{textAlign: 'left', paddingLeft: '5px'}}>\r\n                <Text style={csscolor}>{data.add_time}</Text>\r\n            </View>\r\n        </View>\r\n        {data.enable ? <View className='at-row oper'>\r\n            <View className='at-col at-col-4' style={{textAlign: 'center', borderRight: '1px solid #e2e2e2'}} onClick={ () => {\r\n                Taro.navigateTo({\r\n                    url : `/pages/qr/index?code=${data.user_card_code}`\r\n                })\r\n            }}>\r\n                扫码入园\r\n            </View>\r\n            <View className='at-col at-col-4' style={{textAlign: 'center', borderRight: '1px solid #e2e2e2'}} onClick={() => {\r\n                Taro.openLocation({\r\n                    latitude: parseFloat(data.latitude),\r\n                    longitude: parseFloat(data.longitude),\r\n                    scale: 15\r\n                  })\r\n            }}>\r\n                位置导航\r\n            </View>\r\n            <View className='at-col at-col-4' style={{textAlign: 'center'}} onClick={() => {\r\n                Taro.showModal({\r\n                    title: `请确认`,\r\n                    content: `取消此预约？`,\r\n                    success:  async (res) => {\r\n                      if (res.confirm) {\r\n                        const oper = await tools.Api.removeBook({id : data.id})\r\n                        if (oper.data === true) {\r\n                            Taro.showToast({\r\n                                title: '预约已取消',\r\n                                icon: 'none',\r\n                                duration: 2000\r\n                              })\r\n                            setShow(false)\r\n                        }   \r\n                        \r\n                      }\r\n                    }\r\n                  })\r\n            }}>\r\n                取消预约\r\n            </View>\r\n        </View> : <View><View style={{height: '10px'}}></View></View>}\r\n        \r\n        </View>\r\n        </View> : null}\r\n        </Fragment>\r\n        \r\n    )\r\n}\r\nexport default Item;", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./book.jsx\"\nvar config = {\"navigationBarTitleText\":\"预约记录\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/my/book', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnDA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AANA;AAOA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAIA;AACA;;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
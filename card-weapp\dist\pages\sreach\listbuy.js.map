{"version": 3, "file": "pages/sreach/listbuy.js", "sources": ["webpack:///./src/pages/sreach/listbuy.jsx", "webpack:///./src/pages/sreach/listbuy.jsx?6843"], "sourcesContent": ["/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2022-07-21 23:24:38\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/listbuy.jsx\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport { View } from '@tarojs/components'\r\nimport { useSelector } from 'react-redux'\r\nimport Taro, { useReachBottom } from '@tarojs/taro'\r\nimport Memu from '@/components/memu'\r\nimport None from '@/components/tips/none'\r\nimport SreachTools from './bar'\r\nimport Item from '@/components/brand/itembuy'\r\nimport tools from '@/utils/tools'\r\nimport \"./index.scss\"\r\n\r\nconst SreachIndex = (props) => {\r\n    const query = tools.getQuery()\r\n    const login = useSelector(state => state.login)\r\n    const point = tools.getDataSafe('mypoint')\r\n    const [init, setInit] = useState(null)\r\n    const [config, setConfig] = useState(false)\r\n    const [page , setPage] = useState(1)\r\n    const [params , setParams] = useState({\r\n      order: \"km\",\r\n      ordertype: \"asc\",\r\n      range: {\r\n        latitude: point.api.latitude,\r\n        longitude: point.api.longitude,\r\n        val: 5000\r\n      }\r\n    })\r\n    const [brandList , setBrandList] = useState([])  \r\n    const [word, setWord] = useState(query.data.v)\r\n    const [loaded, setLoaded] = useState(false)\r\n\r\n\r\n    useEffect( async () => {\r\n        if (login) {\r\n          \r\n          const selData = await tools.Api.brandBuy({\r\n            latitude: point.api.latitude,\r\n            longitude: point.api.longitude\r\n          })\r\n          if (selData.code === 200) {\r\n            setConfig(selData.data)\r\n          }\r\n        }\r\n    }, [login])\r\n\r\n    useEffect( async () => {\r\n        setLoaded(false)\r\n        console.log(params)\r\n        const userInfo = tools.getDataSafe('userInfo');\r\n        Taro.showLoading({\r\n          mask: true,\r\n          title: '请稍等'\r\n        })\r\n        if (login && config) {\r\n          // const apiParams = {\r\n          //   line_id: tools.line_code,\r\n          //   order : \"km\",\r\n          //   point : true,\r\n          //   page : {\r\n          //       current: page,\r\n          //       pageSize : 20\r\n          //   },\r\n          //   ...params\r\n          // }\r\n          const apiParams = {\r\n            admin: \"buyer\",\r\n            showlist: 1,\r\n            from: \"fe\",\r\n            page: page - 1,\r\n            enter_user_id: 0,\r\n            agent_id: 1,\r\n            pageSize: 20,\r\n            currentPage: page,\r\n            buyer: userInfo.mem_id,\r\n            ...params\r\n          }\r\n          if (Boolean(word)) {\r\n            apiParams.keywords = word\r\n          }\r\n          const data = await tools.Api.brandBuyList(apiParams)\r\n          if (data.code === 0) {\r\n            setInit(data.pagination)\r\n            setBrandList([\r\n                ...brandList,\r\n                ...data.list\r\n            ])\r\n          }\r\n          setLoaded(true)\r\n          Taro.hideLoading()\r\n        }\r\n    }, [login, page, params, word, config])\r\n\r\n    useReachBottom(() => {\r\n        const nextPage = init.current + 1\r\n        if (nextPage <= init.totalPage) {\r\n          setPage(nextPage)\r\n        } else {\r\n          Taro.showToast({\r\n            title: '暂无更多内容',\r\n            icon: 'none',\r\n            duration: 2000\r\n          })\r\n        }\r\n    })\r\n\r\n    const configParams = data => {\r\n      let formatData = {}\r\n      if (Boolean(data.orderVal)) {\r\n        formatData.orderVal = data.orderVal[0]\r\n      }\r\n      if (Boolean(data.range)) {\r\n        formatData.range = data.range[0]\r\n      }\r\n      if (Boolean(data.order)) {\r\n        formatData =  {\r\n          ...formatData,\r\n          ...data.order\r\n        }\r\n      }\r\n      setParams({\r\n        ...params,\r\n        ...formatData\r\n      })\r\n    }\r\n\r\n    return (\r\n        <View>\r\n         {config ? <SreachTools config={config} callBack={data => {\r\n            setBrandList([])\r\n            configParams(data)\r\n            setPage(1)\r\n          }} setkey = {(v) => {\r\n            setBrandList([])\r\n            setWord(v)\r\n            setPage(1)\r\n          }} init={word}/> : null}\r\n          <View>\r\n            {\r\n                (brandList.length > 0) ?  brandList.map(v => <Item className=\"list\" data={v} cname=\"images_l\"/>) : <None loaded={loaded}/>\r\n            }\r\n          </View>\r\n          <Memu now={10} />\r\n        </View>\r\n    )\r\n}\r\nexport default SreachIndex ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./listbuy.jsx\"\nvar config = {\"navigationBarTitleText\":\"年票福利\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/sreach/listbuy', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AARA;AAAA;AASA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AAHA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AAIA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;ACzJA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
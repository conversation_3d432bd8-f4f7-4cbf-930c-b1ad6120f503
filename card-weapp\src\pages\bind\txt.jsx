import { Component } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { AtIcon } from "taro-ui"

function Index (props) { 
  return (
    <View className='index' style={{padding: "20px", color: "#666"}}>
        <View style={{textAlign: "center", fontWeight: 'bold', marginBottom: "15px"}}>《用户授权协议》</View>
        <View style={{marginBottom: '15px'}}>《用户授权协议》(以下简称“本协议”)是北京风景名胜协会（以下简称“协会”或“我们”）与用户（以下简称“您”）所订立的有效合约。请您先仔细阅读本协议内容，尤其是字体加粗部分。如您对本协议内容或页面提示信息有疑问，请勿进行下一步操作。您可通过协会在线客服或官方热线4006071798进行咨询，以便我们为您解释和说明。如您通过页面点击或我们认可的其他方式确认本协议即表示您已同意本协议。
</View>
        <View style={{marginBottom: '15px'}}>1、为了便于您使用第三方的服务，您同意协会将您的姓名、证件号、手机号及页面提示的相关信息传递给第三方。页面提示上会展示具体授权对象以及授权信息类型，您的信息将通过加密通道传递给第三方。协会会要求第三方严格遵守相关法律法规与监管要求，依法使用您的信息，并应对您的信息保密。点击授权之后，授权关系长期有效，直至您主动解除。为了更好的保护您的信息安全，我们采用了信息查询令牌技术。授权有效期内，当您使用被授权方服务时，将激活令牌，被授权方仅在令牌激活期内方可查询您的信息。激活期届满后，令牌将暂时失效，直至您再次使用被授权方服务时被激活。</View>
        <View style={{marginBottom: '15px', fontWeight: "bold"}}>2、协会是中立的社会团体，上述第三方服务由该第三方独立运营并独立承担全部责任。因第三方服务或其使用您的信息而产生的纠纷，或第三方服务违反相关法律法规或协议约定，或您在使用第三方服务过程中遭受损失的，请您和第三方协商解决。</View>
        <View style={{marginBottom: '15px'}}>3、如我们对本协议进行变更，我们将通过公告或客户端消息等方式予以通知，该等变更自通知载明的生效时间开始生效。若您无法同意变更修改后的协议内容，您有权停止使用相关服务；双方协商一致的，也可另行变更相关服务和对应协议内容。</View>
        <View style={{marginBottom: '15px'}}>4、本协议之效力、解释、变更、执行与争议解决均适用中华人民共和国法律。因本协议产生的争议，均应依照中华人民共和国法律予以处理，并由被告住所地人民法院管辖。</View>
    </View>
  )
}
export default Index;

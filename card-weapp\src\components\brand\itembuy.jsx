/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2022-07-23 00:02:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/brand/itembuy.jsx
 */
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import tools from '@/utils/tools'
import "./index.scss"

const BrandBuyItem = (props) => {
    const { data } = props
    const css = (props.className) ? props.className : 'noraml'
    return (data.good_info !== null && data.id !== "817") ? (
        <View className={`brandIndex ${css}`} onClick={() => {
            Taro.navigateTo({
                url : `/pages/brand/buy?v=${data.id}`
            })
        }}>
            
            <View className="top_tags" style={{ backgroundColor : '#ED8502'}}>持年票立减<Text style={{fontWeight: 'bold' , fontSize: '14px', color: '#FFEB3B'}}> {parseInt(parseFloat(data.good_info.show_price) - parseFloat(data.good_info.share_price), 10)} </Text>元</View>
            <View className="status open" style={{backgroundColor: '#FF9800'}}>
                <View>
                    <Text>仅售</Text>
                    <View className="num">{tools.getPrice(data.good_info.share_price)}</View>
                    <Text>元</Text>
                </View>
            </View>
           <View style={{position: 'relative', background: `url(${data.logo}?x-oss-process=image/blur,r_30,s_30)`, textAlign: 'center', backgroundSize: '100% 100%', borderTopLeftRadius: '15px', borderTopRightRadius: '8px'}}>
            <Image src={`${data.logo}`} className={props.cname} mode="heightFix"/>
            {
                (data.website_url !== '') ? <View style={{position:'absolute', bottom: '0px', background: '#0000007a', padding: '5px 0', color:'#fff', fontSize: '12px', width: '100%', zIndex: 1, textIndent: '5px', textAlign: 'left'}}>{data.website_url}</View> : null
            }
            
           </View>
           
           <View className="title">
               {data.name}
            </View>
           <View className="info">{data.province}{data.city}{data.address} 距{data.km}公里</View>
           <View style={{paddingLeft: '5px'}}>
            {
                data.good_info.day.map( (v, index) => {
                    return (index <= 3) ? <View className='tips_three'>{v}</View> : null
                } )
            }
           </View>
        </View>
    ) : null
}
export default BrandBuyItem 
import { Component, useEffect, useState } from 'react'
import { View, Image } from '@tarojs/components'
import tools from '@/utils/tools'
import QRCode from 'qrcode'

const Qr = (props) => {
    const { txt , size } = props;
    const [url, setUrl] = useState('')
    const qrSize = Boolean(size) ? size : 530
    useEffect( async () => {
      const opts = {
        errorCorrectionLevel: 'H',
        type: 'image/jpeg',
        quality: 1,
        margin: 1,
        width: qrSize,
        heigth: qrSize
      }
      console.log("reload")
      QRCode.toDataURL(JSON.stringify(txt), opts)
      .then(url => {
        setUrl(url)
      }).catch(err => {
        //console.error(err)
      })
    }, [txt])
    return (
        <View style={{textAlign:"center", position: "relative"}}>
          <Image src={`${tools.picUrl}/bind/qrlogo.png`} mode="widthFix" style={{width:"65rpx", height: "65rpx", position: "absolute", top: "42%", left: "46%"}} />
          <Image src={url} mode="widthFix" style={{width: `${qrSize}rpx`, height: `${qrSize}r600px`}}/>
        </View>
    )
}
export default Qr 
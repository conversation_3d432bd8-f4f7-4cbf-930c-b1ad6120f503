/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-09-07 13:34:07
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /dfx-umi/Users/<USER>/dfx-video/src/components/ele/price.js
 */
import { View } from '@tarojs/components';
import Min from '@/components/ele/min';
import tools from '@/utils/tools';
const Price = (props) => {
    const {buy, show} = props;
    return (
        <View style={{marginTop: '8rpx'}}>
            <Text className='priceV3'>
                <Text style={{fontSize: '20rpx'}}>¥</Text>
                    {tools.getPrice(buy)}
                </Text>
            <Text style={{color:'#A3A7A6', fontSize: '25rpx', marginLeft: '13rpx', textDecoration: 'line-through'}}>¥{tools.getPrice(show)}</Text>
            {(props.coupon !== 0) ? <Min txt="劵" cash={props.coupon}/>  : null}
          
        </View>
    )
}
Price.options = {
    addGlobalClass: true
  }
  Price.defaultProps = {
    buy : '--',
    show: '--',
    coupon: 0
}
export default Price;
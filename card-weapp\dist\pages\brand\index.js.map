{"version": 3, "file": "pages/brand/index.js", "sources": ["webpack:///./src/pages/brand/index.jsx", "webpack:///./src/components/calendar/index.jsx", "webpack:///./src/components/calendar/index.scss", "webpack:///./src/components/memu/brand.jsx", "webpack:///./src/pages/brand/index.jsx?14d1"], "sourcesContent": ["import { useState, useEffect } from 'react'\r\nimport Taro, { usePageScroll, useShareAppMessage } from '@tarojs/taro';\r\nimport { useSelector } from 'react-redux'\r\nimport { View, Text, Image, Swiper, SwiperItem, Video, RichText } from '@tarojs/components'\r\nimport { AtIcon , AtFloatLayout, AtNoticebar} from \"taro-ui\"\r\nimport Calendar from \"@/components/calendar\";\r\nimport CardItem from \"@/components/card\";\r\nimport Skeleton from 'taro-skeleton'\r\nimport Top from '@/components/memu/brand'\r\nimport tools from '@/utils/tools'\r\nimport moment from 'moment';\r\nimport './index.scss'\r\n\r\nfunction Index (props) { \r\n  const query = tools.getQuery()\r\n  const login = useSelector(state => state.login)\r\n  const [loading, setLoading] = useState(true)\r\n  const [init, setInit] = useState(null)\r\n  const [auto, setAuto] = useState(true)\r\n  const [love, setLove] = useState(false)\r\n  const [bookShow, setBookShow] = useState(false)\r\n  const [changeCard, setChangeCard] = useState(false)\r\n  const [card, setCard] = useState({\r\n    num: 0,\r\n    good_id: '--',\r\n    list: [],\r\n    name: '--',\r\n  })\r\n  const [dateLine, setDateLine] = useState({\r\n    start: \"2022-01-01\",\r\n    end: \"2022-02-01\",\r\n    list: []\r\n  })\r\n  const [weather, setWeather] = useState(\"\")\r\n  const brandCode = query.data.v\r\n  useEffect( async () => {\r\n    if (login && Boolean(brandCode)) {\r\n      const data = await tools.Api.brandInfo({code: brandCode})\r\n      if (data.code === 200) {\r\n        const cardList = await tools.getCardList({\r\n          list: data.data.brand.templet_list_enable\r\n        })\r\n        if (cardList.enable_card_num > 0) {\r\n          const enableCard = cardList.card_list.filter(v => v.enable === true)\r\n          setCard({\r\n            num : enableCard.length,\r\n            good_id: enableCard[0].good_id,\r\n            list: enableCard\r\n          })\r\n        }\r\n        console.warn(\"--------- data.data --------\", data.data)\r\n        setInit(data.data)\r\n        setLove(data.data.brand.collect)\r\n        setLoading(false)\r\n        getWeather(data.data.brand.area)\r\n      }\r\n    }\r\n  }, [login])\r\n\r\n  const getWeather = (area) => {\r\n    let url = \"\"\r\n    if (area.length > 2) {\r\n      url = `https://wis.qq.com/weather/common?source=pc&weather_type=observe|air&province=${area[0]}&city=${area[1]}&county=${area[2]}`\r\n    } else {\r\n      url = `https://wis.qq.com/weather/common?source=pc&weather_type=observe|air&province=${area[0]}&city=${area[0]}&county=${area[1]}`\r\n    }\r\n    Taro.request({\r\n      url: url,\r\n      method: \"get\",\r\n    }).then((res) => {\r\n      const data = res.data.data\r\n      // const weather_text = `${data.observe.weather} / ${data.observe.degree}° / 湿度 ${data.observe.humidity}% / ${data.observe.wind_direction_name} ${data.observe.wind_power} 级 / 空气质量 ${data.air.aqi_name}`\r\n      const weather_text = `${data.observe.weather} / ${data.observe.degree}° / ${data.observe.wind_direction_name} ${data.observe.wind_power} 级`\r\n      setWeather(weather_text)\r\n    })\r\n  }\r\n  \r\n  useShareAppMessage(() => {\r\n    return {\r\n      title: init.brand.brand_name,\r\n      path: `/pages/brand/index?v=${brandCode}`,\r\n      imageUrl: `${init.brand.image[0].thumbUrl}?x-oss-process=image/resize,m_fill,h_400,w_400`\r\n    }\r\n  })\r\n\r\n  const operBook = async () => {\r\n    if (card.num > 0) {\r\n      Taro.showLoading({\r\n        mask: true,\r\n        title: '读取中'\r\n      })\r\n      const data = await tools.Api.brandBookList({\r\n        brand_code : brandCode,\r\n        good_id : card.good_id,\r\n        today : init.book.today_time\r\n      })\r\n      Taro.hideLoading()\r\n      if (data.code === 200) {\r\n        if (data.data.list.length > 0) {\r\n          setDateLine({\r\n            start: data.data.list[0].val,\r\n            end: data.data.list[data.data.list.length - 1].val,\r\n            list: data.data.list\r\n          })\r\n          setBookShow(true)\r\n        }\r\n      } else {\r\n        Taro.showToast({\r\n          title: data.message,\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n      }\r\n    } else {\r\n      Taro.navigateTo({\r\n        url: '/pages/bind/index'\r\n      })\r\n    }\r\n  }\r\n\r\n  const showStatus = txt => {\r\n    if (txt.includes('|')) {\r\n      return txt.split('|')\r\n    }\r\n    return [txt]\r\n  }\r\n\r\n  const showTime = time => {\r\n    const newTime = []\r\n    time.forEach(v => {\r\n      newTime.push(v.replace(':00',''))\r\n    })\r\n    return newTime.join('~')\r\n  }\r\n\r\n  return (\r\n    <View>\r\n       {(init === null && Boolean(brandCode)) ?  <Skeleton title row={30} loading={loading}></Skeleton> :\r\n    <View>\r\n      <Top love={love} oper = {async () => {\r\n        await tools.Api.brandCollect({\r\n          brand_code: brandCode,\r\n          action: (love === false) ? 'ADD' : 'DEL'\r\n        })\r\n        setLove(!love)\r\n      }}/>\r\n      <View className=\"brand_top_pic\">\r\n        <Swiper\r\n          className='brand_swiper'\r\n          indicatorColor='#999'\r\n          indicatorActiveColor='#fff'\r\n          circular\r\n          indicatorDots\r\n          autoplay={auto}\r\n        >\r\n          {init.brand.video !== null && init.brand.video.map(v => <SwiperItem>\r\n            <Video\r\n              src={v.url}\r\n              controls={true}\r\n              autoplay={false}\r\n              showProgress={true}\r\n              poster={''}\r\n              initialTime='0'\r\n              enablePlayGesture\r\n              autoPauseIfOpenNative\r\n              muted={false}\r\n              className= \"images\"\r\n              objectFit='contain'\r\n              onPlay={() => {\r\n                setAuto(false)\r\n              }}\r\n              onEnded={() => {\r\n                setAuto(true)\r\n              }}\r\n            />\r\n          </SwiperItem>)}\r\n          {init.brand.static_list.map(v => <SwiperItem>\r\n            <Image src={v.url} mode=\"heightfix\" className=\"images\"/>\r\n          </SwiperItem>)}\r\n        </Swiper>\r\n      </View>\r\n      {\r\n        (init.brand.brand_note !== 'none' && init.brand.brand_note.length > 4) ? <AtNoticebar>{init.brand.brand_note}</AtNoticebar> : null\r\n      }\r\n     \r\n      <View className=\"brand_content\">\r\n        <View className='at-row' style={{marginBottom: '20px'}}>\r\n          <View className='at-col at-col-10'>\r\n            <View className=\"title\">{init.brand.brand_name}</View>\r\n            <View className=\"memo\">{init.brand.intro}</View>\r\n          </View>\r\n          <View className='at-col at-col-2'>\r\n            {init.limit.is_open_config.is_open ? <View className=\"open\">\r\n                <View style={{marginTop: '5px'}}>\r\n                    {\r\n                      showStatus(init.limit.is_open_config.is_open_tips).map(v => <View className=\"num\">{v}</View>)\r\n                    }\r\n                </View>\r\n            </View> : <View className=\"open close\">\r\n                <View style={{marginTop: '5px'}}>\r\n                    {\r\n                      showStatus(init.limit.is_open_config.is_open_tips).map(v => <View className=\"num\">{v}</View>)\r\n                    }\r\n                </View>\r\n            </View>}\r\n          </View>\r\n        </View>\r\n        \r\n       \r\n        <View className='at-row list_show' onClick={() => {\r\n          Taro.makePhoneCall({\r\n            phoneNumber: init.brand.tel\r\n          })\r\n        }}>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/time.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text'>\r\n            营业时间 {showTime(init.brand.jijie.time)}\r\n          </View>\r\n          <View className='at-col at-col-1'>\r\n            <AtIcon value='phone' size='18' color='#afafaf'></AtIcon>\r\n          </View>\r\n        </View>\r\n\r\n        <View className='at-row list_show' onClick={() => {\r\n          Taro.openLocation({\r\n            latitude: parseFloat(init.brand.latitude),\r\n            longitude: parseFloat(init.brand.longitude),\r\n            name: init.brand.brand_name,\r\n            address: init.brand.address,\r\n            scale: 12\r\n          })\r\n        }}>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/map.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text'>\r\n            {`${init.brand.area.join('')}${init.brand.address}`}\r\n          </View>\r\n          <View className='at-col at-col-1'>\r\n            <AtIcon value='chevron-right' size='18' color='#afafaf'></AtIcon>\r\n          </View>\r\n        </View>\r\n        <View className='at-row list_show'>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/rank.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text'>\r\n          {tools.brandRank(init.brand.rank)} {tools.brandSpace(init.brand.in_out)}\r\n          </View>\r\n          <View className='at-col at-col-1'>\r\n          </View>\r\n        </View>\r\n        <View className='at-row list_show'>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/bookt.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text'>\r\n            入园方式 <Text style={{color: init.limit.is_book ? '#FF9800' : '#4CAF50', fontWeight: 600}}>{init.limit.is_book ? '需提前预约' : '无需预约'}</Text>\r\n            {\r\n              (init.limit.today_num > -1) ? <Text style={{color: 'red'}}> {init.limit.today_num_str}</Text> : null\r\n            }\r\n            \r\n          </View>\r\n        </View>\r\n\r\n        <View className='at-row list_show'>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/date.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text'>\r\n            实时天气 <Text>{weather}</Text>\r\n          </View>\r\n        </View>\r\n\r\n\r\n        \r\n        <View className='at-row list_show'>\r\n          <View className='at-col at-col-1'>\r\n            <Image src={`${tools.picUrl}/info/date.png`} mode=\"widthFix\" className=\"icon\"/>\r\n          </View>\r\n          <View className='at-col at-col-10 text'>\r\n            <View>开放时间 {`${moment(init.limit.enable_start_time_unix * 1000).format('YYYY年M月D日')} 至 ${moment(init.limit.enable_end_time_unix * 1000).format('YYYY年M月D日')}`}</View>\r\n            <View style={{paddingTop: '4px', color: '#4CAF50', fontWeight: 400}}>{init.limit.enable_week_str.join(' , ')}{(init.limit.is_book) ? '可约' : '开放'}</View>\r\n          </View>\r\n          <View className='at-col at-col-1'>\r\n          </View>\r\n        </View>\r\n      </View>\r\n\r\n\r\n      {init.brand.rule_list !== null && init.brand.rule_list.map(v =>  <View>\r\n        <View className=\"brand_title\">\r\n            <View className=\"t_icon\"></View>\r\n            <View className=\"t_name\">{v.val.title}</View>\r\n        </View>\r\n        <View className=\"brand_html\">\r\n          <RichText className='htmlformat' style={{lineHeight: '30px'}} nodes={tools.removeCss(v.val.memo)}/>\r\n        </View>\r\n      </View>)}\r\n     \r\n\r\n      <View>\r\n        <View className=\"brand_title\">\r\n            <View className=\"t_icon\"></View>\r\n            <View className=\"t_name\">特色介绍</View>\r\n        </View>\r\n        <View className=\"brand_html\">\r\n          <RichText  nodes={tools.removeCss(init.brand.content)}/>\r\n        </View>\r\n      </View>\r\n\r\n    \r\n      <View style={{height: '80px'}}></View>\r\n      <View className=\"at-row brand_memu\">\r\n        <View className='at-col at-col-4 card_status'>\r\n          <View className='at-row'>\r\n            <View className='at-col at-col-2'><Image mode=\"widthFix\" src={`${tools.picUrl}/index/card.png`} className=\"c_icon\"/></View>\r\n            <View className='at-col at-col-9' onClick={() => {\r\n                if (card.num > 0) {\r\n                  setChangeCard(true)\r\n                } \r\n            }}>\r\n              {card.num === 0 ? <Text className='nocard'>暂无可用卡</Text> : <Text className='hadcard'>NO.{card.good_id} <Text className=\"ok\">换卡</Text> </Text>}\r\n              \r\n            </View> \r\n          </View>\r\n        </View> \r\n        <View className='at-col at-col-4'>\r\n          {init.limit.is_open ? \r\n             <View className=\"book\" style={init.limit.is_book ? { backgroundColor: \"#FF5722\" } :{}} onClick={() => {\r\n              //setBookShow(true)\r\n              setChangeCard(true)\r\n              // operBook()\r\n            }}>{(card.num === 0) ? '激活年票' : (() => {\r\n              return (init.limit.is_book) ? '预约入园' : '开放日历' \r\n            })()}</View>\r\n          : <View className=\"book_close\">暂停入园</View>}\r\n        </View> \r\n        <View className='at-col at-col-4'>\r\n           <View className=\"book\" style={{ backgroundColor: \"#FF5722\" }} onClick={() => {\r\n            Taro.navigateTo({\r\n              url: '/pages/buy/index'\r\n            })\r\n          }}>购买年票</View>\r\n        </View> \r\n      </View>\r\n\r\n        <AtFloatLayout isOpened={bookShow} title={(init.limit.is_book) ? `请您选择预约日期` : '景区开放日历'} onClose={() => {\r\n          setBookShow(false)\r\n        }}>\r\n          <View style={{backgroundColor:'#fff'}}>\r\n            <Calendar start={dateLine.start} end={dateLine.end} list={dateLine.list} book={init.limit.is_book} onChange={  date =>{\r\n                     Taro.showModal({\r\n                      title: `No.${card.good_id}预约确认`,\r\n                      content: `预约 ${date} 入园？`,\r\n                      success:  async (res) => {\r\n                        if (res.confirm) {\r\n                          setBookShow(false)\r\n                          Taro.showLoading({\r\n                            mask: true,\r\n                            title: '提交中'\r\n                          })\r\n                          const params = {\r\n                            brand_code : brandCode,\r\n                            good_id : card.good_id,\r\n                            day : date\r\n                          }\r\n                          const data = await tools.Api.addBrandBook(params)\r\n                          Taro.hideLoading()\r\n                          Taro.showToast({\r\n                            title: data.message,\r\n                            icon: 'none',\r\n                            duration: 2000\r\n                          })\r\n                        }\r\n                      }\r\n                    })\r\n            }}/>\r\n          </View>\r\n        </AtFloatLayout>\r\n        <AtFloatLayout isOpened={changeCard} title=\"请您选择需预约卡\" onClose={() => {\r\n          setChangeCard(false)\r\n        }}>\r\n          <View style={{backgroundColor:'#fff', width: '93%', margin: '0 auto 20px auto'}}>\r\n            {card.list.map(v => <CardItem data={v} oper={(id, name) => {\r\n              setCard({\r\n                num : card.num,\r\n                good_id: id,\r\n                list: card.list,\r\n                name\r\n              })\r\n              setChangeCard(false)\r\n              operBook()\r\n            }}/>)}\r\n          </View>\r\n        </AtFloatLayout>\r\n\r\n    </View>}\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport Taro, { usePageScroll } from '@tarojs/taro';\r\nimport { View } from \"@tarojs/components\";\r\nimport { AtIcon } from 'taro-ui'\r\nimport './index.scss'\r\n\r\n\r\nconst Main = (props) => {\r\n    //每月多少天\r\n    let MONTH_DAYS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\r\n    const WEEK_NAMES = ['日', '一', '二', '三', '四', '五', '六'];\r\n    const { start, end, list, book }  = props\r\n    const LINES = [1, 2, 3, 4, 5, 6];\r\n    const [year, setLoinsYear] = useState(0);\r\n    const [month, seLoinstMonth] = useState(0);\r\n    const [endYear, setEndYear] = useState(0);\r\n    const [endMonth, setEndMonth] = useState(0);\r\n    const currentDate = new Date();\r\n    const [tag, setTag] = useState(false);\r\n    useEffect(() => {\r\n        if (Boolean(start) && Boolean(end)) {\r\n            const startDate = new Date(start)\r\n            const endDate = new Date(end)\r\n            setLoinsYear(startDate.getFullYear())\r\n            seLoinstMonth(startDate.getMonth())\r\n            setEndYear(endDate.getFullYear())\r\n            setEndMonth(endDate.getMonth())\r\n        } else {\r\n            setLoinsYear(currentDate.getFullYear())\r\n            seLoinstMonth(currentDate.getMonth())\r\n            setEndYear(currentDate.getFullYear())\r\n            setEndMonth(currentDate.getMonth() + 3)\r\n        }\r\n    }, [ start, end ])\r\n    //获取当前月份\r\n    const getMonth = (date) => {\r\n        return date.getMonth();\r\n    }\r\n    //获取当前年份\r\n    const getFullYear = (date) => {\r\n        return date.getFullYear();\r\n    }\r\n\r\n    const getCurrentMonthDays = (month, year) => {\r\n        let _year = year + currentDate.getFullYear();\r\n        if(_year % 100 != 0 && _year % 4 == 0 || _year % 400 == 0){\r\n            MONTH_DAYS = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\r\n        }\r\n        return MONTH_DAYS[month]\r\n    }\r\n    //当前月第一天是周几\r\n    const getDateByYearMonth = (year, month, day = 1) => {\r\n        var date = new Date()\r\n        date.setFullYear(year)\r\n        date.setMonth(month, day)\r\n        return date\r\n    }\r\n    const getWeeksByFirstDay = (year, month) => {\r\n        var date = getDateByYearMonth(year, month)\r\n        return date.getDay()\r\n    }\r\n    const formatNum = num => {\r\n        const day = parseInt(num ,10)\r\n        return (day < 10) ? `0${day}` : `${day}`\r\n    }\r\n\r\n    const getDayText = (line, weekIndex, weekDay, monthDays) => {\r\n        var number = line * 7 + weekIndex - weekDay + 1\r\n        var date = new Date()\r\n        if (number <= 0 || number > monthDays) {\r\n            return <View className=\"day-c\" key={weekIndex}>&nbsp;</View>\r\n        }\r\n        const today = `${date.getFullYear()}-${formatNum(date.getMonth() + 1)}-${formatNum(date.getDate())}`\r\n        const now = `${year}-${formatNum(month + 1)}-${formatNum(number)}`\r\n        const enable = (() => {\r\n            const dateEnable = (() => {\r\n                if (list.length === 0) {\r\n                    return true\r\n                } else {\r\n                    const check = []\r\n                    list.forEach(ele => {\r\n                        if (ele.val === now && ele.enable === true) {\r\n                            check.push(ele)\r\n                        }\r\n                    });\r\n                    return Boolean(check.length > 0)\r\n                }\r\n            })()\r\n            if (new Date(now) >= new Date(start) && new Date(now) <= new Date(end) && dateEnable) {\r\n                return true\r\n            }\r\n        })()\r\n        return <React.Fragment>\r\n                {enable ? <View className=\"day-c\" key={weekIndex} onClick={() => {\r\n                    (book) ? props.onChange(now) : Taro.showToast({\r\n                        title: '无需预约直接入园',\r\n                        icon: 'none',\r\n                        duration: 1000\r\n                      })\r\n                }}>\r\n                    <View className=\"day\" style={((today === now)) ? {width: '25px', height: '25px', borderRadius: '25px', backgroundColor: '#f12828', color: '#fff', margin: '0 auto', fontSize: '14px', lineHeight: '25px'} : {}}>{number}</View>\r\n                    <View className=\"desc\" style={ {color: book ? '#26942a' : '#2196f3'} }>{book ? '可约' : '开放'}</View>\r\n                </View> : <View className=\"day-c\" key={weekIndex}>\r\n                    <View className=\"day\" style={(today === now) ? {width: '25px', height: '25px', borderRadius: '25px', backgroundColor: '#e2d7d7', color: '#fff', margin: '0 auto', fontSize: '14px', lineHeight: '25px'} : {color: '#c7c3c3'}}>{number}</View>\r\n                    <View className=\"desc\" style={{color: '#fff'}}>过期</View>\r\n                </View>}\r\n                \r\n              </React.Fragment>\r\n    }\r\n\r\n    const setCurrentYearMonth = (date) => {\r\n        var month = getMonth(date)\r\n        var year = getFullYear(date)\r\n        setLoinsYear(year);\r\n        seLoinstMonth(month)\r\n        setTag(false)\r\n    }\r\n\r\n    const monthChange = (monthChanged) => {\r\n        if (tag) {\r\n            return;\r\n        } else {\r\n            setTag(true)\r\n        }\r\n        var monthAfter = (month + monthChanged)\r\n        const operMonth = (() => {\r\n            if (monthChanged === 1) {\r\n                return (month === 11) ? 0 : monthAfter\r\n            } else {\r\n                return (month === 0) ? 11 : monthAfter\r\n            }\r\n        })()\r\n        const operYear = (() => {\r\n            if (month === 11 && monthChanged === 1) {\r\n                return year + 1\r\n            } else if (month === 0 && monthChanged === -1) {\r\n                return year - 1\r\n            } else {\r\n                return year\r\n            }\r\n        })()\r\n        const now = new Date(`${operYear}`, `${operMonth + 1}`)\r\n        const limitStart =  new Date(`${new Date(start).getFullYear()}`, `${new Date(start).getMonth() + 1}`)\r\n        const limitEnd =  new Date(`${new Date(end).getFullYear()}`, `${new Date(end).getMonth() + 1}`)\r\n        if (now >= limitStart && now <= limitEnd) {\r\n            var date = getDateByYearMonth(operYear, operMonth)\r\n            setCurrentYearMonth(date)\r\n        } else {\r\n            setTag(false)\r\n        }\r\n    }\r\n    const formatNumber = (num) => {\r\n        var _num = num + 1\r\n        return _num < 10 ? `0${_num}` : `${_num}`\r\n    }\r\n\r\n    // let monthDays = getCurrentMonthDays(month);\r\n    let weekDay = getWeeksByFirstDay(year, month);\r\n\r\n    let _startX = 0;\r\n    return <React.Fragment>\r\n        <View className=\"loins-calendar\"\r\n            onTouchEnd={(val) => {\r\n                if (_startX > val.changedTouches[0]['clientX'] + 30) {\r\n                    monthChange(1);\r\n                }\r\n                if (_startX < val.changedTouches[0]['clientX'] - 30) {\r\n                    monthChange(-1);\r\n                }\r\n            }} \r\n            onTouchStart={(val) => {\r\n                _startX = val.changedTouches[0]['clientX']\r\n            }}\r\n        >\r\n            <View className=\"loins-calendar-tabbar\">\r\n                <View><AtIcon value='chevron-left' size='25' color='#297AF8' onClick={() => {\r\n                    monthChange(-1);\r\n                }}></AtIcon></View>\r\n                <View className=\"loins-calendar-title\">{year} 年 {formatNumber(month)}月</View>\r\n                <View><AtIcon value='chevron-right' size='25' color='#297AF8' onClick={() => {\r\n                    monthChange(1);\r\n                }}></AtIcon></View>\r\n            </View>\r\n            {\r\n                WEEK_NAMES.map((week, key) => {\r\n                    return <View className=\"title-c\" key={key}>{week}</View>\r\n                })\r\n            }\r\n            {\r\n                LINES.map((l, key) => {\r\n                    return <View key={key} className=\"day-content\">\r\n                        {\r\n                            WEEK_NAMES.map((week, index) => {\r\n                                return getDayText(key, index, weekDay, getCurrentMonthDays(month, year))\r\n                            })\r\n                        }\r\n                    </View>\r\n                })\r\n            }\r\n        </View>\r\n    </React.Fragment>\r\n}\r\nexport default Main;", "// extracted by mini-css-extract-plugin", "/*\r\n * @Author: your name\r\n * @Date: 2020-12-03 10:42:24\r\n * @LastEditTime: 2021-12-24 21:59:15\r\n * @LastEditors: Please set LastEditors\r\n * @Description: In User Settings Edit\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/components/memu/brand.jsx\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport { View, Text, Image, Swiper, SwiperItem } from '@tarojs/components'\r\nimport Taro from '@tarojs/taro'\r\nimport tools from '@/utils/tools'\r\n\r\nconst Brand = (props) => {\r\n    const {love} = props\r\n    return (\r\n        <View className=\"topNav\" style={{position: 'absolute'}}>\r\n            <View style={{height:  `${tools.reTopH(6)}rpx`}}></View>\r\n            <View className='at-row'>\r\n                <View className='at-col at-col-1 left' onClick={() => {\r\n                        Taro.navigateBack({\r\n                            delta: 1,\r\n                            fail: () => {\r\n                                Taro.navigateTo({\r\n                                    url: '/pages/index/index'\r\n                                })\r\n                            }\r\n                        })\r\n                }}>\r\n                    <Image src={`${tools.picUrl}/memu/back.png`} mode='widthFix' className=\"topIcon_b\" />\r\n                </View>\r\n                <View className='at-col at-col-1' onClick={() => {\r\n                    props.oper()\r\n                }}>\r\n                {\r\n                    love ? \r\n                    <Image src={`${tools.picUrl}/memu/love_s.png`} mode='widthFix' className=\"topIcon_b\" />\r\n                        : \r\n                    <Image src={`${tools.picUrl}/memu/love.png`} mode='widthFix' className=\"topIcon_b\" />\r\n                \r\n                }\r\n                </View>\r\n                <View className='at-col at-col-9'></View>\r\n            </View>\r\n        </View>\r\n    )\r\n}\r\nexport default Brand ", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.jsx\"\nvar config = {\"navigationBarTitleText\":\"\",\"navigationStyle\":\"custom\",\"enableShareAppMessage\":true,\"transparentTitle\":\"always\",\"titlePenetrate\":\"YES\"};\n\ncomponent.enableShareAppMessage = true\nvar inst = Page(createPageConfig(component, 'pages/brand/index', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AALA;AAAA;AAMA;AACA;AACA;AACA;AACA;AAAA;AAJA;AAAA;AAKA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAFA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAJA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAjCA;AAAA;AAAA;AAmCA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAGA;AAAA;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAIA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAEA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAGA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAKA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;AAIA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAIA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAGA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;ACnZA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AACA;AAIA;AAAA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;AAEA;AAEA;AACA;;;;;;;;;;;AC1MA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;AAAA;AAGA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AACA;AAGA;AACA;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
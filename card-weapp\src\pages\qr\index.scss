page {
    background-color: #3CB980;
}

.user_img {
    width: 60px;
    height: 60px;
    border-radius: 60px;
    background-color: #fff;
}
.user_good_id {
    padding-top: 11px;
    padding-left: 20px;
    color: #fff;
    font-weight: bold;
}
.qr {
    background-color: #fff;
    border-radius: 18px;
    height: 1060px;
    margin-top: 25px;
    .title{
        width: 100%;
        text-align: center;
        color: #000;
        font-size: 32px;
        font-weight: bold;
        padding: 30px 0;
    }
    .note {
        width: 80%;
        margin: 10px auto;
        border-radius: 10px;
    }
}
.reload {
    margin-top: 8px;
    .txt {
        font-size: 28px;
        color: #49aa7f;
        padding-left: 10px;
    }
}
.at-grid__flex .content-inner__img {
    width: 55px;
    height: 55px;
}
.at-grid__flex .content-inner__text {
    font-size: 25px;
    color: rgb(78, 79, 92);
}
.near {
    margin-top: 13px;
    height: 40px;
    background-color: #bff1dd;
    border-radius: 18px;
    padding: 0 0 50px 0;
    font-size: 30px;
    color: #373E47;
    font-weight: bold;
    height: 60px;
    .list {
        height: 280px;
        background-color: #fff;
        border-radius: 18px;
        margin-top: -1px;
        margin-bottom: 30px;
        text-align: center;
        color: rgb(124, 121, 121);
        font-weight: normal;
    }
    .none {
        width: 120px;
        height: 120px;
        margin: 30px auto;
        text-align: center;
    }
}
.help {
    border-radius: 18px;
    margin-top: 20px;
    text-align: center;
}
.at-noticebar {
    border-radius: 10px;
}
.at-icon{
    margin-top: 0;
}
.user_img_show { 
    width: 180px;
    height: 220px;
    border: 1px solid #bff1dd;
    border-radius: 10px;
}
.cardBtn {
    display: inline-block;
    border: 1px solid #fff;
    padding: 4px 10px;
    border-radius: 9px;
    margin-left: 8px;
    font-size: 24px;
    color: #fff;
}
(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/buy/ok"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/buy/ok.jsx":
/*!**************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/buy/ok.jsx ***!
  \**************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _components_tool_bg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/tool/bg */ "./src/components/tool/bg.jsx");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./index.scss */ "./src/pages/buy/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__);











function Index(props) {
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])([]),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    init = _useState2[0],
    setInit = _useState2[1];
  var good_key = "";
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var myOrders;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.showLoading({
            mask: true,
            title: '读取中'
          });
          _context.next = 3;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].Api.myOrders();
        case 3:
          myOrders = _context.sent;
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.hideLoading();
          if (myOrders.code === 200) {
            setInit(myOrders.data[myOrders.data.length - 1]);
          }
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), []);
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
    className: "index",
    children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_components_tool_bg__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], {}), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
      style: {
        textAlign: 'center',
        marginTop: "60px",
        color: "#333",
        fontSize: "18px"
      },
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["Image"], {
          src: "".concat(_utils_tools__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"].picUrl, "/bind/ok.png"),
          mode: "widthFix",
          style: {
            width: '20%'
          }
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
        style: {
          marginTop: "10px",
          color: "#848181"
        },
        children: "\u8D2D\u4E70\u6210\u529F"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
        className: "okbtn",
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.redirectTo({
            url: '/pages/bind/index?key=' + init.good_key
          });
        },
        children: "\u7ACB\u5373\u7ED1\u5B9A\u6FC0\u6D3B"
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__["View"], {
        className: "checkbtn",
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_5___default.a.redirectTo({
            url: '/pages/my/order'
          });
        },
        children: "\u67E5\u770B\u6FC0\u6D3B\u7801\uFF0C\u6682\u4E0D\u7ED1\u5B9A"
      })]
    })]
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Index);

/***/ }),

/***/ "./src/pages/buy/ok.jsx":
/*!******************************!*\
  !*** ./src/pages/buy/ok.jsx ***!
  \******************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ok_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./ok.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/buy/ok.jsx");


var config = {"navigationBarTitleText":"年票购买成功"};


var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_ok_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/buy/ok', {root:{cn:[]}}, config || {}))



/***/ })

},[["./src/pages/buy/ok.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=ok.js.map
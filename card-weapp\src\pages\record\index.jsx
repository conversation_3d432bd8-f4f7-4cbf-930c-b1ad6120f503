import { Component } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { AtIcon } from "taro-ui"

function Index (props) { 
  return (
    <View className='index'>
        <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "16px"}}>
          <View><AtIcon value='calendar' size='30' color='#848181'></AtIcon></View>
          <View style={{marginTop: "10px" , color: "#848181"}}>暂无入园记录</View>
        </View>
    </View>
  )
}
export default Index;

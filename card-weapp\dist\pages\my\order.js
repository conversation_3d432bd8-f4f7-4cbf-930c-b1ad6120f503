(my["webpackJsonp"] = my["webpackJsonp"] || []).push([["pages/my/order"],{

/***/ "./node_modules/babel-loader/lib/index.js!./src/pages/my/order.jsx":
/*!****************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./src/pages/my/order.jsx ***!
  \****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var _components_my_order__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/my/order */ "./src/components/my/order.jsx");
/* harmony import */ var taro_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! taro-ui */ "./node_modules/taro-ui/dist/index.esm.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./index.scss */ "./src/pages/my/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__);












function Index(props) {
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])([]),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    init = _useState2[0],
    setInit = _useState2[1];
  Object(react__WEBPACK_IMPORTED_MODULE_3__["useEffect"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee() {
    var myOrders;
    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showLoading({
            mask: true,
            title: '读取中'
          });
          _context.next = 3;
          return _utils_tools__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"].Api.myOrders();
        case 3:
          myOrders = _context.sent;
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.hideLoading();
          if (myOrders.code === 200) {
            setInit(myOrders.data);
          }
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), []);
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
    className: "index",
    children: [init.length === 0 ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      style: {
        textAlign: 'center',
        marginTop: "60px",
        color: "#333",
        fontSize: "16px"
      },
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(taro_ui__WEBPACK_IMPORTED_MODULE_8__[/* AtIcon */ "f"], {
          value: "calendar",
          size: "30",
          color: "#848181"
        })
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        style: {
          marginTop: "10px",
          color: "#848181"
        },
        children: "\u6682\u65E0\u8BA2\u5355\u8BB0\u5F55"
      })]
    }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      children: init.map(function (v) {
        return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_components_my_order__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"], {
          data: v
        });
      })
    }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      style: {
        height: '30px'
      }
    })]
  });
}
/* harmony default export */ __webpack_exports__["a"] = (Index);

/***/ }),

/***/ "./src/components/my/order.jsx":
/*!*************************************!*\
  !*** ./src/components/my/order.jsx ***!
  \*************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/cjs/react.production.min.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-alipay/dist/components-react.js");
/* harmony import */ var _utils_tools__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/tools */ "./src/utils/tools.js");
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ "./node_modules/moment/dist/moment.js");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./index.scss */ "./src/components/my/index.scss");
/* harmony import */ var _index_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_index_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__);











var Item = function Item(props) {
  var data = props.data;
  console.warn('--------------------', data);
  var _useState = Object(react__WEBPACK_IMPORTED_MODULE_3__["useState"])(true),
    _useState2 = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(_useState, 2),
    show = _useState2[0],
    setShow = _useState2[1];
  var csscolor = data.bound || data.refund_status ? {
    color: '#ccc'
  } : {};
  return /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(react__WEBPACK_IMPORTED_MODULE_3__["Fragment"], {
    children: show ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
      className: "bookItem",
      children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        className: "at-row cardNum",
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-col at-col-4",
          style: csscolor,
          children: ["\u72B6\u6001\uFF1A", data.bound ? '已使用' : '未使用', " ", data.refund_status ? '(已退)' : '']
        }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-col at-col-7",
          style: {
            textAlign: 'right'
          },
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
            style: csscolor,
            children: ["\u8D2D\u4E70\u65E5\u671F\uFF1A", data.create_time]
          })
        })]
      }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
        children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-row",
          style: {
            padding: '0 14px',
            width: '90%',
            marginTop: '14px'
          },
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
            className: "at-col at-col-3",
            style: {
              textAlign: 'center'
            },
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
              className: "day",
              style: csscolor,
              children: "\u5317\u4EAC\u98CE\u666F\u540D\u80DC\u533A\u7535\u5B50\u5E74\u7968\u4E00\u5F20"
            })
          })
        }), !data.refund_status ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-row",
          style: {
            padding: '0 14px',
            width: '90%',
            marginBottom: '0px',
            marginTop: '14px',
            color: '#8e8c8c'
          },
          onClick: function onClick() {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.setClipboardData({
              data: data.good_key,
              success: function success(res) {
                _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showToast({
                  title: '激活码已复制',
                  icon: 'none'
                });
              }
            });
          },
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
              style: csscolor,
              children: ["\u6FC0\u6D3B\u7801\uFF1A", data.good_key]
            })
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
            className: "copy_btn",
            children: "\u70B9\u51FB\u590D\u5236"
          })]
        }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {}), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-row",
          style: {
            padding: '0 14px',
            width: '90%',
            marginBottom: '14px',
            marginTop: '6px',
            color: '#8e8c8c'
          },
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
            className: "at-col at-col-12",
            style: {
              textAlign: 'left'
            },
            children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["Text"], {
              style: csscolor,
              children: "\u51ED\u6B64\u6FC0\u6D3B\u7801\u53EF\u6FC0\u6D3B\u5E74\u7968\u4E00\u5F20\uFF0C\u91CD\u7ED1\u65E0\u6548\uFF0C\u8BF7\u59A5\u5584\u4FDD\u7BA1"
            })
          })
        }), !data.bound && !data.refund_status ? /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsxs"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          className: "at-row oper",
          children: [/*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
            className: "at-col at-col-6",
            style: {
              textAlign: 'center',
              borderRight: '1px solid #e2e2e2'
            },
            onClick: function onClick() {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.navigateTo({
                url: "/pages/bind/index?key=".concat(data.good_key)
              });
            },
            children: "\u7ACB\u5373\u6FC0\u6D3B\u7ED1\u5B9A"
          }), /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
            className: "at-col at-col-6",
            style: {
              textAlign: 'center'
            },
            onClick: function onClick() {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default.a.showModal({
                title: "\u8F6C\u8D60\u6FC0\u6D3B\u7801",
                content: "\u8BF7\u5C06\u8BE5\u6FC0\u6D3B\u7801\u53D1\u9001\u7ED9\u60A8\u7684\u5BB6\u4EBA\u6216\u670B\u53CB\uFF0C\u51ED\u6B64\u7801\u53EF\u6FC0\u6D3B\u5317\u4EAC\u98CE\u666F\u540D\u80DC\u533A\u534F\u4F1A\u5E74\u7968\u4E00\u5F20",
                success: function () {
                  var _success = Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])( /*#__PURE__*/Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().mark(function _callee(res) {
                    return Object(D_Sites_card_all_card_weapp_node_modules_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])().wrap(function _callee$(_context) {
                      while (1) switch (_context.prev = _context.next) {
                        case 0:
                        case "end":
                          return _context.stop();
                      }
                    }, _callee);
                  }));
                  function success(_x) {
                    return _success.apply(this, arguments);
                  }
                  return success;
                }()
              });
            },
            children: "\u8F6C\u8D60\u5BB6\u4EBA\u6216\u670B\u53CB"
          })]
        }) : /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
          children: /*#__PURE__*/Object(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__["jsx"])(_tarojs_components__WEBPACK_IMPORTED_MODULE_5__["View"], {
            style: {
              height: '10px'
            }
          })
        })]
      })]
    }) : null
  });
};
/* harmony default export */ __webpack_exports__["a"] = (Item);

/***/ }),

/***/ "./src/pages/my/order.jsx":
/*!********************************!*\
  !*** ./src/pages/my/order.jsx ***!
  \********************************/
/*! no exports provided */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/runtime.esm.js");
/* harmony import */ var _node_modules_babel_loader_lib_index_js_order_jsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/babel-loader/lib!./order.jsx */ "./node_modules/babel-loader/lib/index.js!./src/pages/my/order.jsx");


var config = {"navigationBarTitleText":"我的订单"};


var inst = Page(Object(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__["createPageConfig"])(_node_modules_babel_loader_lib_index_js_order_jsx__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], 'pages/my/order', {root:{cn:[]}}, config || {}))



/***/ })

},[["./src/pages/my/order.jsx","runtime","taro","vendors","common"]]]);
//# sourceMappingURL=order.js.map
import { useState, useEffect } from 'react'
import Taro, { usePageScroll, useShareAppMessage } from '@tarojs/taro';
import { useSelector } from 'react-redux'
import { View, Text, Image, Swiper, SwiperItem, Video, RichText } from '@tarojs/components'
import { AtIcon , AtFloatLayout, AtNoticebar} from "taro-ui"
import Calendar from "@/components/calendar";
import CardItem from "@/components/card";
import Skeleton from 'taro-skeleton'
import Top from '@/components/memu/brand'
import tools from '@/utils/tools'
import moment from 'moment';
import './index.scss'

function Index (props) { 
  const query = tools.getQuery()
  const login = useSelector(state => state.login)
  const [loading, setLoading] = useState(true)
  const [init, setInit] = useState(null)
  const [auto, setAuto] = useState(true)
  const [love, setLove] = useState(false)
  const [bookShow, setBookShow] = useState(false)
  const [changeCard, setChangeCard] = useState(false)
  const [card, setCard] = useState({
    num: 0,
    good_id: '--',
    list: [],
    name: '--',
  })
  const [dateLine, setDateLine] = useState({
    start: "2022-01-01",
    end: "2022-02-01",
    list: []
  })
  const [weather, setWeather] = useState("")
  const brandCode = query.data.v
  useEffect( async () => {
    if (login && Boolean(brandCode)) {
      const data = await tools.Api.brandInfo({code: brandCode})
      if (data.code === 200) {
        const cardList = await tools.getCardList({
          list: data.data.brand.templet_list_enable
        })
        if (cardList.enable_card_num > 0) {
          const enableCard = cardList.card_list.filter(v => v.enable === true)
          setCard({
            num : enableCard.length,
            good_id: enableCard[0].good_id,
            list: enableCard
          })
        }
        console.warn("--------- data.data --------", data.data)
        setInit(data.data)
        setLove(data.data.brand.collect)
        setLoading(false)
        getWeather(data.data.brand.area)
      }
    }
  }, [login])

  const getWeather = (area) => {
    let url = ""
    if (area.length > 2) {
      url = `https://wis.qq.com/weather/common?source=pc&weather_type=observe|air&province=${area[0]}&city=${area[1]}&county=${area[2]}`
    } else {
      url = `https://wis.qq.com/weather/common?source=pc&weather_type=observe|air&province=${area[0]}&city=${area[0]}&county=${area[1]}`
    }
    Taro.request({
      url: url,
      method: "get",
    }).then((res) => {
      const data = res.data.data
      // const weather_text = `${data.observe.weather} / ${data.observe.degree}° / 湿度 ${data.observe.humidity}% / ${data.observe.wind_direction_name} ${data.observe.wind_power} 级 / 空气质量 ${data.air.aqi_name}`
      const weather_text = `${data.observe.weather} / ${data.observe.degree}° / ${data.observe.wind_direction_name} ${data.observe.wind_power} 级`
      setWeather(weather_text)
    })
  }
  
  useShareAppMessage(() => {
    return {
      title: init.brand.brand_name,
      path: `/pages/brand/index?v=${brandCode}`,
      imageUrl: `${init.brand.image[0].thumbUrl}?x-oss-process=image/resize,m_fill,h_400,w_400`
    }
  })

  const operBook = async () => {
    if (card.num > 0) {
      Taro.showLoading({
        mask: true,
        title: '读取中'
      })
      const data = await tools.Api.brandBookList({
        brand_code : brandCode,
        good_id : card.good_id,
        today : init.book.today_time
      })
      Taro.hideLoading()
      if (data.code === 200) {
        if (data.data.list.length > 0) {
          setDateLine({
            start: data.data.list[0].val,
            end: data.data.list[data.data.list.length - 1].val,
            list: data.data.list
          })
          setBookShow(true)
        }
      } else {
        Taro.showToast({
          title: data.message,
          icon: 'none',
          duration: 2000
        })
      }
    } else {
      Taro.navigateTo({
        url: '/pages/bind/index'
      })
    }
  }

  const showStatus = txt => {
    if (txt.includes('|')) {
      return txt.split('|')
    }
    return [txt]
  }

  const showTime = time => {
    const newTime = []
    time.forEach(v => {
      newTime.push(v.replace(':00',''))
    })
    return newTime.join('~')
  }

  return (
    <View>
       {(init === null && Boolean(brandCode)) ?  <Skeleton title row={30} loading={loading}></Skeleton> :
    <View>
      <Top love={love} oper = {async () => {
        await tools.Api.brandCollect({
          brand_code: brandCode,
          action: (love === false) ? 'ADD' : 'DEL'
        })
        setLove(!love)
      }}/>
      <View className="brand_top_pic">
        <Swiper
          className='brand_swiper'
          indicatorColor='#999'
          indicatorActiveColor='#fff'
          circular
          indicatorDots
          autoplay={auto}
        >
          {init.brand.video !== null && init.brand.video.map(v => <SwiperItem>
            <Video
              src={v.url}
              controls={true}
              autoplay={false}
              showProgress={true}
              poster={''}
              initialTime='0'
              enablePlayGesture
              autoPauseIfOpenNative
              muted={false}
              className= "images"
              objectFit='contain'
              onPlay={() => {
                setAuto(false)
              }}
              onEnded={() => {
                setAuto(true)
              }}
            />
          </SwiperItem>)}
          {init.brand.static_list.map(v => <SwiperItem>
            <Image src={v.url} mode="heightfix" className="images"/>
          </SwiperItem>)}
        </Swiper>
      </View>
      {
        (init.brand.brand_note !== 'none' && init.brand.brand_note.length > 4) ? <AtNoticebar>{init.brand.brand_note}</AtNoticebar> : null
      }
     
      <View className="brand_content">
        <View className='at-row' style={{marginBottom: '20px'}}>
          <View className='at-col at-col-10'>
            <View className="title">{init.brand.brand_name}</View>
            <View className="memo">{init.brand.intro}</View>
          </View>
          <View className='at-col at-col-2'>
            {init.limit.is_open_config.is_open ? <View className="open">
                <View style={{marginTop: '5px'}}>
                    {
                      showStatus(init.limit.is_open_config.is_open_tips).map(v => <View className="num">{v}</View>)
                    }
                </View>
            </View> : <View className="open close">
                <View style={{marginTop: '5px'}}>
                    {
                      showStatus(init.limit.is_open_config.is_open_tips).map(v => <View className="num">{v}</View>)
                    }
                </View>
            </View>}
          </View>
        </View>
        
       
        <View className='at-row list_show' onClick={() => {
          Taro.makePhoneCall({
            phoneNumber: init.brand.tel
          })
        }}>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/time.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text'>
            营业时间 {showTime(init.brand.jijie.time)}
          </View>
          <View className='at-col at-col-1'>
            <AtIcon value='phone' size='18' color='#afafaf'></AtIcon>
          </View>
        </View>

        <View className='at-row list_show' onClick={() => {
          Taro.openLocation({
            latitude: parseFloat(init.brand.latitude),
            longitude: parseFloat(init.brand.longitude),
            name: init.brand.brand_name,
            address: init.brand.address,
            scale: 12
          })
        }}>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/map.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text'>
            {`${init.brand.area.join('')}${init.brand.address}`}
          </View>
          <View className='at-col at-col-1'>
            <AtIcon value='chevron-right' size='18' color='#afafaf'></AtIcon>
          </View>
        </View>
        <View className='at-row list_show'>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/rank.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text'>
          {tools.brandRank(init.brand.rank)} {tools.brandSpace(init.brand.in_out)}
          </View>
          <View className='at-col at-col-1'>
          </View>
        </View>
        <View className='at-row list_show'>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/bookt.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text'>
            入园方式 <Text style={{color: init.limit.is_book ? '#FF9800' : '#4CAF50', fontWeight: 600}}>{init.limit.is_book ? '需提前预约' : '无需预约'}</Text>
            {
              (init.limit.today_num > -1) ? <Text style={{color: 'red'}}> {init.limit.today_num_str}</Text> : null
            }
            
          </View>
        </View>

        <View className='at-row list_show'>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/date.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text'>
            实时天气 <Text>{weather}</Text>
          </View>
        </View>


        
        <View className='at-row list_show'>
          <View className='at-col at-col-1'>
            <Image src={`${tools.picUrl}/info/date.png`} mode="widthFix" className="icon"/>
          </View>
          <View className='at-col at-col-10 text'>
            <View>开放时间 {`${moment(init.limit.enable_start_time_unix * 1000).format('YYYY年M月D日')} 至 ${moment(init.limit.enable_end_time_unix * 1000).format('YYYY年M月D日')}`}</View>
            <View style={{paddingTop: '4px', color: '#4CAF50', fontWeight: 400}}>{init.limit.enable_week_str.join(' , ')}{(init.limit.is_book) ? '可约' : '开放'}</View>
          </View>
          <View className='at-col at-col-1'>
          </View>
        </View>
      </View>


      {init.brand.rule_list !== null && init.brand.rule_list.map(v =>  <View>
        <View className="brand_title">
            <View className="t_icon"></View>
            <View className="t_name">{v.val.title}</View>
        </View>
        <View className="brand_html">
          <RichText className='htmlformat' style={{lineHeight: '30px'}} nodes={tools.removeCss(v.val.memo)}/>
        </View>
      </View>)}
     

      <View>
        <View className="brand_title">
            <View className="t_icon"></View>
            <View className="t_name">特色介绍</View>
        </View>
        <View className="brand_html">
          <RichText  nodes={tools.removeCss(init.brand.content)}/>
        </View>
      </View>

    
      <View style={{height: '80px'}}></View>
      <View className="at-row brand_memu">
        <View className='at-col at-col-4 card_status'>
          <View className='at-row'>
            <View className='at-col at-col-2'><Image mode="widthFix" src={`${tools.picUrl}/index/card.png`} className="c_icon"/></View>
            <View className='at-col at-col-9' onClick={() => {
                if (card.num > 0) {
                  setChangeCard(true)
                } 
            }}>
              {card.num === 0 ? <Text className='nocard'>暂无可用卡</Text> : <Text className='hadcard'>NO.{card.good_id} <Text className="ok">换卡</Text> </Text>}
              
            </View> 
          </View>
        </View> 
        <View className='at-col at-col-4'>
          {init.limit.is_open ? 
             <View className="book" style={init.limit.is_book ? { backgroundColor: "#FF5722" } :{}} onClick={() => {
              //setBookShow(true)
              setChangeCard(true)
              // operBook()
            }}>{(card.num === 0) ? '激活年票' : (() => {
              return (init.limit.is_book) ? '预约入园' : '开放日历' 
            })()}</View>
          : <View className="book_close">暂停入园</View>}
        </View> 
        <View className='at-col at-col-4'>
           <View className="book" style={{ backgroundColor: "#FF5722" }} onClick={() => {
            Taro.navigateTo({
              url: '/pages/buy/index'
            })
          }}>购买年票</View>
        </View> 
      </View>

        <AtFloatLayout isOpened={bookShow} title={(init.limit.is_book) ? `请您选择预约日期` : '景区开放日历'} onClose={() => {
          setBookShow(false)
        }}>
          <View style={{backgroundColor:'#fff'}}>
            <Calendar start={dateLine.start} end={dateLine.end} list={dateLine.list} book={init.limit.is_book} onChange={  date =>{
                     Taro.showModal({
                      title: `No.${card.good_id}预约确认`,
                      content: `预约 ${date} 入园？`,
                      success:  async (res) => {
                        if (res.confirm) {
                          setBookShow(false)
                          Taro.showLoading({
                            mask: true,
                            title: '提交中'
                          })
                          const params = {
                            brand_code : brandCode,
                            good_id : card.good_id,
                            day : date
                          }
                          const data = await tools.Api.addBrandBook(params)
                          Taro.hideLoading()
                          Taro.showToast({
                            title: data.message,
                            icon: 'none',
                            duration: 2000
                          })
                        }
                      }
                    })
            }}/>
          </View>
        </AtFloatLayout>
        <AtFloatLayout isOpened={changeCard} title="请您选择需预约卡" onClose={() => {
          setChangeCard(false)
        }}>
          <View style={{backgroundColor:'#fff', width: '93%', margin: '0 auto 20px auto'}}>
            {card.list.map(v => <CardItem data={v} oper={(id, name) => {
              setCard({
                num : card.num,
                good_id: id,
                list: card.list,
                name
              })
              setChangeCard(false)
              operBook()
            }}/>)}
          </View>
        </AtFloatLayout>

    </View>}
    </View>
  )
}
export default Index;

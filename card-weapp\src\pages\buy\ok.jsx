import { useState, useEffect } from 'react'
import { View, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import Bg from '@/components/tool/bg'
import tools from "@/utils/tools"
import "./index.scss"

function Index (props) { 
  const [init, setInit] = useState([])
  let good_key = ""
  useEffect( async () => {
    Taro.showLoading({
      mask: true,
      title: '读取中'
    })
    const myOrders = await tools.Api.myOrders()
    Taro.hideLoading()
    if (myOrders.code === 200) {
      setInit(myOrders.data[myOrders.data.length - 1])
    }
  }, [])

  return (
    <View className='index'>
        <Bg/>
        <View style={{textAlign: 'center', marginTop: "60px", color: "#333", fontSize: "18px"}}>
          <View><Image src={`${tools.picUrl}/bind/ok.png`} mode= "widthFix" style={{width : '20%'}}/></View>
          <View style={{marginTop: "10px" , color: "#848181"}}>购买成功</View>
          <View className="okbtn" onClick={() => {
            Taro.redirectTo({
              url: '/pages/bind/index?key=' + init.good_key,
            });
          }}>立即绑定激活</View>
          <View className="checkbtn" onClick={() => {
            Taro.redirectTo({
              url: '/pages/my/order',
            });
          }}>查看激活码，暂不绑定</View>
        </View>
    </View>
  )
}
export default Index;

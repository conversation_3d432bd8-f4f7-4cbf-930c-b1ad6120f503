import { useState, useEffect } from 'react'
import Taro from '@tarojs/taro';
import { View, Text, Image, RichText } from '@tarojs/components';
import { AtFloatLayout, AtBadge } from 'taro-ui';
import Timer from '@/components/ele/timer';
import tools from '@/utils/tools';

const Ticket = (props) => {
    const { allT, openmm, weixinadinfo, coupon } = props;
    const [showNum, setShowNum] = useState(tools.showNum);
    const [tipsBar, setTipsBar] = useState(false);
    const [selData, setSelData] = useState({tips: []});
    const [showList, setShowList] = useState([]);
    const [canCoupon, setCanCoupon] = useState(coupon);
    const brandAid = (() => {
      try {
        return Boolean(weixinadinfo) ? weixinadinfo.toString().split('.')[0] : 0;
      } catch(e) {
        return 0;
      }
    })();
    useEffect(() => {
      setCanCoupon(coupon);
    }, [coupon])
    const showDate = ['指定日期', '超长有效'];
    return (<View><View>
         <View className='brand_tt' style={{padding: 0}}>
          {/* <View className='brand_tt_top'>在线预定</View> */}
          {allT.map((val, index) =>
            (val.good.id && index < showNum) ?
            <View>
             <View className='at-row brand_tt_per' key={val.id} onClick={ async () => {
              if (parseInt(val.good.place_origin, 10) === 0) {
                setSelData({
                    ...val.good,
                    name: val.name,
                    itemname: null
                });
                openmm(false);
                setTipsBar(true);
              } else {
                  const checked = showList.filter(item => item.goods_id === val.good.id)
                  if (checked.length > 0) {
                    setShowList([]);
                  } else {
                    Taro.showLoading({
                      title: '读取中',
                      mask: true
                    });
                    const allPrice = await tools.Api.getBrandDatePrice({id: val.good.id});
                    setShowList(allPrice.data.rules);
                    Taro.hideLoading();
                  }
              }
             }}>
              <View className='at-col at-col-8'>
                {(parseInt(val.good.place_origin, 10) === 1) ? <View style={{height: '10rpx'}}></View> : null}
                <View className='at-col--wrap brand_tt_name'>
                  <Text className='brand_tt_name'>{val.name}</Text>
                </View>
                <View className='brand_tt_tags'>
                  <Text className='brand_tt_tips' style={{color: '#28bb65'}}>{tools.getlimt(val.good.limittype, val.good.limitnum)}</Text>
                  <Text className='brand_tt_shu'>|</Text>
                  <Text className='brand_tt_tips' style={{color: '#e23b14'}}>{['随时退', '有效可退', '条件退'][parseInt(val.good.if_refund)]}</Text>
                  <Text style={(tools.showUseDate(val.good.is_book,val.good.open_time,val.good.close_time) !== -1) ? {display: 'inline-block'} : {display: 'none'}}>
                    <Text className='brand_tt_shu'>|</Text>
                    <Text className='brand_tt_tips' style={{color: '#ff9800'}}>{showDate[tools.showUseDate(val.good.is_book,val.good.open_time,val.good.close_time)]}</Text>
                  </Text>
                  {/* <Text className='brand_tt_shu'>|</Text>
                  <Text className='brand_tt_sale'>{tools.getSale(val.good.sales_count)}</Text> */}

                  {(parseInt(val.good.place_origin, 10) === 1) ? null : <Text><Text className='brand_tt_shu'>|</Text>
                  
                  <Text className='brand_tt_tips' style={{color:'#d02114', fontWeight: 'bold'}}>购买须知 必读
                  
                  </Text></Text>}


                  {(val.good.buy_time === '1' && parseInt(val.good.place_origin, 10) === 0) ? <View style={{width: '60%', margin: '10rpx 0'}}><Timer end={val.good.end_time} start={val.good.start_time} /></View> : null}
                </View>
              </View>
              {(parseInt(val.good.place_origin, 10) === 0) ?   <View className='at-col at-col-4' style={{textAlign: 'right'}}>
                  <View className='brand_buy_price'><Text className='price_t'>¥</Text> {tools.getPrice(val.good.share_price)}
                  <Text className='brand_buy_showp'>¥{tools.getPrice(val.good.show_price)}</Text></View>
                  {(val.good.buy_time === '1') ? <View className='brand_buy_btn_time '>抢购</View> : <View className='brand_buy_btn'>购买</View>}
                </View> :   <View className='at-col at-col-4' style={{textAlign: 'right'}}>
                    <View className='brand_buy_price'><Text className='price_t'>¥</Text> {tools.getPrice(val.good.share_price)}
                    <Text className='brand_buy_showqi'>起</Text></View>
                    <View style={{color: '#fff', fontSize: '26rpx', background: '#03A9F4', width: '62%', textAlign: 'center', marginLeft: '43%', borderRadius: '25rpx',padding:'8rpx 0'}}>立即预定</View>
                  </View>}
            </View>
            {showList.map( rules =>
              (rules.goods_id == val.good.id) ? <View className='at-row brand_tt_per' key={rules.code} style={{background: '#fbfbfb'}} onClick={() => {
                setSelData({
                  ...val.good,
                  rulescode: rules.title_code,
                  show_price: rules.show_price,
                  buy_price: rules.buy_price,
                  share_price: rules.share_price,
                  name: val.name,
                  itemname: rules.title
                });
                openmm(false);
                setTipsBar(true);
              }}>
                  <View className='at-col at-col-8'>
                    <View className='at-col--wrap brand_tt_name'>
                      <Text className='brand_tt_name' style={{fontSize: '26rpx', color: '#636161'}}>{rules.title}</Text>
                    </View>
                    <View className='brand_tt_tags'>
                      <View style={{color: 'red'}}><Text><Text className='brand_tt_tips' style={{color:'#6fb2f9'}}>购买须知</Text><Text className='brand_tt_shu'>|</Text></Text>{tools.dateInfo(rules.stime, rules.etime, rules.enable_week)}</View>
                    </View>
                  </View>
                  <View className='at-col at-col-4' style={{textAlign: 'right'}}>
                    <View className='brand_buy_price'><Text className='price_t'>¥</Text> {tools.getPrice(rules.share_price)}
                    <Text className='brand_buy_showp'>¥{tools.getPrice(rules.show_price)}</Text></View>
                    <View className='brand_buy_btn'>购买</View>
                  </View>
                </View> : null
          )}
          </View> : null
        )}
        </View>
        {allT.length > tools.showNum ? <View className='brand_show_all' onClick={() => {
            if (showNum === tools.showNum) {
              setShowNum(99)
            } else {
              setShowNum(tools.showNum)
            }
          }}>{(showNum > tools.showNum) ? `收起` : `查看全部${allT.length}类产品`}
        </View> : null}
      </View>
      {(tipsBar) ? <AtFloatLayout isOpened={tipsBar} onClose={() => {
        setTipsBar(false);
        openmm(true);
      }} title='购买须知'>
        <View className='brand_tips'>
            <View className='at-row'>
                <View className='at-col at-col-3'>
                    <Image src={selData.home_recommended_images} className='brand_good_img' mode={'aspectFill'}/>
                </View>
                <View className='at-col at-col-9' style={{paddingLeft: '15rpx'}}>
                    <View className='at-col--wrap brand_tt_name'>
                        <Text className='brand_tt_name'>{tools.getSamll(selData.name, 15)}</Text>
                    </View>
                    {(selData.itemname !== null) ? <View>{selData.itemname}</View>: null}
                    <View className='brand_tt_tags' style={(selData.itemname !== null) ? {padding: 0} : {}}>
                      <Text className='brand_tt_tips' style={{color: '#28bb65'}}>{tools.getlimt(selData.limittype, selData.limitnum)}</Text>
                      <Text className='brand_tt_shu'>|</Text>
                      <Text className='brand_tt_tips' style={{color: '#e23b14'}}>{['随时退', '有效可退', '条件退'][parseInt(selData.if_refund)]}</Text>
                      <Text style={(tools.showUseDate(selData.is_book,selData.open_time,selData.close_time) !== -1) ? {display: 'inline-block'} : {display: 'none'}}>
                        <Text className='brand_tt_shu'>|</Text>
                        <Text className='brand_tt_tips' style={{color: '#ff9800'}}>{showDate[tools.showUseDate(selData.is_book,selData.open_time,selData.close_time)]}</Text>
                      </Text>
                      {/* <Text className='brand_tt_shu'>|</Text>
                      <Text className='brand_tt_sale'>{tools.getSale(selData.sales_count)}</Text> */}
                      {(selData.buy_time === '1') ? <View style={{width: '60%', margin: '10rpx 0'}}><Timer end={selData.end_time} start={selData.start_time} /></View> : null}

                    </View>
                </View>
            </View>
            {(selData.tips.length === 0)
                ? <View>{selData.content_web}</View> :
                <View style={{paddingBottom: '50rpx'}}> {selData.tips.map(txt => {
                return (<View key={selData.title}>
                    <View style={{margin: '15rpx 0 5rpx 0', fontWeight: 'bold'}}>{txt.title}</View>
                    {(txt.memo.includes('<span class="none"></span>')) ? <RichText nodes={tools.removeCss(txt.memo)} style={{color: '#585656'}}/> : <Text style={{color: '#585656'}}>{txt.memo}</Text>}
                    <View style={{height: '30rpx'}}></View>
                    {/* <Text style={{color: '#777373'}}>{txt.memo}</Text> */}
                </View>)
                })}</View>
            }

        </View>
      </AtFloatLayout> : null}
      {(tipsBar) ?   <View className='brand_bar'>
          <View className='at-row'>
              <View className='at-col at-col-8'>
                  <View className='brand_buy_price' style={{fontSize: '40rpx'}}>
                      <Text className='price_t'>¥</Text> {tools.getPrice(selData.share_price)}
                      <Text className='brand_buy_showp'>¥{tools.getPrice(selData.show_price)}</Text>
                  </View>
              </View>
              {/*<View className='at-col at-col-4'>
                      <View className='brand_bar_btn' onClick={() => {
                          if (props.card === 0) {

                            Taro.showModal({
                              title: '友情提示',
                              content: '需持有并激活《北京风景年票》后购买此特价产品！',
                              confirmText: '购买年票',
                              cancelText:  '放弃折扣',
                              success : async (res) => {
                                if (res.confirm) {
                                  await tools.appConfig('buy')
                                }
                              }
                            })
                          } else {
                            Taro.navigateTo({
                              url: `/pages/wxshop/productBuy?productId=${selData.id}&rulescode=${(Boolean(selData.rulescode) ? selData.rulescode : 'none')}`
                            });
                          }
                      }}>立即购买</View>
              </View>*/}
          </View>
        </View> : null}
    </View>)
}
Ticket.defaultProps = {
  allT: [],
  coupon: []
}
export default Ticket;

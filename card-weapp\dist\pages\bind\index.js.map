{"version": 3, "file": "pages/bind/index.js", "sources": ["webpack:///./src/pages/bind/index.jsx", "webpack:///./src/pages/bind/index.jsx?f8a6"], "sourcesContent": ["/*\r\n * @Author: 高超\r\n * @Date: 2021-11-04 17:05:46\r\n * @LastEditTime: 2022-02-16 17:37:22\r\n * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/bind/index.jsx\r\n * love jiajia\r\n */\r\nimport { Component, useEffect, useState } from 'react'\r\nimport Taro from '@tarojs/taro'\r\nimport { View, Text, Button, Image } from '@tarojs/components'\r\nimport { AtIcon, AtButton } from 'taro-ui'\r\nimport Bg from '@/components/tool/bg'\r\nimport tools from \"@/utils/tools\"\r\nimport \"./index.scss\"\r\n\r\nfunction Index (props) {\r\n  const params =  tools.getQuery()\r\n  const good_key = Boolean(params.data.key) ? params.data.key : \"\"\r\n  console.warn(\"good_key -------------\", good_key, Boolean(params.data.key), params.data, params.data.key)\r\n  const [check, setCheck] = useState(false)\r\n  const [tel, setTel] = useState(false)\r\n  useEffect( async () => {\r\n    const userInfo = tools.getDataSafe(\"userInfo\")\r\n    if (userInfo !== null) {\r\n      setTel(Boolean(userInfo.user_tel))\r\n    } else {\r\n      setTel(false)\r\n    }\r\n  }, [tools.getDataSafe(\"userInfo\")])\r\n  return (\r\n    <View className='pageAdd'>\r\n       <Bg/>\r\n       <Image src={`${tools.picUrl}/bind/code.jpg`} style={{width: \"100%\"}} mode=\"widthFix\"/>\r\n       <View style={{textAlign:\"center\"}}>\r\n         <View className=\"title\">北京风景名胜年票</View>\r\n         <View className=\"info\">开通年票全年便捷入园</View>\r\n         <Image src={`${tools.picUrl}/bind/link.jpg`} mode=\"widthFix\" style={{marginTop : \"30px\"}}/>\r\n       </View>\r\n      <View style={{marginLeft: \"15px\", marginTop: \"15px\"}}>\r\n        <View className='at-row at-row--wrap'>\r\n        <View className='at-col at-col-1'>\r\n          <View className=\"agree\" onClick={() => {\r\n            setCheck(!check)\r\n          }}>\r\n            <View className=\"ok\">\r\n              {check ? <AtIcon value='check' size='17' color='#02b921'/> : null}\r\n            </View>\r\n          </View>\r\n        </View>\r\n        <View className='at-col at-col-10 at-col--wrap'>\r\n          <View className=\"txt\">\r\n            开通并同意<Text style={{color: \"#03A9F4\"}} onClick={() => {\r\n              Taro.navigateTo({\r\n                url: '/pages/bind/txt',\r\n              });\r\n            }}>《用户授权协议》</Text>，并授权<Text style={{fontWeight:\"bold\"}}>北京风景名胜协会</Text>使用您的姓名、证件号、手机号进行实名开卡，以便为您提供更好的电子卡服务\r\n          </View>\r\n        </View>\r\n        </View>\r\n      </View>\r\n      {check ?\r\n         (tel || Taro.getEnv() === Taro.ENV_TYPE.ALIPAY) ? \r\n          <View className={`btn ${(check) ? '' : 'disable'}`} onClick={() => {\r\n            if (!check) {\r\n              Taro.showToast({\r\n                title: '请您同意《用户授权协议》后开通',\r\n                icon: 'none',\r\n                duration: 2000\r\n              })\r\n            } else {\r\n              Taro.redirectTo({\r\n                url: '/pages/bind/rel?key=' + good_key,\r\n              });\r\n            }\r\n          }}>立即开通</View>\r\n         :\r\n         <AtButton className=\"btn\" openType=\"getPhoneNumber\"  onGetPhoneNumber={tools.getPhoneNumber.bind(tools)}>立即开通</AtButton> :\r\n         <AtButton className=\"btn disable\" onClick={() => {\r\n          Taro.showToast({\r\n            title: '请您同意《用户授权协议》后开通',\r\n            icon: 'none',\r\n            duration: 2000\r\n          })\r\n        }}>立即开通</AtButton>\r\n      }\r\n      {<View style={{textAlign:'center' , paddingTop: '20px', fontSize: '14px', color: '#03A9F4'}} onClick={() => {\r\n          Taro.navigateTo({url: '/pages/buy/index'})\r\n      }}>如尚未持有年票，可点击此处立即购买</View>}\r\n    </View>\r\n  )\r\n}\r\nexport default Index;\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"../../../node_modules/babel-loader/lib/index.js!./index.jsx\"\nvar config = {\"navigationBarTitleText\":\"激活年票\",\"navigationBarBackgroundColor\":\"#3ed087\",\"navigationStyle\":\"default\"};\n\n\nvar inst = Page(createPageConfig(component, 'pages/bind/index', {root:{cn:[]}}, config || {}))\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAIA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAGA;AACA;;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}
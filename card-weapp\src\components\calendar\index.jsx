import React, { useState, useEffect } from "react";
import Taro, { usePageScroll } from '@tarojs/taro';
import { View } from "@tarojs/components";
import { AtIcon } from 'taro-ui'
import './index.scss'


const Main = (props) => {
    //每月多少天
    let MONTH_DAYS = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    const WEEK_NAMES = ['日', '一', '二', '三', '四', '五', '六'];
    const { start, end, list, book }  = props
    const LINES = [1, 2, 3, 4, 5, 6];
    const [year, setLoinsYear] = useState(0);
    const [month, seLoinstMonth] = useState(0);
    const [endYear, setEndYear] = useState(0);
    const [endMonth, setEndMonth] = useState(0);
    const currentDate = new Date();
    const [tag, setTag] = useState(false);
    useEffect(() => {
        if (Boolean(start) && Boolean(end)) {
            const startDate = new Date(start)
            const endDate = new Date(end)
            setLoinsYear(startDate.getFullYear())
            seLoinstMonth(startDate.getMonth())
            setEndYear(endDate.getFullYear())
            setEndMonth(endDate.getMonth())
        } else {
            setLoinsYear(currentDate.getFullYear())
            seLoinstMonth(currentDate.getMonth())
            setEndYear(currentDate.getFullYear())
            setEndMonth(currentDate.getMonth() + 3)
        }
    }, [ start, end ])
    //获取当前月份
    const getMonth = (date) => {
        return date.getMonth();
    }
    //获取当前年份
    const getFullYear = (date) => {
        return date.getFullYear();
    }

    const getCurrentMonthDays = (month, year) => {
        let _year = year + currentDate.getFullYear();
        if(_year % 100 != 0 && _year % 4 == 0 || _year % 400 == 0){
            MONTH_DAYS = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
        }
        return MONTH_DAYS[month]
    }
    //当前月第一天是周几
    const getDateByYearMonth = (year, month, day = 1) => {
        var date = new Date()
        date.setFullYear(year)
        date.setMonth(month, day)
        return date
    }
    const getWeeksByFirstDay = (year, month) => {
        var date = getDateByYearMonth(year, month)
        return date.getDay()
    }
    const formatNum = num => {
        const day = parseInt(num ,10)
        return (day < 10) ? `0${day}` : `${day}`
    }

    const getDayText = (line, weekIndex, weekDay, monthDays) => {
        var number = line * 7 + weekIndex - weekDay + 1
        var date = new Date()
        if (number <= 0 || number > monthDays) {
            return <View className="day-c" key={weekIndex}>&nbsp;</View>
        }
        const today = `${date.getFullYear()}-${formatNum(date.getMonth() + 1)}-${formatNum(date.getDate())}`
        const now = `${year}-${formatNum(month + 1)}-${formatNum(number)}`
        const enable = (() => {
            const dateEnable = (() => {
                if (list.length === 0) {
                    return true
                } else {
                    const check = []
                    list.forEach(ele => {
                        if (ele.val === now && ele.enable === true) {
                            check.push(ele)
                        }
                    });
                    return Boolean(check.length > 0)
                }
            })()
            if (new Date(now) >= new Date(start) && new Date(now) <= new Date(end) && dateEnable) {
                return true
            }
        })()
        return <React.Fragment>
                {enable ? <View className="day-c" key={weekIndex} onClick={() => {
                    (book) ? props.onChange(now) : Taro.showToast({
                        title: '无需预约直接入园',
                        icon: 'none',
                        duration: 1000
                      })
                }}>
                    <View className="day" style={((today === now)) ? {width: '25px', height: '25px', borderRadius: '25px', backgroundColor: '#f12828', color: '#fff', margin: '0 auto', fontSize: '14px', lineHeight: '25px'} : {}}>{number}</View>
                    <View className="desc" style={ {color: book ? '#26942a' : '#2196f3'} }>{book ? '可约' : '开放'}</View>
                </View> : <View className="day-c" key={weekIndex}>
                    <View className="day" style={(today === now) ? {width: '25px', height: '25px', borderRadius: '25px', backgroundColor: '#e2d7d7', color: '#fff', margin: '0 auto', fontSize: '14px', lineHeight: '25px'} : {color: '#c7c3c3'}}>{number}</View>
                    <View className="desc" style={{color: '#fff'}}>过期</View>
                </View>}
                
              </React.Fragment>
    }

    const setCurrentYearMonth = (date) => {
        var month = getMonth(date)
        var year = getFullYear(date)
        setLoinsYear(year);
        seLoinstMonth(month)
        setTag(false)
    }

    const monthChange = (monthChanged) => {
        if (tag) {
            return;
        } else {
            setTag(true)
        }
        var monthAfter = (month + monthChanged)
        const operMonth = (() => {
            if (monthChanged === 1) {
                return (month === 11) ? 0 : monthAfter
            } else {
                return (month === 0) ? 11 : monthAfter
            }
        })()
        const operYear = (() => {
            if (month === 11 && monthChanged === 1) {
                return year + 1
            } else if (month === 0 && monthChanged === -1) {
                return year - 1
            } else {
                return year
            }
        })()
        const now = new Date(`${operYear}`, `${operMonth + 1}`)
        const limitStart =  new Date(`${new Date(start).getFullYear()}`, `${new Date(start).getMonth() + 1}`)
        const limitEnd =  new Date(`${new Date(end).getFullYear()}`, `${new Date(end).getMonth() + 1}`)
        if (now >= limitStart && now <= limitEnd) {
            var date = getDateByYearMonth(operYear, operMonth)
            setCurrentYearMonth(date)
        } else {
            setTag(false)
        }
    }
    const formatNumber = (num) => {
        var _num = num + 1
        return _num < 10 ? `0${_num}` : `${_num}`
    }

    // let monthDays = getCurrentMonthDays(month);
    let weekDay = getWeeksByFirstDay(year, month);

    let _startX = 0;
    return <React.Fragment>
        <View className="loins-calendar"
            onTouchEnd={(val) => {
                if (_startX > val.changedTouches[0]['clientX'] + 30) {
                    monthChange(1);
                }
                if (_startX < val.changedTouches[0]['clientX'] - 30) {
                    monthChange(-1);
                }
            }} 
            onTouchStart={(val) => {
                _startX = val.changedTouches[0]['clientX']
            }}
        >
            <View className="loins-calendar-tabbar">
                <View><AtIcon value='chevron-left' size='25' color='#297AF8' onClick={() => {
                    monthChange(-1);
                }}></AtIcon></View>
                <View className="loins-calendar-title">{year} 年 {formatNumber(month)}月</View>
                <View><AtIcon value='chevron-right' size='25' color='#297AF8' onClick={() => {
                    monthChange(1);
                }}></AtIcon></View>
            </View>
            {
                WEEK_NAMES.map((week, key) => {
                    return <View className="title-c" key={key}>{week}</View>
                })
            }
            {
                LINES.map((l, key) => {
                    return <View key={key} className="day-content">
                        {
                            WEEK_NAMES.map((week, index) => {
                                return getDayText(key, index, weekDay, getCurrentMonthDays(month, year))
                            })
                        }
                    </View>
                })
            }
        </View>
    </React.Fragment>
}
export default Main;
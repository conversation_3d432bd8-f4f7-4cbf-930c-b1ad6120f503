/*
 * @Author: 高超
 * @Date: 2021-12-17 14:44:03
 * @LastEditTime: 2022-07-21 10:47:08
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/tips/index.js
 * love jiajia
 */
import Taro from '@tarojs/taro';
import { AtIcon, AtButton } from 'taro-ui';
import { View } from '@tarojs/components';
import tools from '@/utils/tools'
import './index.scss';


function Tips() {
  const { params:{type} } = Taro.useRouter();
    return (
      <View>

        {(type === 'pay100') ? <View className='tips_wrap'>
          <AtIcon value='check' size='40' color='#5cc323' className='tips_t'></AtIcon>
          <View className='tips_m'>支付成功</View>
          <View className='tips_s'>感谢您的使用，我们将竭诚为您服务</View>
          <View className='tip_btn'>
            <AtButton type='primary' className='tips_btn_bg' onClick={() => {
              Taro.redirectTo({
                url: '/pages/sreach/listbuy',
              });
            }}>再去看看</AtButton>
          </View>
          <View className='tip_btn'>
              <AtButton type='secondary' className='tips_btn_line' onClick={() => {
                Taro.redirectTo({
                  url: '/pages/my/index',
                });
              }}>我的订单</AtButton>
          </View>
        </View> : null}

        {(type === 'pay105') ? <View className='tips_wrap'>
          <AtIcon value='alert-circle' size='60' color='#b80e0e'></AtIcon>
          <View className='tips_m'>支付失败</View>
          <View className='tips_s'>您取消了支付请求～</View>
          <View className='tip_btn'>
          <AtButton type='primary' className='tips_btn_bg' onClick={() => {
              Taro.redirectTo({
                url: '/pages/sreach/listbuy',
              });
            }}>再去看看</AtButton>
          </View>
          <View className='tip_btn'>
          <AtButton type='secondary' className='tips_btn_line' onClick={() => {
                Taro.redirectTo({
                  url: '/pages/my/index',
                });
              }}>我的订单</AtButton>
          </View>
        </View> : null}

        {(type === 'pay106') ? <View className='tips_wrap'>
          <AtIcon value='alert-circle' size='60' color='#b80e0e'></AtIcon>
          <View className='tips_m'>支付失败</View>
          <View className='tips_s'>订单状态错误～您可以拨打客服电话：4006091798</View>
          <View className='tip_btn'>
          <AtButton type='primary' className='tips_btn_bg' onClick={() => {
              Taro.redirectTo({
                url: '/pages/sreach/listbuy',
              });
            }}>再去看看</AtButton>
          </View>
          <View className='tip_btn'>
          <AtButton type='secondary' className='tips_btn_line' onClick={() => {
                Taro.redirectTo({
                  url: '/pages/my/index',
                });
              }}>我的订单</AtButton>
          </View>
        </View> : null}
        
        {(type === '100') ? <View className='tips_wrap'>
          <AtIcon value='check' size='40' color='#5cc323' className='tips_t'></AtIcon>
          <View className='tips_m'>激活成功</View>
          <View className='tips_s'>感谢您的使用，我们将竭诚为您服务</View>
          <View className='tip_btn'>
            <AtButton type='primary' className='tips_btn_bg' onClick={() => {
              Taro.redirectTo({
                url: '/pages/index/index',
              });
            }}>立即预约景区</AtButton>
          </View>
          <View className='tip_btn'>
              <AtButton type='secondary' className='tips_btn_line' onClick={() => {
                Taro.redirectTo({
                  url: '/pages/my/index',
                });
              }}>我的年票</AtButton>
          </View>
        </View> : null}

        {(type === '101') ? <View className='tips_wrap'>
          <AtIcon value='alert-circle' size='60' color='#b80e0e'></AtIcon>
          <View className='tips_m'>暂无可用</View>
          <View className='tips_s'>请购买景区年票后按说明绑定激活卡片</View>
          <View className='tip_btn'>
            <AtButton type='primary' className='tips_btn_bg' onClick={() => {
              Taro.redirectTo({
                url: '/pages/bind/index',
              });
            }}>立即绑卡</AtButton>
          </View>
         {/* <View className='tip_btn'>
              <AtButton type='secondary' className='tips_btn_line' onClick={ async () => {
                await tools.appConfig('buy')
              }}>购买年票</AtButton>
          </View>*/}
        </View> : null}

        {(type === '102') ? <View className='tips_wrap'>
          <AtIcon value='alert-circle' size='60' color='#b80e0e'></AtIcon>
          <View className='tips_m'>程序开小差</View>
          <View className='tips_s'>请您稍后再试</View>
          <View className='tip_btn'>
            <AtButton type='primary' className='tips_btn_bg' onClick={() => {
              Taro.redirectTo({
                url: '/pages/index/index',
              });
            }}>返回首页</AtButton>
          </View>
        </View> : null}

      </View>
    )
}
export default Tips
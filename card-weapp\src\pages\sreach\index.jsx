/*
 * @Author: your name
 * @Date: 2020-12-03 10:42:24
 * @LastEditTime: 2021-12-13 00:27:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /card-server/Users/<USER>/oneCard/card-weapp/src/pages/sreach/index.jsx
 */
import {  useState } from 'react'
import { View, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import SreachTools from './bar'
import tools from '@/utils/tools'
import "./index.scss"

const SreachIndex = () => {
    const [keyWords, setKeyWords] = useState(tools.getDataSafe('keyWords'))
    return (
        <View>
          <SreachTools config={[]}/>
          <View className="sreach_index">
              <Text>搜索历史</Text>
              <View>
                {
                    (keyWords !== null) ? keyWords.map(v => <Text className="sreach_tags" onClick={() => {
                      Taro.navigateTo({
                        url : `/pages/sreach/list?v=${v}`
                      })
                    }}>#{v}</Text>) : <View style={{paddingTop: '12px', color: '#a9a6a6'}}>暂无搜索记录</View>
                }
              </View>
          </View>
        </View>
    )
}
export default SreachIndex 